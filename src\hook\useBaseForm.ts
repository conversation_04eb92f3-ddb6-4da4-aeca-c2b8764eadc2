import type { FormInstance } from 'ant-design-vue'
import BaseForm, { type BaseFormItem, type BaseFormProps } from '@/components/BaseForm'

// eslint-disable-next-line no-unused-vars
type BaseFormResult = [Component, Ref<FormInstance | undefined>, (key: string, option: any) => void]
/**
 * 基础表单
 * @param formProps 表单配置
 * @returns 表单组件和表单实例
 */
export const useBaseForm = (formProps: BaseFormProps): BaseFormResult => {
  const formRef = ref<FormInstance>()

  const Component = () =>
    h(
      BaseForm,
      {
        formRef,
        isText: formProps.isText,
        ...formProps,
      },
      {},
    )

  /**
   * @description 设置选项
   * @param key 选项key
   * @param option 选项
   */
  const setOption = (key: string, option: any) => {
    const item = formProps.formConfig.value.find((item) => item.type !== 'title' && item.key === key)
    if (item && (item.type === 'select' || item.type === 'cascader')) {
      item.props!.options = option
    }
  }

  return [Component, formRef, setOption]
}

export type { BaseFormItem, BaseFormProps }
