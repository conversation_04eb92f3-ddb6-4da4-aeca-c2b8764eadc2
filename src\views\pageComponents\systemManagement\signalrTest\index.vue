<template>
  <div class="signalr-test-container">
    <a-card title="SignalR 导出通知测试页面" class="test-card">
      <div class="container">
        <!-- 连接配置面板 -->
        <a-card title="连接配置" class="panel">
          <a-form layout="vertical">
            <a-form-item label="服务器地址">
              <a-input v-model:value="config.serverUrl" placeholder="http://localhost:8083/backendhub" />
            </a-form-item>
            <a-form-item label="用户ID">
              <a-input v-model:value="config.userId" placeholder="用户ID" />
            </a-form-item>
            <a-form-item label="用户Token">
              <a-input v-model:value="config.userToken" placeholder="用户Token" />
            </a-form-item>
          </a-form>

          <div :class="['status', connectionStatus.type]" class="connection-status">
            {{ connectionStatus.message }}
          </div>

          <a-space>
            <a-button type="primary" @click="connectSignalR" :disabled="isConnected" :loading="isConnecting">连接</a-button>
            <a-button @click="disconnectSignalR" :disabled="!isConnected">断开连接</a-button>
            <a-button @click="testConnection">测试连接</a-button>
            <a-button @click="loadUserData">加载用户数据</a-button>
          </a-space>
        </a-card>

        <!-- 消息测试面板 -->
        <a-card title="消息测试" class="panel">
          <a-space wrap>
            <a-button @click="simulateProgress">模拟进度消息</a-button>
            <a-button @click="simulateComplete">模拟完成消息</a-button>
            <a-button @click="simulateFailed">模拟失败消息</a-button>
            <a-button type="primary" @click="testRealNotification">测试真实通知(带下载)</a-button>
            <a-button @click="testBackendFormat">模拟后端格式(无下载)</a-button>
            <a-button danger @click="clearMessages">清空消息</a-button>
          </a-space>

          <div class="messages-section">
            <h4>接收到的消息:</h4>
            <div class="messages-list">
              <div v-for="(message, index) in messages" :key="index" :class="['message', message.type]">
                <div class="message-header">
                  <strong>[{{ message.timestamp }}] {{ message.title }}</strong>
                </div>
                <div class="message-content">{{ message.content }}</div>
                <div v-if="message.data" class="message-data">数据: {{ JSON.stringify(message.data, null, 2) }}</div>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 日志面板 -->
      <a-card title="连接日志" class="logs-panel">
        <a-button danger @click="clearLogs" style="margin-bottom: 10px">清空日志</a-button>
        <div class="logs-container">
          <div v-for="(log, index) in logs" :key="index" :class="['log-entry', log.type]">[{{ log.timestamp }}] {{ log.message }}</div>
        </div>
      </a-card>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import * as signalR from '@microsoft/signalr'
import { showExportNotification } from '@/utils/exportNotification'

// 响应式数据
const connection = ref<signalR.HubConnection | null>(null)
const isConnected = ref(false)
const isConnecting = ref(false)

const config = reactive({
  serverUrl: 'http://localhost:8083/backendhub',
  userId: '7343700607372365833',
  userToken: 'c499e3134c4f42a0b690698796404cff',
})

const connectionStatus = reactive({
  type: 'disconnected',
  message: '未连接',
})

const messages = ref<
  Array<{
    timestamp: string
    type: string
    title: string
    content: string
    data?: any
  }>
>([])

const logs = ref<
  Array<{
    timestamp: string
    type: string
    message: string
  }>
>([])

// 工具函数
const addLog = (message: string, type: string = 'info') => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push({ timestamp, type, message })
  console.log(`[${timestamp}] ${message}`)
}

const updateConnectionStatus = (type: string, msg: string) => {
  connectionStatus.type = type
  connectionStatus.message = msg

  if (type === 'connected') {
    isConnected.value = true
    isConnecting.value = false
  } else {
    isConnected.value = false
    isConnecting.value = false
  }
}

const addMessage = (type: string, title: string, content: string, data?: any) => {
  const timestamp = new Date().toLocaleTimeString()
  messages.value.push({ timestamp, type, title, content, data })
}

// 加载用户数据
const loadUserData = () => {
  try {
    const userDataStr = localStorage.getItem('userData')
    if (userDataStr) {
      const userData = JSON.parse(userDataStr)
      config.userId = userData.id || config.userId
      config.userToken = userData.login_token || config.userToken

      // 使用环境变量中的API地址
      const baseApi = import.meta.env.VITE_APP_BASE_API
      if (baseApi) {
        config.serverUrl = `${baseApi}/backendhub`
      }

      addLog('已加载用户数据', 'success')
      message.success('用户数据加载成功')
    } else {
      addLog('未找到用户数据', 'error')
      message.warning('未找到用户数据，请先登录')
    }
  } catch (error) {
    addLog('加载用户数据失败: ' + error, 'error')
    message.error('加载用户数据失败')
  }
}

// 注册消息处理器
const registerMessageHandlers = () => {
  if (!connection.value) return
  // 导出任务进行中
  connection.value.on('DownloadTaskCreated', (data: any) => {
    addLog('收到导出任务进行中DownloadTaskCreated消息', 'success')
    // addMessage('progress', '导出任务进行中', data.message || '正在处理导出请求...', data)
  })
  // 导出任务进行中
  connection.value.on('DownloadTaskInProgress', (data: any) => {
    addLog('收到导出任务进行中DownloadTaskInProgress消息', 'success')
    // addMessage('progress', '导出任务进行中', data.message || '正在处理导出请求...', data)
  })

  // 导出任务完成
  connection.value.on('DownloadTaskComplete', (data: any) => {
    addLog('收到导出任务完成DownloadTaskComplete消息', 'success')
    addLog(`数据详情: ${JSON.stringify(data)}`, 'info')
    addLog(`file_id: ${data.file_id}, file_name: ${data.file_name}, downloadUrl: ${data.downloadUrl}`, 'info')
    addMessage('complete', '导出任务完成', data.message || '导出文件已准备完成', data)

    // 同时调用真实的exportNotification来测试
    showExportNotification({
      type: 'complete',
      title: data.title || '导出文件准备完成',
      message: data.message || '导出文件已经准备完成，文件下载链接将于24小时后失效，请尽快下载。',
      taskId: data.taskId || data.id,
      fileId: data.file_id, // 使用后端字段名
      fileName: data.file_name, // 使用后端字段名
      downloadUrl: data.downloadUrl,
      duration: 10
    })
  })

  // 导出任务失败
  connection.value.on('DownloadTaskFailed', (data: any) => {
    addLog('收到导出任务失败DownloadTaskFailed消息', 'success')
    // addMessage('failed', '导出任务失败', data.message || '导出过程中发生错误', data)
  })

  // 权限相关消息
  connection.value.on('ReceiveRolePermission', (data: any) => {
    addLog('收到角色权限已更新ReceiveRolePermission消息', 'success')
    addMessage('progress', '权限更新', '角色权限已更新', data)
  })

  connection.value.on('ReceiveRoleChange', (data: any) => {
    addLog('收到用户角色已变更ReceiveRoleChange消息', 'success')
    addMessage('progress', '角色变更', '用户角色已变更', data)
  })

  connection.value.on('ReceiveConnectionError', (data: any) => {
    addLog('收到连接错误ReceiveConnectionError消息', 'error')
    addMessage('failed', '连接错误', '连接发生错误', data)
  })

  addLog('消息监听器注册完成', 'success')
}

// 连接SignalR
const connectSignalR = async () => {
  if (!config.serverUrl || !config.userId || !config.userToken) {
    message.error('请填写完整的连接信息')
    return
  }

  const hubUrl = `${config.serverUrl}?userId=${config.userId}&userToken=${config.userToken}`
  addLog(`尝试连接到: ${hubUrl}`)

  try {
    isConnecting.value = true
    updateConnectionStatus('connecting', '连接中...')

    // 创建连接（使用与系统相同的配置）
    connection.value = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          if (retryContext.elapsedMilliseconds < 60000) {
            return Math.random() * 5000
          }
          return 60000
        },
      })
      .configureLogging(signalR.LogLevel.Debug)
      .build()

    // 连接事件监听
    connection.value.onclose((error) => {
      updateConnectionStatus('disconnected', '连接已断开')
      addLog('连接已断开: ' + (error ? error.toString() : '正常断开'), 'error')
    })

    connection.value.onreconnecting((error) => {
      updateConnectionStatus('connecting', '重新连接中...')
      addLog('正在重新连接: ' + (error ? error.toString() : ''), 'info')
    })

    connection.value.onreconnected((connectionId) => {
      updateConnectionStatus('connected', '重新连接成功')
      addLog('重新连接成功，连接ID: ' + connectionId, 'success')
      registerMessageHandlers() // 重连后重新注册监听器
    })

    // 启动连接
    await connection.value.start()
    updateConnectionStatus('connected', '已连接')
    addLog('SignalR连接成功建立', 'success')
    message.success('SignalR连接成功')

    // 注册消息监听器
    registerMessageHandlers()
  } catch (error) {
    updateConnectionStatus('disconnected', '连接失败')
    addLog('连接失败: ' + error, 'error')
    message.error('连接失败: ' + error)
    console.error('连接失败:', error)
  }
}

// 断开连接
const disconnectSignalR = async () => {
  if (connection.value) {
    try {
      await connection.value.stop()
      addLog('主动断开连接', 'info')
      message.info('连接已断开')
    } catch (error) {
      addLog('断开连接时出错: ' + error, 'error')
    }
    connection.value = null
  }
}

// 测试连接
const testConnection = () => {
  if (!connection.value || !isConnected.value) {
    addLog('连接未建立，无法测试', 'error')
    message.error('连接未建立')
    return
  }

  addLog('连接状态: ' + connection.value.state, 'info')
  addLog('连接ID: ' + (connection.value.connectionId || '未知'), 'info')
  message.info('连接测试完成，请查看日志')
}

// 模拟消息
const simulateProgress = () => {
  const data = {
    title: '导出文件准备中',
    message: '正在处理您的导出请求，请稍候...',
    taskId: 'test_task_' + Date.now(),
  }
  addMessage('progress', data.title, data.message, data)
}

const simulateComplete = () => {
  const data = {
    title: '导出文件准备完成',
    message: '导出文件已经准备完成，可以下载了',
    taskId: 'test_task_' + Date.now(),
    fileId: '12345',
    fileName: 'test_export.xlsx',
  }
  addMessage('complete', data.title, data.message, data)
}

const simulateFailed = () => {
  const data = {
    title: '导出文件失败',
    message: '导出过程中发生错误，请重试',
    taskId: 'test_task_' + Date.now(),
  }
  addMessage('failed', data.title, data.message, data)
}

// 测试真实的导出通知（带下载按钮）
const testRealNotification = () => {
  // 使用真实的exportNotification函数，这样可以测试下载按钮
  showExportNotification({
    type: 'complete',
    title: '导出文件准备完成',
    message: '导出文件已经准备完成，文件下载链接将于24小时后失效，请尽快下载。',
    taskId: 'real_test_' + Date.now(),
    fileId: '12345',
    fileName: 'test_export.xlsx',
    duration: 10 // 10秒后自动关闭
  })

  addLog('使用真实exportNotification显示带下载按钮的通知', 'info')
  message.info('已显示真实的导出完成通知，应该包含下载按钮')
}

// 模拟后端数据格式（可能没有下载信息）
const testBackendFormat = () => {
  // 模拟后端可能发送的数据格式（没有fileId和downloadUrl）
  const backendData = {
    title: '导出文件准备完成',
    message: '导出文件已经准备完成',
    taskId: 'backend_test_' + Date.now(),
    // 注意：没有 fileId, fileName, downloadUrl
  }

  addLog(`模拟后端数据格式: ${JSON.stringify(backendData)}`, 'info')

  showExportNotification({
    type: 'complete',
    title: backendData.title,
    message: backendData.message,
    taskId: backendData.taskId,
    fileId: undefined, // 模拟后端没有发送这些字段
    fileName: undefined,
    downloadUrl: undefined,
    duration: 10
  })

  addLog('使用后端格式数据调用exportNotification', 'info')
  message.warning('已显示后端格式的通知，应该没有下载按钮')
}



// 清空函数
const clearMessages = () => {
  messages.value = []
}

const clearLogs = () => {
  logs.value = []
}

// 生命周期
onMounted(() => {
  addLog('页面加载完成，准备测试SignalR连接', 'info')
  // 自动加载用户数据
  loadUserData()
})

onUnmounted(() => {
  if (connection.value && isConnected.value) {
    connection.value.stop()
  }
})
</script>

<style scoped>
.signalr-test-container {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.panel {
  height: fit-content;
}

.connection-status {
  padding: 10px;
  margin: 10px 0;
  border-radius: 4px;
  font-weight: bold;
  text-align: center;
}

.connection-status.connected {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.connection-status.disconnected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.connection-status.connecting {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.messages-section {
  margin-top: 20px;
}

.messages-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 10px;
}

.message {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin: 5px 0;
  font-size: 14px;
}

.message.progress {
  border-left: 4px solid #1890ff;
}

.message.complete {
  border-left: 4px solid #52c41a;
}

.message.failed {
  border-left: 4px solid #ff4d4f;
}

.message-header {
  margin-bottom: 5px;
}

.message-content {
  margin-bottom: 5px;
  color: #666;
}

.message-data {
  font-size: 12px;
  color: #999;
  background: #f5f5f5;
  padding: 5px;
  border-radius: 2px;
  white-space: pre-wrap;
}

.logs-panel {
  margin-top: 20px;
}

.logs-container {
  height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  margin: 2px 0;
}

.log-entry.error {
  color: #ff4d4f;
}

.log-entry.success {
  color: #52c41a;
}

.log-entry.info {
  color: #1890ff;
}

@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
  }
}
</style>
