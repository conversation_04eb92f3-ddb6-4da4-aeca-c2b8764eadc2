<template>
  <a-drawer width="700" @close="handleClose" v-model:open="openDrawer" :maskClosable="false" destroyOnClose>
    <template #title>
      <div class="flex justify-between h-25px">
        <div class="text-15px">审核资质/证书</div>
        <div class="flex gap-10px">
          <a-button @click="handleClose">关闭</a-button>
          <a-button @click="handleRefuse(certificateId, 'refuse')" v-if="approvalStatus === 0">拒绝</a-button>
          <a-button type="primary" @click="handlePass(certificateId, 'pass')" v-if="approvalStatus === 0">通过</a-button>
          <a-button @click="handleRecord()">审核记录</a-button>
        </div>
      </div>
    </template>
    <a-form :model="certificate" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }" layout="vertical">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="资质/证书">
            <span>{{ certificate.certificate_name }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="生产商">
            <span>{{ certificate.manufacturer_name }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="资质有效期">
            <span>
              {{ certificate?.validity_period_start_time ? certificate.validity_period_start_time.split(' ')[0] : '' }}
              至
              {{ certificate?.validity_period_end_time ? certificate.validity_period_end_time.split(' ')[0] : '' }}
            </span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="过期状态">
            <span>{{ certificate.expired_status_str }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="上传时间">
            <span>{{ certificate.modified_at }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="所属供应商">
            <span>{{ certificate.supplier_name }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="供应商编码">
            <span>{{ certificate.supplier_code }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="资质/证书文件" :wrapper-col="{ span: 24 }">
            <div class="flex gap-10px flex-wrap">
              <div v-for="file in certificate.file_infos" :key="file.id" class="w-100 flex flex-col relative" @click.stop>
                <img :src="file.url" class="w-100 h-60" loading="lazy" />
                <div class="file-card-mask">
                  <DownloadOutlined class="absolute top-4 right-4 c-#fff" @click.stop="handleDownload(file.id, file.original_name)" />
                  <div class="absolute top-50% left-50% -translate-x-1/2 -translate-y-1/2 c-#fff cursor-pointer" @click.stop="handlePreview(file)">
                    <EyeOutlined />
                  </div>
                  <LoadingOutlined class="c-primary absolute top-50% left-50% -translate-x-1/2 -translate-y-1/2" v-show="file.status === 'uploading'" />
                </div>
                <div class="flex mt-4">
                  <span class="ml-4 hover:c-primary truncate">{{ file.original_name }}</span>
                </div>
              </div>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <AuditModal ref="auditRef" @refreshList="refreshList" @handleClose="handleClose" />
    <AuditRecords ref="auditRecordsRef" />
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import * as pdfjsLib from 'pdfjs-dist/build/pdf'
import { DownloadOutlined, EyeOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import { GetCertificateDetail } from '@/servers/Certificate'
import { ViewByFileIdCommon } from '@/servers/Common'
import AuditModal from './AuditModal.vue'
import AuditRecords from './AuditRecords.vue'

pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
const emit = defineEmits(['search'])
// 资质/证书数据
const certificate = ref<any>({})
// 审核通过或拒绝ref
const auditRef = ref()
// 审核记录ref
const auditRecordsRef = ref()
// 资质/证书id
const certificateId = ref<number>(0)
// 审核状态
const approvalStatus = ref<number>(0)

// 抽屉是否打开
const openDrawer = ref(false)
// 显示抽屉
const showDrawer = async (id: number, approval_status: number) => {
  openDrawer.value = true
  certificateId.value = id
  approvalStatus.value = approval_status
  await getCertificateDetail()
  const urls: any = await getFileStream()
  certificate.value.file_infos.map((item: any, index: number) => {
    item.url = urls[index].coverUrl // 用于 img src
    item.originUrl = urls[index].originUrl // 用于预览
    return item
  })
}
// 刷新列表
const refreshList = () => {
  emit('search')
}
// 关闭抽屉
const handleClose = () => {
  openDrawer.value = false
}
// 拒绝
const handleRefuse = (id: number, type: string) => {
  auditRef.value.showModal(id, type)
}
// 通过
const handlePass = (id: number, type: string) => {
  auditRef.value.showModal(id, type)
}
// 审核记录
const handleRecord = () => {
  auditRecordsRef.value.open(certificateId.value)
}
// 获取资质/证书详情
const getCertificateDetail = async () => {
  const parms = {
    id: certificateId.value,
  }
  const res = await GetCertificateDetail(parms)
  console.log('res', res)
  certificate.value = res.data
}
// 获取文件流
const getFileStream = async (): Promise<string[]> => {
  const ids = (certificate.value.file_ids || '').split(',').filter(Boolean)
  const urls = await Promise.all(
    ids.map(async (id, idx) => {
      const res = await ViewByFileIdCommon(id)
      const fileInfo = certificate.value.file_infos[idx]
      let coverUrl
      let originUrl
      if (fileInfo && fileInfo.original_name && fileInfo.original_name.toLowerCase().endsWith('.pdf')) {
        // PDF，生成封面
        coverUrl = await getPdfFirstPageAsImage(res.data)
        originUrl = URL.createObjectURL(res.data)
      } else {
        // 其它类型，直接用 blob url
        coverUrl = URL.createObjectURL(res.data)
        originUrl = coverUrl
      }
      return { coverUrl, originUrl }
    }),
  )
  return urls
}
// 预览
const handlePreview = (item: any) => {
  window.open(item.originUrl, '_blank')
}

// 下载授权书文件
const handleDownload = async (fileId: number, auth_file_original_name: string) => {
  if (!fileId) {
    console.warn('缺少文件信息')
    return
  }

  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建下载URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/DownloadFile' : '/api/Files/DownloadFile'

    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        logintoken: loginToken,
      },
      body: JSON.stringify({
        file_id: fileId,
        original_name: auth_file_original_name,
        data_source: 'SRS',
      }),
    })

    if (!response.ok) {
      console.error('下载请求失败:', response.status)
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 获取文件blob
    const blob = await response.blob()

    // 创建下载链接
    const downloadUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = auth_file_original_name
    link.style.display = 'none'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(downloadUrl), 1000)
  } catch (error) {
    console.error('文件下载失败:', error)
  }
}
async function getPdfFirstPageAsImage(pdfBlob) {
  const arrayBuffer = await pdfBlob.arrayBuffer()
  const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
  const page = await pdf.getPage(1)
  const viewport = page.getViewport({ scale: 1 })
  const canvas = document.createElement('canvas')
  canvas.width = viewport.width
  canvas.height = viewport.height
  const context = canvas.getContext('2d')
  await page.render({ canvasContext: context, viewport }).promise
  return canvas.toDataURL('image/png')
}
defineExpose({ showDrawer })
</script>

<style lang="scss" scoped>
.file-card-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background-color: rgb(0 0 0 / 45%);
  opacity: 0;
  transition: all 0.3s;

  &:hover {
    opacity: 1;
  }
}
</style>
