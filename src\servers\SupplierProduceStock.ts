import { request } from './request'

export const GetList = (data: any) => {
  return request({ url: '/api/ProductInfo/GetList', data }, 'POST')
}
// 商品类目选项
export const GetCategoryOption = () => request({ url: '/plm/Category/GetCategorySelectOption', data: { type: 1 } }, 'POST')
// 导出
export const ExportProduct = (data: any) => request({ url: '/api/ProductInfo/Export', data, responseType: 'blob' })
// 通过id获取单个商品信息
export const GetProduct = (data) => request({ url: '/api/ProductInfo/Get', data }, 'GET')

// 商品类目模板
export const GetCategoryTemplate = (data) => request({ url: '/plm/CategoryTemplate/GetCategoryTemplateDetail', data }, 'POST')

// 获取计量单位选项
export const GetMeteringUnitSelectOption = () => request({ url: '/plm/CategoryTemplate/GetMeteringUnitSelectOption' }, 'POST')

// 获取红点数字
export const GetLabelStatusCount = () => request({ url: '/api/ProductInfo/GetLabelStatusCount' }, 'GET')

// 驳回商品选择
export const OverruleSelection = (data: { id: number; status: number; is_audit: boolean; remake: string }) => request({ url: '/api/ProductInfo/OverruleSelection', data }, 'POST')

// 拒绝商品选择
export const RefuseSelection = (data: { id: number; status: number; is_audit: boolean; remake: string }) => request({ url: '/api/ProductInfo/RefuseSelection', data }, 'POST')

// 提交审核
export const SubmitAudit = (data: {
  id: number
  selection_notes: string
  first_batch_quantity: number
  shipping_fee: number
  shipment_time: string
  expected_delivery_date: string
  agreed_purchase_price: number
  agreed_purchase_tax_rate: number
  agreed_purchase_tax_price: number
  store_supply_price: number
}) => request({ url: '/api/ProductInfo/SubmitAudit', data }, 'POST')

export const DownloadImagesZip = (data) => request({ url: `/api/ProductInfo/DownloadImagesZip`, data, responseType: 'blob' }, 'POST')
