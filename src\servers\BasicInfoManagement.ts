// 供应商入驻审核
import { request, requestXY } from './request'
// /api/Language/GetLanguageList

// 获取语言信息列表
export const GetLanguageList = (data) => {
  return request({ url: '/api/Language/GetList', data }, 'POST')
}
// 获取语言信息详情/api/Language/Get
export const GetLanguageDetail = (data) => {
  return request({ url: '/api/Language/Get', data }, 'GET')
}
// 语言信息批量启用停用api/Language/BatchUpdateStatus
export const GetLanguageBatchUpdateStatus = (data) => {
  return request({ url: '/api/Language/BatchUpdateStatus', data }, 'POST')
}
// 获取货币信息列表
export const GetCurrencyList = (data) => {
  return request({ url: '/api/Currency/GetList', data }, 'POST')
}

// 获取货币信息详情/api/Currency/Get
export const GetCurrencyDetail = (data) => {
  return request({ url: '/api/Currency/Get', data }, 'GET')
}
// 货币信息批量启用停用api/Language/BatchUpdateStatus
export const GetCurrencyUpdateStatus = (data) => {
  return request({ url: '/api/Currency/BatchUpdateStatus', data }, 'POST')
}
// 获取国家区域信息列表
export const GetCountryRegionList = (data) => {
  return request({ url: '/api/CountryRegion/GetList', data }, 'POST')
}

// 获取国家区域信息详情/api/Currency/Get
export const GetCountryRegionDetail = (data) => {
  return request({ url: '/api/CountryRegion/Get', data }, 'GET')
}
// 国家区域批量启用停用api/Language/BatchUpdateStatus
export const GetCountryRegionUpdateStatus = (data) => {
  return request({ url: '/api/CountryRegion/BatchUpdateStatus', data }, 'POST')
}
// /api/Common/GetDropdownItems
export const GetDropdownItems = () => {
  return request({ url: '/api/Common/GetDropdownItems' }, 'GET')
}
// /XY/Common/GetOpLogInfos
export const GetOpLogInfos = (data) => {
  return requestXY({ url: '/Common/GetOpLogInfos', data }, 'POST')
}
