<template>
  <a-drawer v-if="open" v-model:open="open" width="65%" class="custom-class" root-class-name="drawerrootclass" :bodyStyle="{ padding: '0px' }" :title="title" placement="right" @close="closeDrawer">
    <template #extra>
      <a-button @click="changeLogVisible">日志</a-button>
    </template>
    <div class="personalInfoBox">
      <div class="PI_Box" v-for="(item, index) in tableInfo" :key="index">
        <div class="PI_Box_Title" v-if="item.title != null">
          {{ item.title }}
          <span class="PI_Box_SubTitle">{{ item.subtitle }}</span>
        </div>
        <div class="PI_Box_Items">
          <template v-for="(titem, tindex) in item.tableItem" :key="tindex">
            <div v-if="titem.ishide != true" class="PI_Box_Item" :style="{ width: titem.width + '%' }">
              <div v-if="titem.type != 'table'" class="PI_Box_Item_label" :style="{ width: titem.labelwidth + 'px' }">{{ titem.label }}：</div>
              <div v-if="titem.type == 'text'" class="PI_Box_Item_content">
                {{ tableobj[titem.key] == 'null' ? '' : tableobj[titem.key] }}
              </div>
              <div v-if="titem.type == 'xsgstext'" class="PI_Box_Item_content">--</div>
              <div v-if="titem.type == 'namanddepartmenttext'" class="PI_Box_Item_content">
                {{ tableobj[titem.key] == 'null' ? '' : tableobj[titem.key] }}
                <span v-if="tableobj[titem.key3] != null || tableobj[titem.key2] != null">(</span>
                <span v-if="tableobj[titem.key2] != null">{{ tableobj[titem.key2] }}</span>
                <span v-if="tableobj[titem.key3] != null && tableobj[titem.key2] != null">|</span>
                <span v-if="tableobj[titem.key3] != null">{{ tableobj[titem.key3] }}</span>
                <span v-if="tableobj[titem.key3] != null || tableobj[titem.key2] != null">)</span>
              </div>
              <div v-if="titem.type == 'textoptions'" class="PI_Box_Item_content">
                <template v-for="(otiem, oindex) in titem.options" :key="oindex">
                  <template v-if="otiem.value == tableobj[titem.key]">
                    {{ otiem.label }}
                  </template>
                </template>
              </div>
              <div v-if="titem.type == 'imgbox'" class="PI_Box_Item_content">
                <a-image :width="60" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" />
              </div>
              <vxe-table border class="tableClass" v-if="titem.type == 'table'" :data="tableobj[titem.key]">
                <template v-for="(ttitem, ttindex) in titem.tableInfo" :key="ttindex">
                  <vxe-column v-if="ttitem.ishide != true" :width="ttitem.width || 'auto'" align="center">
                    <template #header>
                      <span v-if="ttitem.ismust" style="color: red">*</span>
                      <span class="thClass">{{ ttitem.title }}</span>
                    </template>
                    <template v-if="ttitem.type == 'ID'" #default="{ $rowIndex }">
                      <div>{{ $rowIndex + 1 }}</div>
                    </template>
                    <template v-if="ttitem.type == 'text'" #default="{ row }">
                      <div>{{ row[ttitem.fieldkey] }}</div>
                    </template>
                    <template v-if="ttitem.type == 'imgbox'" #default="{}">
                      <a-image :width="40" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" />
                    </template>
                    <template v-if="ttitem.type == 'textoptions'" #default="{ row }">
                      <template v-for="(otiem, oindex) in ttitem.options" :key="oindex">
                        <template v-if="otiem.value == row[ttitem.fieldkey]">
                          {{ otiem.label }}
                        </template>
                      </template>
                    </template>
                  </vxe-column>
                </template>
              </vxe-table>
            </div>
          </template>
        </div>
      </div>
    </div>
  </a-drawer>
  <logModel ref="logModelRef"></logModel>
</template>
<script setup lang="ts">
import { GetOpLogInfos } from '@/servers/BasicInfoManagement'
import { message } from 'ant-design-vue'
import logModel from './logModel.vue'

const open = ref(false)
const title = ref('')
const type = ref('')
const logModelRef = ref()
onMounted(() => {})
const tableInfo = ref()
const tableobj = ref()

const init = (tableItems, value, titletext, typetext) => {
  open.value = true
  tableInfo.value = tableItems
  tableobj.value = JSON.parse(JSON.stringify(value))
  title.value = titletext
  type.value = typetext
}
const closeDrawer = () => {}
const changeLogVisible = async () => {
  const data: any = []
  let title = ''
  let page = 0
  let opt = ''
  if (type.value == 'Country_Region') {
    title = '国家/地区日志'
    page = 52
    opt = '了此国家/地区'
  }
  if (type.value == 'Currency') {
    title = '货币日志'
    page = 51
    opt = '了此货币'
  }
  // if (type.value == 'ExchangeRate') {
  //   title = '汇率日志'
  // }
  if (type.value == 'LanguageTB') {
    title = '语言日志'
    page = 50
    opt = '了此语言'
  }
  const params = {
    id: tableobj.value.id,
    page,
  }
  const res = await GetOpLogInfos(params)
  if (res.success == true) {
    res.data.forEach((item) => {
      const obj: any = {
        time: item.op_at,
        user: item.user_name,
        title: `[${item.user_department}]`,
        subtitle: item.edits[0].new_val + opt,
      }
      data.push(obj)
    })

    logModelRef.value.init(data, title)
  } else {
    message.warning(res.message)
  }
}
defineExpose({ init })
</script>
<style lang="scss" scoped>
.personalInfoBox {
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: calc(100% - 60px);
  padding: 24px;
  overflow-y: auto;
  color: #000;

  .PI_Box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    font-size: 14px;

    .PI_Box_Title {
      display: flex;
      align-items: center;
      width: 100%;
      height: 36px;
      padding-left: 12px;
      color: #111;
      background: #f4f7fe;

      .PI_Box_SubTitle {
        margin-top: 5px;
        margin-left: 12px;
        font-size: 10px;
        color: #9ca3af;
      }
    }

    .PI_Box_Items {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100%;

      .PI_Box_Item {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        width: 100%;
        min-height: 28px;
        margin-top: 15px;
        margin-bottom: 5px;
        font-size: 12px;
        color: #000;

        .PI_Box_Item_label {
          color: #666;
          text-align: right;
        }

        .PI_Box_Item_content {
          width: 80%;
          text-align: left;
          word-wrap: break-word;

          .PI_Box_Item_link {
            color: blue;
            cursor: pointer;
          }
        }

        .tableClass {
          width: 100%;
          margin-top: 0;
          margin-bottom: 20px;
          font-size: 12px;
          color: #000;

          .thClass {
            font-size: 12px;
          }

          .PI_Box_Item_link {
            color: blue;
            cursor: pointer;
          }

          .colorgrey {
            font-size: 12px;
            color: #b4b6b9;
          }
        }
      }
    }
  }
}
</style>
