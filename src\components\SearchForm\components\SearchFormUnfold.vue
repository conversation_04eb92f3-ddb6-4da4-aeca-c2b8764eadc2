<template>
  <div class="unfold-box">
    <div class="unfold">
      <div class="lin1"></div>
      <a-button class="unfold-btn" size="small" type="primary" @click="handleChangeShow">
        <span>{{ show ? '收起' : '展开' }}</span>
      </a-button>
      <div class="lin2"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
const show = defineModel('show', { required: true })

const handleChangeShow = () => {
  show.value = !show.value
}
</script>

<style scoped lang="scss">
.unfold {
  display: flex;
  align-items: center;
  width: 100%;

  .lin1 {
    flex: 1;
    height: 2px;
    background: linear-gradient(-90deg, #3d7fff, #f7f8fa);
    border-top-left-radius: 50%;
    border-bottom-left-radius: 50%;
  }

  .lin2 {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, #3d7fff, #f7f8fa);
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
  }

  .unfold-btn {
    display: flex;
    justify-content: center;
    width: 40px;
    height: 20px;
    line-height: 10px !important;
    font-size: 12px;
  }
}
.unfold-box {
  position: absolute;
  right: 0;
  bottom: -10px;
  left: 0;
  display: flex;
  align-items: center;
  height: 21px;
}
</style>
