<template>
  <div class="copy-btn">
    <a-button type="primary" ghost class="!bg-white" :icon="h(CopyOutlined)"></a-button>
  </div>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { CopyOutlined } from '@ant-design/icons-vue'
</script>

<style scoped lang="scss">
.copy-btn {
  position: absolute;
  top: 0;
  right: 8px;
  display: none;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 100%;
  transition: opacity 0.3s;

  .ant-btn {
    padding: 0;
    border: none !important;
  }
}
</style>
