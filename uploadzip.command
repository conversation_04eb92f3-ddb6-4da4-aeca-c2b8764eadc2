#!/bin/bash
# 压缩dist
# zip -r dist.zip dist
# 设置要上传的文件路径（使用与脚本同一目录的相对路径）
filePath="$(dirname "$0")/dist/dist.zip"

# 设置目标 URL
url="http://**************:1880/srsmgupload"

# 检查文件是否存在
if [ ! -f "$filePath" ]; then
    echo "File \"$filePath\" not found!"
    exit 1
fi

# # 提示用户输入 token
# read -p "Please enter the token: " token

# 使用 curl 上传文件和 token
response=$(curl -X POST -F "file=@$filePath" -F "token=zKF2AvT8gOrGWn3suMgtKud3UXzYseJY" "$url")

# 检查 curl 的返回值
if [ $? -ne 0 ]; then
    echo "Upload failed!"
    exit 1
fi

echo "Upload successful!"

# 删除压缩包
rm -f ./dist
