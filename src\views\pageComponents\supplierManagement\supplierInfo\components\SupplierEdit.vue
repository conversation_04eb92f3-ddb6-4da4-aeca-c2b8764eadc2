<template>
  <a-drawer width="1000" @close="handleClose" v-model:open="openDrawer" :maskClosable="false">
    <template #title>编辑供应商</template>
    <a-form ref="formRef" :model="ExamineInfo">
      <div class="drawer-title">基本信息</div>
      <a-form :model="ExamineInfo" :label-col="{ style: { width: '120px' } }" :wrapper-col="{ span: 17 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="供应商编码">
              <span>{{ ExamineInfo.number }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件编号">
              <span>{{ ExamineInfo.credit_id }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="统一社会信用代码">
              <span>
                {{ ExamineInfo.credit_code }}
                <span>可在企信网上查询，</span>
                <a-button type="link" @click="openEnterpriseNet" class="p-0">去查询</a-button>
              </span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="供应商名称">
              <span>{{ ExamineInfo.supplier_name }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="营业执照有效期">
              <span v-if="ExamineInfo.is_long">长期</span>
              <span v-else>{{ ExamineInfo.business_license_validity?.slice(0, 10) }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="公司类型">
              <span>{{ ExamineInfo.company_type_string }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="成立日期">
              <span>{{ ExamineInfo.establishment_date?.slice(0, 10) }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="法人">
              <span>{{ ExamineInfo.legal_person_name }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="经营规模">
              <span>{{ ExamineInfo.business_scale_string }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件类型">
              <span>{{ ExamineInfo.certificate_type_string }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="营业执照">
              <a-button type="link" class="p-0" @click="certificatePreview(ExamineInfo.business_license_file_id)">预览</a-button>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="证件号">
              <span>{{ ExamineInfo.certificate_number }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="法人证件">
              <a-button type="link" class="p-0" @click="certificatePreview([ExamineInfo.id_card_front_file_id, ExamineInfo.id_card_back_file_id])">预览</a-button>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="营业执照地址">
              <span>{{ ExamineInfo.business_license_province + ExamineInfo.business_license_city + ExamineInfo.business_license_area + ExamineInfo.business_license_address }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="16">
            <a-form-item label="办公地址">
              <span>{{ ExamineInfo.officeAddress }}</span>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="drawer-title">公司扩展信息</div>
      <a-form :model="ExamineInfo" :label-col="{ style: { width: '120px' } }" :wrapper-col="{ span: 17 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="供应商类型">
              <span>{{ ExamineInfo.supplier_type_string }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="主营类目">
              <span v-for="(item, index) in ExamineInfo.main_categories_strings" :key="item">{{ item }}{{ index !== ExamineInfo.main_categories_strings.length - 1 ? '，' : '' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="自营/合作工厂规模">
              <span>{{ ExamineInfo.factory_scale_string }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="主营区域">
              <span v-for="(item, index) in ExamineInfo.main_regions_strings" :key="item">{{ item }}{{ index !== ExamineInfo.main_regions_strings.length - 1 ? '，' : '' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工厂人员规模">
              <span>{{ ExamineInfo.factory_employee_count || 0 }}人</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="主营商品">
              <span style="word-break: break-word">{{ ExamineInfo.main_products }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="SKU数量">
              <span>{{ ExamineInfo.sku_count || 0 }}个</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="公司年销售额">
              <span>{{ ExamineInfo.annual_sales }}万元</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="主营商品价格区间">
              <span>{{ ExamineInfo.main_products_min_price + '-' + ExamineInfo.main_products_max_price }}元</span>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="drawer-title">联系人信息</div>
      <a-table :columns="contactInfoColumns" :data-source="ExamineInfo.srs_supplier_contact_infos" size="small" bordered :pagination="false" class="mb-16px">
        <template #headerCell="{ column }">
          <span v-if="['name', 'mobile_phone_number', 'is_default'].includes(column.dataIndex)" class="color-red-6">*</span>
          <span>{{ column.title }}</span>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.type === 'input'">
            <a-form-item :name="['srs_supplier_contact_infos', column.dataIndex]" :rules="column.rules(record, column)">
              <a-input v-model:value="record[column.dataIndex]" />
            </a-form-item>
          </template>
          <template v-if="column.type === 'select'">
            <a-form-item :name="column.dataIndex" :rules="column.rules(record, column)" style="width: 70px">
              <a-select v-model:value="record[column.dataIndex]" :options="column.selectArr" style="width: 100%" @change="changeSelect($event, index, 'srs_supplier_contact_infos')" />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'operate'">
            <a-space>
              <MinusOutlined style="color: red" @click="onDelContact(index, 'srs_supplier_contact_infos')" />
              <PlusOutlined v-if="index >= 1 || index == ExamineInfo.srs_supplier_contact_infos.length - 1" @click="onAddTableItem('srs_supplier_contact_infos')" />
            </a-space>
          </template>
        </template>
      </a-table>
      <div class="drawer-title">财务信息</div>
      <a-form :model="ExamineInfo" :label-col="{ style: { width: '150px' } }" :wrapper-col="{ span: 17 }">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="结算方式" required>
              <a-select v-model:value="ExamineInfo.settlement_method" :options="settlementMethodOptions" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="发票类型" required>
              <a-select v-model:value="ExamineInfo.invoice_type" :options="invoiceTypeOptions" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="默认税率" required>
              <a-select v-model:value="ExamineInfo.default_tax_rate" :options="defaultTaxRateOptions" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-table :columns="financeInfoColumns" :data-source="ExamineInfo.srs_supplier_finance_infos" size="small" bordered :pagination="false" class="mb-16px">
        <template #headerCell="{ column }">
          <span class="color-red-6">*</span>
          {{ column.title }}
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.type === 'input'">
            <a-form-item :name="column.dataIndex" :rules="column.rules(record, column)">
              <a-input v-model:value="record[column.dataIndex]" />
            </a-form-item>
          </template>
          <template v-if="column.type === 'select'">
            <a-form-item :name="column.dataIndex" :rules="column.rules(record, column)" style="width: 70px">
              <a-select v-model:value="record[column.dataIndex]" :options="column.selectArr" style="width: 100%" @change="changeSelect($event, index, 'srs_supplier_finance_infos')" />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'operate'">
            <a-space>
              <MinusOutlined style="color: red" @click="onDelContact(index, 'srs_supplier_finance_infos')" />
              <PlusOutlined v-if="index >= 1 || index == ExamineInfo.srs_supplier_finance_infos.length - 1" @click="onAddTableItem('srs_supplier_finance_infos')" />
            </a-space>
          </template>
        </template>
      </a-table>
    </a-form>
    <template #footer>
      <a-space class="flex justify-end">
        <a-button type="primary" @click="handleSave">保存</a-button>
        <a-button @click="handleClose">关闭</a-button>
      </a-space>
    </template>
    <a-image-preview-group
      :style="{ display: 'none' }"
      :preview="{
        visible: imageVisible,
        onVisibleChange: setVisible,
      }"
    >
      <div v-for="(imgitem, imgindex) in previewUrl" :key="imgindex">
        <a-image :width="0" :src="imgitem" />
      </div>
    </a-image-preview-group>
  </a-drawer>
</template>

<script setup lang="ts">
import { GetSupplierInfo, GetEnumList, UpdateSupplierFollow } from '@/servers/supplierSettlementApproval'
import { message } from 'ant-design-vue'
import { ref, onMounted } from 'vue'
import { MinusOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { defEmptyStr } from '@/utils/index'

const userData = ref()
const href = ref()
const VITE_APP_ENV = ref('')
// 预览url
const previewUrl = ref<Array<string>>([])
const imageVisible = ref(false)
const setVisible = (visible: boolean) => {
  imageVisible.value = visible
}
const loading = ref(false)
const formRef = ref()
const openDrawer = ref(false)
// 编辑信息
const ExamineInfo = ref<any>({})
// 传入的列表id
const ExamineId = ref<number>(0)
// 结算方式
const settlementMethodOptions = ref<any[]>([])
// 发票类型
const invoiceTypeOptions = ref<any[]>([])
// 默认税率
const defaultTaxRateOptions = ref<any[]>([])
// 账户类型
const accountTypeOptions = ref<any[]>([])
// 联系人信息表格列
const contactInfoColumns = ref<any[]>([
  {
    title: '联系人姓名',
    dataIndex: 'name',
    align: 'center',
    type: 'input',
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, isRequired: true, length: 20 }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '职务',
    dataIndex: 'job',
    align: 'center',
    type: 'input',
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, length: 20 }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '手机号',
    dataIndex: 'mobile_phone_number',
    align: 'center',
    type: 'input',
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, isRequired: true, length: 13 }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    align: 'center',
    type: 'input',
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, length: 50 }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '微信',
    dataIndex: 'weixin_number',
    align: 'center',
    type: 'input',
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, length: 20 }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '默认联系人',
    dataIndex: 'is_default',
    align: 'center',
    type: 'select',
    selectArr: [
      {
        label: '是',
        value: true,
      },
      { label: '否', value: false },
    ],
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, isRequired: true, type: 'select' }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
  },
])
// 财务信息表格列
const financeInfoColumns = ref<any[]>([
  {
    title: '账户名称',
    dataIndex: 'account_name',
    align: 'center',
    type: 'input',
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '账户类型',
    dataIndex: 'account_type',
    align: 'center',
    type: 'select',
    selectArr: [],
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, isRequired: true }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '银行卡号',
    dataIndex: 'collection_card_number',
    align: 'center',
    type: 'input',
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '开户行',
    dataIndex: 'collection_bank',
    type: 'input',
    align: 'center',
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '支行',
    dataIndex: 'collection_bank_branch',
    align: 'center',
    type: 'input',
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '是否默认',
    dataIndex: 'is_default',
    align: 'center',
    type: 'select',
    selectArr: [
      {
        label: '是',
        value: true,
      },
      { label: '否', value: false },
    ],
    rules: (row: any, column: any) => {
      return [{ validator: () => validators({ row, column, isRequired: true, type: 'select' }), trigger: ['change', 'blur'] }]
    },
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
  },
])
const validators = ({ row, isRequired, length, column, type }: any) => {
  const value = row[column.dataIndex]
  const tipsBefore = type == 'select' ? '请选择' : '请输入'
  if (typeof value != 'number' && typeof value != 'boolean' && isRequired && (!value || value.trim() === '')) {
    return Promise.reject(tipsBefore + column.title)
  }
  if (length && String(value).length > length) {
    return Promise.reject(`不可超过${length}字符`)
  }
  return Promise.resolve()
}

// 关闭编辑
const handleClose = () => {
  formRef.value.resetFields()
  openDrawer.value = false
}
// 打开编辑
const showDrawer = (id: number) => {
  openDrawer.value = true
  ExamineId.value = id
  getSupplierInfo()
  getEnumList()
}
const certificatePreview = async (id: number | number[], type = 1) => {
  console.log('window', window.location)
  previewUrl.value = []
  setVisible(false)
  let url: string[] = []
  if (Array.isArray(id)) {
    id.forEach((item) => {
      if (VITE_APP_ENV.value == 'development') {
        url.push(`${window.location.origin}/api/api/Files/ViewByFileId?fileId=${item}`)
      } else {
        url.push(`${window.location.origin}/api/Files/ViewByFileId?fileId=${item}`)
      }
    })
  } else {
    if (VITE_APP_ENV.value == 'development') {
      url = [`${window.location.origin}/api/api/Files/ViewByFileId?fileId=${id}`]
    } else {
      url = [`${window.location.origin}/api/Files/ViewByFileId?fileId=${id}`]
    }
  }

  console.log('url', url)
  const arr: any = []
  try {
    url.forEach(async (item) => {
      const response = await fetch(item, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/octet-stream',
          logintoken: userData.value.login_token,
        },
      })
      console.log('response11', response)

      if (!response.ok) {
        message.warning('获取失败')
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const blob = await response.blob()
      const previewImageUrl = URL.createObjectURL(blob)
      previewUrl.value.push(previewImageUrl)
      if (typeof id === 'number' && type !== 1) {
        window.open(previewUrl.value[0], '_blank')
        setTimeout(() => URL.revokeObjectURL(previewUrl.value[0]), 30000)
      }
      console.log(previewImageUrl)
    })

    // previewUrl.value = arr
    console.log('previewUrl', previewUrl.value)
    if (type == 1) {
      imageVisible.value = true
      return
    }
    // 在新窗口打开预览

    // 可选：一段时间后释放内存

    return arr[0]
  } catch (error) {
    console.error('转换失败:', error)
    throw error
  }
}

// 获取供应商信息
const getSupplierInfo = async () => {
  const params = {
    id: ExamineId.value,
    preview: false,
  }
  const res = await GetSupplierInfo(params)
  ExamineInfo.value = res.data
  ExamineInfo.value.officeAddress =
    defEmptyStr(ExamineInfo.value.office_address_province) +
    defEmptyStr(ExamineInfo.value.office_address_city) +
    defEmptyStr(ExamineInfo.value.office_address_area) +
    defEmptyStr(ExamineInfo.value.office_address_detail)
}
// 获取枚举列表
const getEnumList = async () => {
  // 结算方式
  const res = await GetEnumList()
  settlementMethodOptions.value = res.data.settlementmethod_list.map((item: any) => ({
    label: item.label,
    value: Number(item.value),
  }))
  // 发票类型
  invoiceTypeOptions.value = res.data.invoicetype_list.map((item: any) => ({
    label: item.label,
    value: Number(item.value),
  }))
  // 默认税率
  defaultTaxRateOptions.value = res.data.defaulttaxrate_list.map((item: any) => ({
    label: item.label,
    value: Number(item.value),
  }))
  // 账户类型
  accountTypeOptions.value = res.data.publicaccounttype_list.map((item: any) => ({
    label: item.label,
    value: Number(item.value),
  }))
  // 账户类型赋值给表格列
  const accountTypeColumn = financeInfoColumns.value.find((col) => col.dataIndex === 'account_type')
  if (accountTypeColumn) {
    accountTypeColumn.selectArr = accountTypeOptions.value
  }
}
// 打开企信网
const openEnterpriseNet = () => {
  window.open('https://www.gsxt.gov.cn/index.html', '_blank')
}

const handleSave = async () => {
  try {
    await formRef.value.validateFields()
    const form = {
      id: ExamineInfo.value.id,
      settlement_method: `${ExamineInfo.value.settlement_method}`,
      invoice_type: `${ExamineInfo.value.invoice_type}`,
      default_tax_rate: `${ExamineInfo.value.default_tax_rate}`,
      srs_supplier_contact_infos: ExamineInfo.value.srs_supplier_contact_infos,
      srs_supplier_finance_infos: ExamineInfo.value.srs_supplier_finance_infos,
    }
    const res = await UpdateSupplierFollow(form)
    message.success('保存成功')
    console.log('UpdateSupplierFollow:', res)
    handleClose()
  } catch (err) {
    loading.value = false
  }
}
const onAddTableItem = (tableData) => {
  if (ExamineInfo.value[tableData].length < 5) ExamineInfo.value[tableData].push({})
}
const onDelContact = (index, tableData) => {
  if (ExamineInfo.value[tableData].length <= 1) return
  ExamineInfo.value[tableData].splice(index, 1)
}
const changeSelect = (val, index, key) => {
  if (val && ['srs_supplier_contact_infos', 'srs_supplier_finance_infos'].includes(key)) {
    ExamineInfo.value[key].forEach((item, i) => {
      console.log('item', item)
      if (index !== i) {
        item.is_default = false
      }
    })
  }
}

watch(
  () => ExamineInfo.value.srs_supplier_contact_infos,
  () => {
    if (ExamineInfo.value.srs_supplier_contact_infos && (ExamineInfo.value.srs_supplier_contact_infos.length == 1 || !ExamineInfo.value.srs_supplier_contact_infos.find((n) => n.is_default))) {
      ExamineInfo.value.srs_supplier_contact_infos[0].is_default = true
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(
  () => ExamineInfo.value.srs_supplier_finance_infos,
  () => {
    if (ExamineInfo.value.srs_supplier_finance_infos && (ExamineInfo.value.srs_supplier_finance_infos.length == 1 || !ExamineInfo.value.srs_supplier_finance_infos.find((n) => n.is_default))) {
      ExamineInfo.value.srs_supplier_finance_infos[0].is_default = true
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

onMounted(() => {
  href.value = window.location.origin
  VITE_APP_ENV.value = import.meta.env.VITE_APP_ENV
  const userDatastr = localStorage.getItem('userData') || ''
  userData.value = userDatastr != '' ? JSON.parse(userDatastr) : {}
})

defineExpose({
  showDrawer,
})
</script>
