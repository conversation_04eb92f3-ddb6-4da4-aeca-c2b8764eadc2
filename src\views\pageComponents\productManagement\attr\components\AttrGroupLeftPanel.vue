<template>
  <div class="role-group-left-panel" :class="{ collapsed }">
    <div class="header" v-show="!collapsed">
      <span class="title">属性分组</span>
    </div>
    <div class="collapse-button" @click="toggleCollapse">
      <DoubleLeftOutlined v-if="!collapsed" />
      <DoubleRightOutlined v-else />
    </div>
    
    <div class="panel-content" v-show="!collapsed">
      <!-- 分组列表 -->
      <div class="group-list">
        <a-spin :spinning="loading">
          <div class="group-item-container">
            <!-- 所有分组（包括API返回的"全部"选项） -->
            <div
              v-for="group in filteredGroupList"
              :key="group.id || 'all'"
              class="group-item"
              :class="{ active: selectedGroupId === (group.id || 'all') }"
              @click="handleSelectGroup(group.id || 'all')"
            >
              <div class="group-content">
                <div class="group-name" :title="group.attr_group_name">{{ group.attr_group_name }}</div>
                <div class="group-count">({{ group.attr_count || 0 }})</div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { 
  
  DoubleLeftOutlined,
  DoubleRightOutlined
} from '@ant-design/icons-vue'

interface AttrGroup {
  id: string | number
  attr_group_name: string
  attr_count: number
  is_enabled: boolean
}

const props = defineProps<{
  groupList: AttrGroup[]
  selectedGroupId: string | number | null
  loading?: boolean
  collapsed: boolean
}>()

const emit = defineEmits<{
  selectGroup: [groupId: string | number]
  createGroup: []
  editGroup: [group: AttrGroup]
  deleteGroup: [group: AttrGroup]
  search: [value: string]
  'update:collapsed': [collapsed: boolean]
}>()

const searchValue = ref('')
const filteredGroupList = ref<AttrGroup[]>([])



// 过滤分组
const filterGroups = () => {
  if (!searchValue.value) {
    filteredGroupList.value = props.groupList
  } else {
    filteredGroupList.value = props.groupList.filter(group =>
      group.attr_group_name.toLowerCase().includes(searchValue.value.toLowerCase())
    )
  }
}

// 监听分组列表变化，更新过滤列表
watch(
  () => props.groupList,
  (newList) => {
   
    filterGroups()
    
  },
  { immediate: true }
)

// 监听搜索值变化
watch(searchValue, () => {
  filterGroups()
})

// 选择分组
const handleSelectGroup = (groupId: string | number) => {
  
  emit('selectGroup', groupId)
}



// 切换折叠状态
const toggleCollapse = () => {
  emit('update:collapsed', !props.collapsed)
}
</script>

<style lang="scss" scoped>
.role-group-left-panel {
  position: relative;
  height: 100%;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  transition: all 0.3s;
  width: 240px;
  
  &.collapsed {
    width: 0;
    padding: 0;
    border-right: none;
    overflow: visible; /* 确保折叠按钮在面板折叠时可见 */
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .title {
      font-size: 15px;
      font-weight: 500;
      color: #7f7f7f;
    }
  }
  
  .collapse-button {
    position: absolute;
    right: -14px;
    top: 50%;
    transform: translateY(-50%);
    width: 14px;
    height: 40px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-left: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    
    &:hover {
      background-color: #f8f8f9;
    }
    
    .anticon {
      font-size: 11px;
      color: #999;
    }
  }

  .panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .group-list {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 16px;

    .group-item-container {
      .group-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        margin-bottom: 4px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        position: relative;

        &:hover {
          background-color: #f5f5f5;

          .group-actions {
            opacity: 1;
          }
        }

        &.active {
          background-color: #e6f7ff;
          border: 1px solid #91d5ff;
        }

        .group-content {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0;

          .group-name {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 120px;
          }

          .group-count {
            font-size: 12px;
            color: #999;
            margin-left: 4px;
            white-space: nowrap;
          }
        }

        .group-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          opacity: 0;
          transition: opacity 0.2s;

          .action-icon {
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: color 0.2s;

            &:hover {
              color: #1890ff;
            }

            &.delete-icon:hover {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }
}

// 滚动条样式
.group-list::-webkit-scrollbar {
  width: 4px;
}

.group-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.group-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}
</style>
