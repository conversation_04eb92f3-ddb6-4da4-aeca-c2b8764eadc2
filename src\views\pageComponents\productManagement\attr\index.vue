<template>
  <div class="main">
    <div class="main-content" :class="{ 'panel-collapsed': isPanelCollapsed }">
      <!-- 左侧属性分组面板 -->
      <AttrGroupLeftPanel
        ref="AttrGroupLeftPanelRef"
        v-model:collapsed="isPanelCollapsed"
        :group-list="groupList"
        :selected-group-id="selectedGroupId"
        :loading="groupLoading"
        @selectGroup="handleGroupChange"
      />

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <SearchForm v-model:form="formArr" :page-type="PageType.ATTR_MANAGEMENT" @search="search" @setting="tableRef?.showTableSetting()" />
        <!-- 表格 -->
        <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.ATTR_MANAGEMENT" :get-list="GetAttrList" :form-format="formFormat" :data-format="dataFormat">
          <template #type="{ row }">
            <span>{{ row.type_label || row.type }}</span>
          </template>
          <template #create_at="{ row }">
            <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
          </template>
          <template #update_at="{ row }">
            <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
          </template>
          <template #operate="{ row }">
            <a-button type="text" @click="detail(row)" v-if="btnPermission[72001]">查看</a-button>
          </template>
        </BaseTable>

        <!-- 查看 -->
        <detail-drawer ref="detailDrawerRef" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import SearchForm from '@/components/SearchForm/index.vue'
import { PageType } from '@/common/enum'
import { GetAttrList, GetAttrGroupSelectOption } from '@/servers/Attr'
import { enumManager, getEnumLabel, getEnumOptions } from '@/utils/index'

import { onMounted, ref } from 'vue'

import AttrGroupLeftPanel from './components/AttrGroupLeftPanel.vue'
import DetailDrawer from './components/DetailDrawer.vue'

const { btnPermission } = usePermission()

interface AttrGroup {
  id: string | number
  attr_group_name: string
  attr_count: number
  is_enabled: boolean
}

const AttrGroupLeftPanelRef = ref()

const isPanelCollapsed = ref(true)
const tableRef = ref()

// 属性分组相关数据
const groupList = ref<AttrGroup[]>([])
const selectedGroupId = ref<string | number>('all')
const groupLoading = ref(false)

// 查看
const detailDrawerRef = ref()

// 搜索表单配置
const formArr: any = ref([
  {
    label: '属性编码',
    value: '',
    type: 'batch-input',
    key: 'code',
  },
  {
    label: '搜索属性名称',
    value: null,
    type: 'input',
    key: 'attr_name',
  },
  {
    label: '数据类型',
    value: null,
    type: 'select',
    options: [], // 将在 onMounted 中动态填充
    key: 'type',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'create_at',
    formKeys: ['create_start_at', 'create_end_at'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '最后修改时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'update_at',
    formKeys: ['update_start_at', 'update_end_at'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
// 处理分组选择变化
const handleGroupChange = (groupId: string | number) => {
  // 更新选中的分组ID
  selectedGroupId.value = groupId
  // 触发表格重新搜索（关键：重新查询数据）
  // 注意：这里只需要刷新表格，不需要重新加载分组列表
  tableRef.value.search()
}
// 初始化筛选条件
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.ATTR_MANAGEMENT) {
    const arr: any[] = []
    obj.ATTR_MANAGEMENT.forEach((x: any) => {
      formArr.value.forEach((y: any) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item: any) => {
      item.isShow = true
    })
  }
}

// 初始化枚举数据
const initEnumData = async () => {
  try {
    await enumManager.getEnumData()
    // 更新数据类型选项
    const typeOptions = getEnumOptions('attr.type')

    const typeFormItem = formArr.value.find((item: any) => item.key === 'type')
    if (typeFormItem) {
      typeFormItem.options = typeOptions
    }
  } catch (error) {
    console.error('初始化枚举数据失败:', error)
  }
}

onMounted(async () => {
  // 初始化枚举数据
  await initEnumData()
  loadAttrGroups()
  search()
  initScreening()
})

// 加载属性分组
const loadAttrGroups = async (searchValue = '', filterParams = {}) => {
  groupLoading.value = true
  try {
    // 构建传递给属性分组API的参数，包含当前的筛选条件
    const params = { ...filterParams }

    const res = await GetAttrGroupSelectOption(params)
    // console.log('loadAttrGroups - API response:', res)
    if (res.code === 0 || res.code === 1) {
      let groups = res.data || []
      // 如果有搜索值，进行过滤
      if (searchValue) {
        groups = groups.filter((group: any) => group.attr_group_name.toLowerCase().includes(searchValue.toLowerCase()))
      }
      groupList.value = groups
      // console.log('loadAttrGroups - groupList updated:', groupList.value)
    } else {
      groupList.value = []
    }
  } catch (error) {
    console.error('加载属性分组失败:', error)
    groupList.value = []
  } finally {
    groupLoading.value = false
  }
}

// 表单格式化
const formFormat = (obj: any) => {
  const data = { ...obj }
  if (selectedGroupId.value !== 'all') {
    data.attr_group_id = selectedGroupId.value
  }
  return data
}

// 数据格式化
const dataFormat = (data: any[]) => {
  return data.map((item: any) => ({
    ...item,
    // 获取属性名称（多语言支持）
    attr_name: getAttrName(item),
    // 转换数据类型为文本标签
    type_label: getEnumLabel('attr.type', item.type),
  }))
}

// 获取属性名称（多语言支持）
const getAttrName = (item: any) => {
  if (!item?.language_config?.length) return item?.attr_name || '--'
  // 优先获取中文简体的名称
  const zhConfig = item.language_config.find((config: any) => config.language_name === '中文-简体')
  return zhConfig?.attr_name || item.language_config[0]?.attr_name || item?.attr_name || '--'
}

// 获取当前表单的筛选参数（用于传递给属性分组API）
const getCurrentFilterParams = () => {
  const params: any = {}

  // 遍历表单项，提取有值的筛选条件
  formArr.value.forEach((item: any) => {
    if (item.formKeys && item.formKeys.length && item.value) {
      // 处理日期范围类型的字段
      item.formKeys.forEach((key: string, index: number) => {
        if (item.value[index]) {
          params[key] = item.value[index]
        }
      })
    } else if (item.value !== null && item.value !== '' && item.value !== undefined) {
      // 处理普通字段
      params[item.key] = item.value
    }
  })

  return params
}

// 搜索
const search = () => {
  // 1. 获取当前的筛选参数
  const filterParams = getCurrentFilterParams()

  // 2. 先刷新左侧属性分组，传递筛选参数
  loadAttrGroups('', filterParams)

  // 3. 刷新表格数据
  tableRef.value.search()
}

// 查看详情
const detail = (row: any) => {
  detailDrawerRef.value.open(row.id, false)
}

// 操作处理
const tapManipulateCore = (type: string, row: any = '') => {
  switch (type) {
    case 'add':
      // TODO: 实现新增属性功能
      console.log('新增属性')
      break
    default:
      break
  }
}
</script>

<style lang="scss" scoped>
.main-content {
  display: flex;
  height: 100%;
  transition: all 0.3s;

  &.panel-collapsed {
    .right-content {
      padding-left: 15px;
    }
  }
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 30px;
  background-color: #fff;
}
</style>
