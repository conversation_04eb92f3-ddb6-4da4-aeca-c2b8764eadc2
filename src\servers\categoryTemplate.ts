import { request } from './request'

// 获取类目模板列表
export const GetCategoryTemplateList = (data) => request({ url: '/plm/CategoryTemplate/GetCategoryTemplateList', data })

// 获取类目模板详情
export const GetCategoryTemplateDetail = (data) => request({ url: '/plm/CategoryTemplate/GetCategoryTemplateDetail', data })

// 获取复合属性值下拉框（按语言）
export const GetMultiAttrValueSelectOptionByLanguages = (data) => request({ url: '/plm/CategoryTemplate/GetMultiAttrValueSelectOptionByLanguages', data })

// 获取语言下拉框
export const GetLanguageSelectOption = () => request({ url: '/plm/CategoryTemplate/GetLanguageSelectOption' })

// 获取计量单位下拉框
export const GetMeteringUnitSelectOption = () => request({ url: '/plm/CategoryTemplate/GetMeteringUnitSelectOption' })

// 获取品牌数据源列表
export const GetBrandDataSourceList = (data) => request({ url: '/plm/CategoryTemplate/GetBrandDataSourceList', data })

// 获取制造商数据源列表
export const GetManufactureDataSourceList = (data) => request({ url: '/plm/CategoryTemplate/GetManufactureDataSourceList', data })

// 获取开发用户数据源列表
export const GetDevUserDataSourceList = (data) => request({ url: '/plm/CategoryTemplate/GetDevUserDataSourceList', data })

// 获取产品用户数据源列表
export const GetProductUserDataSourceList = (data) => request({ url: '/plm/CategoryTemplate/GetProductUserDataSourceList', data })

// 获取设计用户数据源列表
export const GetDesignUserDataSourceList = (data) => request({ url: '/plm/CategoryTemplate/GetDesignUserDataSourceList', data })

// 获取文案用户数据源列表
export const GetCopywriterUserDataSourceList = (data) => request({ url: '/plm/CategoryTemplate/GetCopywriterUserDataSourceList', data })

// 获取京东客户数据源列表
export const GetJdCustomerDataSourceList = (data) => request({ url: '/plm/CategoryTemplate/GetJdCustomerDataSourceList', data })

// 获取SRM供应商数据源列表
export const GetSrmSupplierDataSourceList = (data) => request({ url: '/plm/CategoryTemplate/GetSrmSupplierDataSourceList', data })
