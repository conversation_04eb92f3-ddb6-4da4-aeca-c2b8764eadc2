<template>
  <div class="main">
    <SearchForm v-model:form="formArr" :page-type="PageType.CategoryTemplate" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.CategoryTemplate" :get-list="GetCategoryTemplateList" :form-format="formFormat" :data-format="dataFormat">
      <template #create_at="{ row }">
        <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
      </template>
      <template #operate="{ row }">
        <a-button type="text" @click="detail(row)" v-if="btnPermission[74001]">查看</a-button>
      </template>
    </BaseTable>

    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
  </div>
</template>

<script lang="ts" setup>
import SearchForm from '@/components/SearchForm/index.vue'
import { PageType } from '@/common/enum'
import { GetCategoryTemplateList, GetCategoryTemplateDetail } from '@/servers/categoryTemplate'
import { buttonDebounce } from '@/utils/index'
import { onMounted, ref } from 'vue'
import DetailDrawer from './components/PreviewTemplate.vue'

const { btnPermission } = usePermission()

// 查看
const detailDrawerRef = ref()

// 搜索表单配置
const formArr: any = ref([
  {
    label: '类目模板编码',
    value: '',
    type: 'batch-input',
    key: 'code',
  },
  {
    label: '类目模板名称',
    value: null,
    type: 'input',
    key: 'name',
  },

  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'create_at',
    formKeys: ['create_start_at', 'create_end_at'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '最后修改时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'update_at',
    formKeys: ['update_start_at', 'update_end_at'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])

const isPanelCollapsed = ref(true)
const tableRef = ref()

// 初始化筛选条件
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.ATTR_MANAGEMENT) {
    const arr: any[] = []
    obj.ATTR_MANAGEMENT.forEach((x: any) => {
      formArr.value.forEach((y: any) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item: any) => {
      item.isShow = true
    })
  }
}

onMounted(() => {
  search()
  initScreening()
})

// 表单格式化
const formFormat = (obj: any) => {
  return { ...obj }
}

// 数据格式化
const dataFormat = (data: any[]) => {
  return data.map((item: any) => ({
    ...item,
  }))
}

// 搜索
const search = () => {
  tableRef.value.search()
}

// 查看详情
const detail = async (row: any) => {
  try {
    // 调用接口获取模板详情
    const response = await GetCategoryTemplateDetail({ id: row.id })

    if (response && response.data) {
      // 传递正确的参数：(data, page, permission, info)
      // page: 2 表示查看模式，1 表示预览模式
      detailDrawerRef.value.open(response.data, 2, null, row)
    } else {
      console.error('获取模板详情失败：数据为空', response)
    }
  } catch (error) {
    console.error('获取模板详情失败：', error)
  }
}

// 操作处理
const tapManipulateCore = (type: string, row: any = '') => {
  switch (type) {
    case 'add':
      // TODO: 实现新增属性功能
      console.log('新增属性')
      break
    default:
      break
  }
}
const tapManipulate = buttonDebounce(tapManipulateCore, 200)
</script>

<style lang="scss" scoped>
.main {
  height: 100vh;
  overflow: hidden;
}

.main-content {
  display: flex;
  height: 100%;
  transition: all 0.3s;

  &.panel-collapsed {
    .right-content {
      padding-left: 15px;
    }
  }
}
</style>
