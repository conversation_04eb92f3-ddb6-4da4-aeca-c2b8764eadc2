<template>
  <a-drawer
    :footer="logVisble ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看通知"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <template #extra v-if="btnPermission[61006]">
      <a-button @click="changeLogVisible">日志</a-button>
    </template>
    <div class="detail-form-container">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />

      <a-form
        v-if="!detailloading && target"
        layout="horizontal"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <!-- 基础通知信息 -->
        <div class="form-section">
          <div class="drawer-title">基础通知信息</div>

          <!-- 通知编码 -->
          <a-form-item label="通知编码">
            <span class="form-value">{{ target.code || target.no || '--' }}</span>
          </a-form-item>

          <a-form-item label="通知标题">
            <span class="form-value">{{ target.title || '--' }}</span>
          </a-form-item>

          <a-form-item label="通知内容">
            <span class="form-value">{{ target.content || '--' }}</span>
          </a-form-item>

          <a-form-item label="通知类型">
            <span class="form-value">{{ target.type || '系统通知' }}</span>
          </a-form-item>

          <a-form-item label="通知范围">
            <span class="form-value">{{ getScopeDisplay(target.scope) }}</span>
          </a-form-item>

          <a-form-item label="发布计划">
            <span class="form-value">{{ getPublishTypeDisplay(target.publish_type) }}</span>
          </a-form-item>

          <a-form-item label="计划发布时间">
            <span class="form-value">{{ target.scheduled_publish_at || target.plan_publish_time || '--' }}</span>
          </a-form-item>

          <a-form-item label="发布时间">
            <span class="form-value">{{ target.publish_at || target.publish_time || '--' }}</span>
          </a-form-item>

          <a-form-item label="过期时间">
            <span class="form-value">{{ target.expired_at || target.exp_time || '--' }}</span>
          </a-form-item>

          <a-form-item label="状态">
            <span class="form-value">{{ getStatusDisplay(target.notice_status || target.status) }}</span>
          </a-form-item>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="drawer-title">其他信息</div>

          <div class="info-section">
            <div class="info-row-group">
              <div class="info-row">
                <div class="info-label">创建时间</div>
                <div class="info-content">{{ target.create_at || target.created || '--' }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">创建人</div>
                <div class="info-content">
                  {{ getCreatorInfo(target).name }}
                  <span v-if="getCreatorInfo(target).department || getCreatorInfo(target).job" class="user-extra">
                    （{{ [getCreatorInfo(target).job, getCreatorInfo(target).department].filter(Boolean).join('/') }}）
                  </span>
                </div>
              </div>
            </div>

            <div class="info-row-group">
              <div class="info-row">
                <div class="info-label">最后修改时间</div>
                <div class="info-content">{{ target.update_at || target.modified || '--' }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">最后修改人</div>
                <div class="info-content">
                  {{ getModifierInfo(target).name }}
                  <span v-if="getModifierInfo(target).department || getModifierInfo(target).job" class="user-extra">
                    （{{ [getModifierInfo(target).job, getModifierInfo(target).department].filter(Boolean).join('/') }}）
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-form>

      <LogDrawer ref="LogDrawerRef" class="log" v-if="!detailloading && appStore.isOpenLog" />
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { DetailByEdit } from '@/servers/Notice'
import { GetEnum } from '@/servers/Common'
import useAppStore from '@/store/modules/app'
import LogDrawer from './LogDrawer.vue'

const appStore = useAppStore()
const { btnPermission } = usePermission()
const LogDrawerRef = ref()
const detailVisible = ref(false)
const logVisble = ref(false)
const detailloading = ref(false)
const target = ref<any>(null)

// 状态显示函数：兼容新旧格式
const getStatusDisplay = (status) => {
  if (typeof status === 'string') {
    return status || '--'
  }
  if (typeof status === 'number') {
    switch (status) {
      case 1: return '草稿'
      case 2: return '待发布'
      case 3: return '已发布'
      default: return '--'
    }
  }
  return '--'
}

// 通知范围显示函数
const getScopeDisplay = (scope) => {
  if (typeof scope === 'string') {
    return scope || '--'
  }
  if (typeof scope === 'number') {
    return scope === 1 ? '全员' : '--'
  }
  return scope || '--'
}

// 发布计划显示函数
const getPublishTypeDisplay = (publishType) => {
  if (typeof publishType === 'string') {
    return publishType || '--'
  }
  if (typeof publishType === 'number') {
    switch (publishType) {
      case 1: return '立即发布'
      case 2: return '定时发布'
      default: return '--'
    }
  }
  return publishType || '--'
}

// 获取创建人信息
const getCreatorInfo = (target) => {
  if (!target) return { name: '--', department: '', job: '' }

  // 优先使用新格式字段
  const name = target.create_user_real_name || target.creator || '--'
  const department = target.create_user_department || target.depart_of_creator || ''
  const job = target.create_user_jobtitlename || target.job_of_creator || ''

  return { name, department, job }
}

// 获取修改人信息
const getModifierInfo = (target) => {
  if (!target) return { name: '--', department: '', job: '' }

  // 优先使用新格式字段
  const name = target.update_user_real_name || target.modifier || '--'
  const department = target.update_user_department || target.depart_of_modifier || ''
  const job = target.update_user_jobtitlename || target.job_of_modifier || ''

  return { name, department, job }
}
const open = (id, boolean) => {
  target.value = null
  logVisble.value = boolean

  detailloading.value = true
  detailVisible.value = true
  DetailByEdit({ id })
    .then((res) => {
      console.log(res)
      target.value = res.data
      detailloading.value = false
      nextTick(() => {
        LogDrawerRef.value?.open(target.value)
      })
    })
    .catch(() => {
      detailloading.value = false
    })
}

// 查看日志
const changeLogVisible = () => {
  appStore.changeLogOpenVisible(appStore.isOpenLog ? 0 : 1)
  if (appStore.isOpenLog) {
    nextTick(() => {
      LogDrawerRef.value?.open(target.value)
    })
  }
}

const enumData = ref<any>([])
// 获取所有枚举选项
const getEnum = () => {
  GetEnum()
    .then((res) => {
      enumData.value = res.data?.notice?.notice_status || []
    })
    .catch((error) => {
      console.warn('获取枚举选项失败:', error)
      enumData.value = []
    })
}
getEnum()

const statusName = (status) => {
  const item = enumData.value.find((item) => item.value === status)
  return item ? item.label : '--'
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.detail-form-container {
  padding: 24px;
  padding-left: 30px;

  // 水平布局样式
  :deep(.ant-form-horizontal) {
    .ant-form-item {
      margin-bottom: 20px;

      .ant-form-item-label {
        text-align: left;
        padding-right: 8px;

        > label {
          color: #262626;
          font-size: 14px;
        }
      }

      .ant-form-item-control {
        .ant-form-item-control-input {
          .form-value {
            font-size: 14px;
            color: #262626;
            line-height: 32px;
          }
        }
      }
    }
  }

  .loadingIcon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 30px;
    color: #1890ff;
  }
}

.form-section {
  margin-bottom: 32px;

  .drawer-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #262626;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }
}

.info-section {
  .info-row-group {
    display: flex;
    margin-bottom: 16px;

    .info-row {
      flex: 1;
      display: flex;
      align-items: flex-start;

      .info-label {
        width: 120px;
        font-size: 14px;
        color: #262626;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .info-content {
        flex: 1;
        font-size: 14px;
        color: #262626;
        line-height: 1.5;
        word-break: break-word;

        .user-extra {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
