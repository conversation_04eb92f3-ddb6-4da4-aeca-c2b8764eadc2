<template>
  <div class="filePreviewMainBox" style="user-select: none;">
    <template v-for="(item, index) in fileList" :key="index">
      <div @click.stop="preview(item, index)" class="filePreviewCard">
        <div class="preview" :style="'display: flex;align-items: center;justify-content: center;'">
          <img style="width: 100%;" v-if="item.url" :src="item.url" alt="">
              <!-- <div v-show="['jpg', 'jpeg', 'png', 'bmp', 'txt', 'pdf'].indexOf(item.name.split('.').pop().toLowerCase()) === -1"> -->
                <img v-if="showCardIcon(item.file ? item.file.name : item.name)" class="otherFile" :src="getIconPath(item.file ? item.file.name : item.name)" alt="" />
              <!-- </div> -->
          <LoadingOutlined v-show="item.loading" class="loadingIcon" />
          <div class="previewMasker">
            <eye-outlined class="icon" />
            <div class="tip">预览</div>
            <div class="delBtn" @click.stop="downLoad(item)"><VerticalAlignBottomOutlined class="delBtnIcon" /></div>
          </div>
        </div>
        <div class="title">
          <img class="icon" :src="getIconPath(item.file ? item.file.name : item.name)" alt="" />
          <span v-if="item.file">{{ item.file.name }}</span>
          <span v-if="item.name">{{ item.name }}</span>
        </div>
      </div>
    </template>
  </div>
  <a-image-preview-group :preview="{ visible: pdfPreviewerVisible, onVisibleChange: (value) => { pdfPreviewerVisible = value } }">
    <a-image v-for="(item, index) in pdfPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
  </a-image-preview-group>
  <a-image-preview-group :preview="{ current: imgPreviewerCurrent, visible: imgPreviewerVisible, onVisibleChange: (value) => { imgPreviewerVisible = value } }">
    <a-image v-for="(item, index) in imgPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
  </a-image-preview-group>
  <a-modal v-model:open="vedioVisible" title="预览">
    <video v-if="videoSrc" :src="videoSrc" controls style="width: 100%; max-width: 600px;"></video>
    <template #footer>
      <a-button @click="vedioVisible = false">关闭</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { message } from 'ant-design-vue';
  import { downloadFile, encodeUrlFilename } from '@/utils/index';
  import { cloneDeep } from 'lodash' 
  import { FileImageFilled, FilePdfFilled, LoadingOutlined, FileTextFilled, EyeOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue';
  import * as pdfjsLib from 'pdfjs-dist/build/pdf';
  const appStore = useAppStore();
  import useAppStore from '@/store/modules/app';
  pdfjsLib.GlobalWorkerOptions.workerSrc = "/pdf.worker.min.js";
  const props = defineProps({
    value: {
      type: Array,
      default: []
    }
  })
  const fileList = ref([])
  const videoSrc = ref(null)
  const vedioVisible = ref(false)
  const pdfPreviewerVisible = ref(false)
  const pdfPreviewerArray = ref([])
  const imgPreviewerVisible = ref(false)
  const imgPreviewerArray = ref([])
  const imgPreviewerCurrent = ref(0)
  const downLoad = (item) => {
    downloadFile(encodeUrlFilename(item.pdfUrl ? item.pdfUrl : item.url), item.name.split('?tmp')[0])
  }
  const preview = (item, index) => {
    if (item.loading) return
    if (['pdf', 'application/pdf'].indexOf(item.type) != -1) {
      previewPdf(item)
    } else if (['image', 'png', 'jpg', 'jpeg', 'bmp', 'gif'].indexOf(item.type) != -1 || item.type.indexOf('image') != -1) {
      previewImg(item)
    } else if (['text', 'txt'].indexOf(item.type) != -1) {
      previewText(item)
    } else if(['mp4'].indexOf(item.type) != -1 || item.type.indexOf('video/mp4') != -1) {
      videoSrc.value = item.url
      vedioVisible.value = true
    } else {
      message.warning('此类型文件不支持预览')
    }
  }
  const previewImg = (item) => {
    var arr = fileList.value.filter(e => ['image', 'png', 'jpg', 'jpeg', 'bmp', 'gif'].indexOf(e.type) != -1 || item.type.indexOf('image') != -1).map(a => a.url)
    imgPreviewerVisible.value = true
    imgPreviewerArray.value = [...arr]
    imgPreviewerCurrent.value = arr.findIndex(e => e === item.url)
  }
  const previewText = (item) => {
    imgPreviewerVisible.value = true
    imgPreviewerArray.value = [item.url]
    imgPreviewerCurrent.value = 0
  }
  const getPdfMinPreviewUrl = async (fileUrl) => {
    return new Promise(async (resolve, reject) => {
      const loadingTask = pdfjsLib.getDocument(fileUrl);
      const pdf = await loadingTask.promise;
      const page = await pdf.getPage(1);
      const viewport = page.getViewport({ scale: 0.2 })
      const canvas = document.createElement('canvas')
      canvas.height = viewport.height
      canvas.width = viewport.width
      const context = canvas.getContext('2d');
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };
      await page.render(renderContext).promise
      resolve(canvas.toDataURL('image/png'))
    })
  }
  const getTxtAsImage = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsText(file)
      reader.onload = () => {
        const textContent = reader.result as string
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        const fontSize = 14
        const lineHeight = 24
        const padding = 20
        const lines = textContent.split('\n')
        canvas.width = 800
        canvas.height = 500
        context.fillStyle = '#fff'
        context.fillRect(0, 0, canvas.width, canvas.height)
        context.font = `${fontSize}px Arial`
        context.fillStyle = '#000'
        context.textBaseline = 'top'
        lines.forEach((line, index) => {
          context.fillText(line, padding, padding + index * lineHeight)
        })
        const imageUrl = canvas.toDataURL('image/png')
        resolve(imageUrl)
      }
      reader.onerror = () => {
        reject('文件读取失败')
      }
    })
  }
  const getIconPath = (name) => {
    const type = name.split('.').pop().toLowerCase()
    let pathName = ''
    if (['zip', 'rar', '7z'].indexOf(type) != -1) {
      pathName = 'zip'
    } else if (['m4a', 'mp3', 'wav'].indexOf(type) != -1) {
      pathName = 'audio'
    } else if (['xls', 'xlsx'].indexOf(type) != -1) {
      pathName = 'excel'
    } else if (['ppt', 'pptx'].indexOf(type) != -1) {
      pathName = 'ppt'
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv'].indexOf(type) != -1) {
      pathName = 'video'
    } else if (['doc', 'docx'].indexOf(type) != -1) {
      pathName = 'word'
    } else if (['csv', 'txt'].indexOf(type) != -1) {
      pathName = 'txt'
    } else if (['bmp', 'png', 'jpg', 'jpeg', 'gif'].indexOf(type) != -1) {
      pathName = 'image'
    } else if (['pdf'].indexOf(type) != -1) {
      pathName = 'pdf'
    } else if (['psd'].indexOf(type) != -1) {
      pathName = 'psd'
    }
    return `filePic/${pathName}.png`
  }
  const showCardIcon = (name) => {
    const type = name.split('.').pop().toLowerCase()
    let Boolean = true
    if (['bmp', 'png', 'jpg', 'jpeg', 'gif'].indexOf(type) != -1) {
      Boolean = false
    } else if (['pdf'].indexOf(type) != -1) {
      Boolean = false
    }
    return Boolean
  }
  const previewPdf = async (item) => {
    pdfPreviewerArray.value = [...item.doneImgArr]
    pdfPreviewerVisible.value = true
    const loadingTask = pdfjsLib.getDocument(item.pdfUrl);
    const pdf = await loadingTask.promise;
    for (let currentPage = item.doneImgLength; currentPage <= pdf.numPages; currentPage++) {
      if (pdfPreviewerVisible.value) {
        const page = await pdf.getPage(currentPage);
        const viewport = page.getViewport({ scale: 3 })
        const canvas = document.createElement('canvas')
        canvas.height = viewport.height
        canvas.width = viewport.width
        const context = canvas.getContext('2d');
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };
        await page.render(renderContext).promise
        item.doneImgArr.push(canvas.toDataURL('image/png'))
        item.doneImgLength = currentPage + 1
        pdfPreviewerArray.value.push(canvas.toDataURL('image/png'))
      }
    }
  };
  watch(
  () => props.value,
  async () => {
    var baseUrl = await appStore.getExteriorDormanName();
    if (props.value && props.value.length) {
      fileList.value = cloneDeep(props.value)
      try {
        fileList.value.forEach((x, index) => {
          if (!x.isRemote) return
          const completeUrl = encodeUrlFilename(x.url) // 需要先拼成完整路径再显示
          console.log(completeUrl)
          if (x.name.split('.').pop().toLowerCase() === 'pdf' && !x.hasLoad && !x.file) {
            fileList.value[index].loading = true
            fileList.value[index].doneImgArr = []
            fileList.value[index].doneImgLength = 1
            fileList.value[index].hasLoad = true
            getPdfMinPreviewUrl(completeUrl)
              .then((res) => {
                fileList.value[index].pdfUrl = completeUrl as string
                fileList.value[index].url = res as string
                fileList.value[index].loading = false
              })
              .catch(() => {
                fileList.value[index].error = true
                fileList.value[index].loading = false
              })
          } else if (x.name.split('.').pop().toLowerCase() === 'txt' && !x.file && !x.hasLoad) {
            fileList.value[index].loading = true
            fileList.value[index].loading = true
            fileList.value[index].hasLoad = true
            getTxtAsImage(completeUrl)
              .then((res) => {
                fileList.value[index].url = res as string
                fileList.value[index].loading = false
              })
              .catch(() => {
                fileList.value[index].error = true
                fileList.value[index].loading = false
              })
          } else if (!x.hasLoad) {
            fileList.value[index].url = completeUrl
            fileList.value[index].hasLoad = true
          }
        })
      } catch (error) {
        console.log(error);
      }
    } else {
      fileList.value = []
    }
  }, { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
  .uploaderTip {
    height: 120px;
    line-height: 120px;
    text-align: center;
    color: rgba(0,0,0,0.5);
    margin: -16px 0;
    user-select: none;
  }
  .filePreviewMainBox {
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    .filePreviewCard {
      width: 150px;
      cursor: pointer;
      .preview {
        width: 100%;
        height: 96px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        overflow: hidden;
        position: relative;
        .previewMasker {
          width: 100%;
          height: 100%;
          position: absolute;
          background-color: rgba(0, 0, 0, 0.5);
          top: 0;
          transition: opacity 0.3s;
          opacity: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          .delBtn {
            width: 25px;
            height: 25px;
            border-radius: 4px;
            position: absolute;
            right: 5px;
            top: 5px;
            background-color: rgba(64, 158, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            .delBtnIcon {
              color: #fff;
              font-size: 14px;
            }
            &:hover {
              background-color: rgba(64, 158, 255, 1);
            }
          }
          .icon {
            font-size: 16px;
            color: #fff;
          }
          .tip {
            color: #fff;
            font-size: 14px;
            margin-left: 8px;
          }
        }
      }
      &:hover {
        .preview {
          border-color: #40a9ff;
        }
        .previewMasker {
          opacity: 1;
        }
        .title {
          color: #40a9ff;
          text-decoration: underline;
        }
      }
      .title {
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: nowrap;
        line-height: 36px;
        font-size: 13px;
        text-align: left;
        .icon {
          width: 14px;
          height: 14px;
          margin-right: 4px;
          font-size: 16px;
          position: relative;
          top: -2px;
        }
      }
    }
  }
  .loadingIcon {
    color: #1890ff;
    font-size: 30px;
    position: absolute;
  }
  .pdfIcon {
    color: rgb(254,83,89);
  }
  .imgIcon {
    color: rgb(37,179,158);
  }
  .txtIcon {
    color: rgb(74,144,255);
  }
  // .draggerUploader {
  //   display: inline-block;
  //   background-color: red;
  //   width: 100%;
  // }
  .otherFile {
    position: absolute;
    inset: 0;
    width: 50px;
    height: 50px;
    margin: auto;
  }
</style>