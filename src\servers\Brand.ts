import { request } from './request'

// 获取品牌列表
export const GetList = (data) => {
  // 根据新的API文档，使用POST方法调用GetList接口
  return request({ url: '/api/BrandInfo/GetList', data }, 'POST').then((res) => {
    console.log('品牌列表API原始响应:', res)

    // 适配BaseTable组件期望的数据结构
    // BaseTable期望 res.data.data 或 res.data.list，但我们的API返回的数据直接在 res.data 中
    // const brandList = Array.isArray(res.data) ? res.data : []

    return {
      ...res,
      // data: {
      //   data: brandList, // 将数组数据放到 data.data 中
      //   total: res.total || brandList.length // 优先使用API返回的total，否则使用数组长度
      // }
    }
  })
}

// 新建品牌
export const Add = (data) => request({ url: '/api/BrandInfo/Create', data })

// 查看品牌详情
export const Details = (data) => request({ url: '/api/BrandInfo/Get', data }, 'GET')

// 编辑品牌
export const Update = (data) => request({ url: '/api/BrandInfo/Update', data })

// 修改品牌状态
export const UpdateStatus = (data) => request({ url: '/api/BrandInfo/UpdateMgmtStatus', data })

// 删除品牌
export const Delete = (data) => request({ url: '/api/BrandInfo/Delete', data }, 'GET')

// 获取品牌授权书记录
export const GetAuthFiles = (data) => request({ url: '/api/BrandInfo/GetAuthFiles', data }, 'GET')

// 上传品牌LOGO
export const UploadLogo = (data) => request({ url: '/api/Files/UploadFile?fileModule=Default', data, isFormData: true })

// 上传授权书
export const UploadAuthorization = (data) => request({ url: '/api/Files/UploadFile?fileModule=Default', data, isFormData: true })

// 获取国家地区下拉选项
export const GetCountryOptions = (data) => request({ url: '/api/Common/GetCountryOptions', data })
