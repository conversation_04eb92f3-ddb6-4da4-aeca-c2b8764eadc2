<template>
  <a-drawer
    :footer="logVisible ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看品牌"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <!-- <template #extra>
      <a-button v-if="logVisible" @click="changeLogVisible">日志</a-button>
    </template> -->

    <div class="detail-form-container">
      <LoadingOutlined v-show="detailLoading" class="loadingIcon" />

      <a-form v-if="!detailLoading && target" layout="horizontal" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <!-- 基础品牌信息 -->
        <div class="form-section">
          <div class="drawer-title">基础品牌信息</div>

          <!-- 品牌编码 -->
          <!-- 品牌编码（改为info-row格式） -->
          <div class="info-row">
            <div class="info-label">品牌编码</div>
            <div class="info-content">{{ target.brand_number || '--' }}</div>
          </div>

          <!-- 品牌名称（改为info-row格式） -->
          <div class="info-row">
            <div class="info-label">品牌名称</div>
            <div class="info-content">{{ target.brand_name || '--' }}</div>
          </div>

          <div class="logo-upload-row">
            <div class="logo-label">LOGO</div>
            <div class="logo-upload-content">
              <div class="flex">
                <div class="logo-display-container">
                  <div class="logo-preview" v-if="target.logo_id">
                    <img :src="logoImageUrl" alt="品牌LOGO" @error="handleImageError" />
                    <div class="preview-actions">
                      <eye-outlined class="preview-icon" @click="handleLogoPreview" />
                    </div>
                  </div>
                  <div v-else class="no-logo">暂无LOGO</div>
                </div>
                <!-- <span class="upload-tips">
                  支持格式：png/jpg/jpeg格式<br>
                  最大文件大小：10MB
                </span> -->
              </div>
            </div>
          </div>

          <!-- 所属供应商编码（修改为info-row格式） -->
          <div class="info-row">
            <div class="info-label">所属供应商编码</div>
            <div class="info-content">{{ target.supplier_number || '--' }}</div>
          </div>

          <!-- 所属供应商名称（修改为info-row格式） -->
          <div class="info-row">
            <div class="info-label">所属供应商名称</div>
            <div class="info-content">{{ target.supplier_name || '--' }}</div>
          </div>

          <div class="info-row">
            <div class="info-label">状态</div>
            <a-tag v-if="target.mgmt_status === 1" color="green">启用</a-tag>
            <a-tag v-else color="red">停用</a-tag>
          </div>
        </div>

        <!-- 默认制造商 -->
        <div class="form-section">
          <div class="drawer-title">默认制造商</div>

          <div class="info-row">
            <div class="info-label">制造商名称</div>
            <div class="info-content">{{ target.manufacturer_name || '--' }}</div>
          </div>

          <div class="info-row">
            <div class="info-label">MANUFACTURER</div>
            <div class="info-content">{{ target.manufacturer_name_en || '--' }}</div>
          </div>

          <div class="info-row">
            <div class="info-label">地址</div>
            <div class="info-content">{{ target.address || '--' }}</div>
          </div>

          <div class="info-row">
            <div class="info-label">ADDRESS</div>
            <div class="info-content">{{ target.address_en || '--' }}</div>
          </div>

          <div class="info-row">
            <div class="info-label">国家/地区</div>
            <div class="info-content">{{ target.country_region_name || '--' }}</div>
          </div>

          <div class="info-row">
            <div class="info-label">联系人</div>
            <div class="info-content">{{ target.contact_person || '--' }}</div>
          </div>

          <div class="info-row">
            <div class="info-label">电话</div>
            <div class="info-content">{{ target.phone || '--' }}</div>
          </div>

          <div class="info-row">
            <div class="info-label">邮箱</div>
            <div class="info-content">{{ target.email || '--' }}</div>
          </div>
        </div>

        <!-- 证书信息 -->
        <div class="form-section">
          <div class="drawer-title">证书信息</div>

          <div class="info-section">
            <!-- <div class="info-title">品牌授权书</div> -->
            <div class="info-row">
              <div class="info-label">品牌权属关系</div>
              <div class="info-content">{{ target.ownership_relation_string || '--' }}</div>
            </div>

            <div class="info-row">
              <div class="info-label">授权期限</div>
              <div class="info-content">
                <span v-if="target.auth_start_at && target.auth_end_at">
                  {{ formatDate(target.auth_start_at) }} 至 {{ formatDate(target.auth_end_at) }}
                  <span class="auth-status">
                    （授权书状态：
                    <span v-if="target.auth_status === 1" class="status-normal">正常</span>
                    <span v-else-if="target.auth_status === 2" class="status-expired">已过期</span>
                    <span v-else>--</span>
                    ）
                  </span>
                  <!-- <span class="status-dot" :class="target.auth_status === 1 ? 'dot-normal' : 'dot-expired'"></span> -->
                </span>
                <span v-else>--</span>
              </div>
            </div>

            <!-- 品牌授权书中文 -->
            <div class="info-row">
              <div class="info-label">品牌授权书(中文)</div>
              <div class="info-content">
                <div v-if="target.auth_file_id && target.auth_file_original_name" class="file-info">
                  <!-- <file-pdf-outlined class="file-icon" /> -->
                  <CheckCircleOutlined class="file-icon2" />
                  <span class="file-name">{{ target.auth_file_original_name }}</span>
                  <a-button type="link" size="small" @click="previewFile(target.auth_file_id)">预览</a-button>
                  <a-button type="link" size="small" @click="downloadFile(target.auth_file_id, target.auth_file_original_name)">下载</a-button>
                </div>
                <span v-else>--</span>
              </div>
            </div>
            <!-- 品牌授权书英文 -->
            <div class="info-row">
              <div class="info-label">品牌授权书(英文)</div>
              <div class="info-content">
                <div v-if="target.auth_file_id_en && target.auth_file_en_original_name" class="file-info">
                  <!-- <file-pdf-outlined class="file-icon" /> -->
                  <CheckCircleOutlined class="file-icon2" />
                  <span class="file-name">{{ target.auth_file_en_original_name }}</span>
                  <a-button type="link" size="small" @click="previewFile(target.auth_file_id_en)">预览</a-button>
                  <a-button type="link" size="small" @click="downloadFile(target.auth_file_id_en, target.auth_file_en_original_name)">下载</a-button>
                </div>
                <span v-else>--</span>
              </div>
            </div>
            <!-- 品牌营业执照英文 -->
            <div class="info-row">
              <div class="info-label">营业执照</div>
              <div class="info-content">
                <div v-if="target.business_license_file_id && target.business_license_file_name" class="file-info">
                  <!-- <file-pdf-outlined class="file-icon" /> -->
                  <CheckCircleOutlined class="file-icon2" />
                  <span class="file-name">{{ target.business_license_file_name }}</span>
                  <a-button type="link" size="small" @click="previewFile(target.business_license_file_id)">预览</a-button>
                  <a-button type="link" size="small" @click="downloadFile(target.business_license_file_id, target.business_license_file_name)">下载</a-button>
                </div>
                <span v-else>--</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="drawer-title">其他信息</div>

          <div class="info-section">
            <div class="info-row-group">
              <div class="info-row">
                <div class="info-label">创建时间</div>
                <div class="info-content">{{ formatDateTime(target.create_at) || '--' }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">创建人</div>
                <div class="info-content">
                  {{ target.creator || '--' }}
                  <span v-if="target.depart_of_creator || target.job_of_creator" class="user-extra">（{{ [target.depart_of_creator, target.job_of_creator].filter(Boolean).join('/') }}）</span>
                </div>
              </div>
            </div>

            <div class="info-row-group">
              <div class="info-row">
                <div class="info-label">最后修改时间</div>
                <div class="info-content">{{ formatDateTime(target.modified_at) || '--' }}</div>
              </div>
              <div class="info-row">
                <div class="info-label">最后修改人</div>
                <div class="info-content">
                  {{ target.modifier || '--' }}
                  <span v-if="target.depart_of_modifier || target.job_of_modifier" class="user-extra">（{{ [target.depart_of_modifier, target.job_of_modifier].filter(Boolean).join('/') }}）</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-form>

      <log-drawer ref="logDrawerRef" class="log" v-if="!detailLoading && appStore.isOpenLog" />
    </div>
  </a-drawer>

  <!-- 图片预览组件 -->
  <a-image
    :width="0"
    :style="{ display: 'none' }"
    :preview="{
      visible: imageVisible,
      onVisibleChange: setImageVisible,
    }"
    :src="previewImageUrl"
  />
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue'
import { LoadingOutlined, CheckCircleOutlined, EyeOutlined } from '@ant-design/icons-vue'
import { Details } from '@/servers/Brand'
import useAppStore from '@/store/modules/app'
import LogDrawer from './LogDrawer.vue'

const appStore = useAppStore()

const logDrawerRef = ref()
const detailVisible = ref(false)
const detailLoading = ref(false)
const logVisible = ref(false)

const target = ref<any>(null)
const logoImageUrl = ref('')

// 图片预览相关
const imageVisible = ref(false)
const previewImageUrl = ref('')

// 打开详情抽屉
const open = async (id: string, hasLogPermission: boolean) => {
  target.value = null
  logoImageUrl.value = ''
  detailLoading.value = true
  detailVisible.value = true
  logVisible.value = hasLogPermission

  try {
    const res = await Details({ id })
    target.value = res.data

    // 如果有LOGO，生成图片URL
    if (res.data.logo_id) {
      logoImageUrl.value = await generateLogoUrl(res.data.logo_id)
    }

    detailLoading.value = false
    nextTick(() => {
      if (hasLogPermission) {
        logDrawerRef.value?.open(target.value)
      }
    })
  } catch (error) {
    console.error('加载品牌详情失败:', error)
    detailLoading.value = false
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '--'
  return dateStr.split(' ')[0] // 只取日期部分，去掉时间
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '--'
  return dateStr // 保留完整的日期时间
}

// 生成LOGO缩略图URL
const generateLogoUrl = async (fileId: number): Promise<string> => {
  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建预览URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/ViewByFileId' : '/api/Files/ViewByFileId'

    const url = `${baseUrl}?fileId=${fileId}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: loginToken,
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    return URL.createObjectURL(blob)
  } catch (error) {
    console.error('生成LOGO缩略图失败:', error)
    return ''
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  console.error('图片加载失败:', event)
}

// 设置图片预览可见性
const setImageVisible = (visible: boolean) => {
  imageVisible.value = visible
}

// 处理LOGO预览
const handleLogoPreview = async () => {
  if (!target.value?.logo_id) {
    console.warn('没有LOGO文件ID')
    return
  }

  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建预览URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/ViewByFileId' : '/api/Files/ViewByFileId'

    const url = `${baseUrl}?fileId=${target.value.logo_id}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: loginToken,
      },
    })

    if (!response.ok) {
      console.warn('获取LOGO失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    // 检查文件类型，如果是图片则使用图片预览组件
    const contentType = response.headers.get('content-type') || ''
    if (contentType.startsWith('image/')) {
      previewImageUrl.value = previewUrl
      imageVisible.value = true
      // 30秒后释放内存
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    } else {
      // 非图片文件在新窗口打开
      window.open(previewUrl, '_blank')
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    }
  } catch (error) {
    console.error('LOGO预览失败:', error)
  }
}

// 预览授权书文件
const previewFile = async (fileId: number) => {
  if (!target.value?.auth_file_id) {
    console.warn('没有授权文件ID')
    return
  }

  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建预览URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/ViewByFileId' : '/api/Files/ViewByFileId'

    const url = `${baseUrl}?fileId=${fileId}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: loginToken,
      },
    })

    if (!response.ok) {
      console.warn('获取文件失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    // 在新窗口打开预览
    window.open(previewUrl, '_blank')
    setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
  } catch (error) {
    console.error('文件预览失败:', error)
  }
}

// 下载授权书文件
const downloadFile = async (fileId: number, auth_file_original_name: string) => {
  if (!fileId) {
    console.warn('缺少文件信息')
    return
  }

  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建下载URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/DownloadFile' : '/api/Files/DownloadFile'

    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        logintoken: loginToken,
      },
      body: JSON.stringify({
        file_id: fileId,
        original_name: auth_file_original_name,
        data_source: 'SRS',
      }),
    })

    if (!response.ok) {
      console.error('下载请求失败:', response.status)
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 获取文件blob
    const blob = await response.blob()

    // 创建下载链接
    const downloadUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = auth_file_original_name
    link.style.display = 'none'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(downloadUrl), 1000)
  } catch (error) {
    console.error('文件下载失败:', error)
  }
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.detail-form-container {
  padding: 24px; /* 关键修改：添加24px的内边距，与编辑抽屉保持一致 */
  padding-left: 30px;

  // 水平布局样式
  :deep(.ant-form-horizontal) {
    .ant-form-item {
      margin-bottom: 20px;

      .ant-form-item-label {
        padding-right: 8px;
        text-align: left;

        > label {
          font-size: 14px;
          color: #262626;
        }
      }

      .ant-form-item-control {
        .ant-form-item-control-input {
          .form-value {
            font-size: 14px;
            line-height: 32px;
            color: #262626;
          }
        }
      }
    }
  }
}

.form-section {
  margin-bottom: 32px;

  .section-title {
    padding-bottom: 8px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    border-bottom: 1px solid #f0f0f0;
  }
}

.drawer-title {
  padding-bottom: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
}

// LOGO显示行样式
.logo-upload-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;

  .logo-label {
    width: 16.7%; // 相当于 span: 4
    padding-right: 8px;
    font-size: 14px;
    line-height: 32px;
    color: #262626;
  }

  .logo-upload-content {
    flex: 1;
    width: 83.3%; // 相当于 span: 20

    .flex {
      display: flex;
      gap: 16px;
      align-items: flex-start;
    }

    .logo-display-container {
      position: relative;
    }

    .logo-preview {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 100px;
      overflow: hidden;
      background: #fafafa;
      border: 1px solid #d9d9d9;
      border-radius: 6px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .preview-actions {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(0 0 0 / 50%);
        opacity: 0;
        transition: opacity 0.3s ease;

        .preview-icon {
          padding: 4px;
          font-size: 16px;
          color: white;
          cursor: pointer;
          border-radius: 4px;
          transition: background-color 0.3s ease;

          &:hover {
            background-color: rgb(255 255 255 / 20%);
          }
        }
      }

      &:hover .preview-actions {
        opacity: 1;
      }
    }

    .no-logo {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 100px;
      font-size: 12px;
      color: #999;
      background: #fafafa;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
    }

    .upload-tips {
      margin-left: 8px;
      font-size: 12px;
      line-height: 1.5;
      color: #666;
    }
  }
}

// 新的信息展示样式
.section-header {
  padding: 8px 16px;
  margin-bottom: 0;
  font-size: 14px;
  color: #262626;
  background-color: #f5f5f5;
  border-left: 3px solid #1890ff;
}

.info-section {
  margin-bottom: 20px;
  background-color: #fff;
}

.info-title {
  padding: 12px 0;
  margin: 0;
  font-size: 14px;
  color: #262626;
}

.info-row {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
}

.info-label {
  flex-shrink: 0;
  width: 120px;
  margin-right: 16px;
  font-size: 14px;
  line-height: 22px;
  color: #262626;
}

.info-content {
  flex: 1;
  font-size: 14px;
  line-height: 22px;
  color: #262626;

  .auth-status {
    font-size: 12px;
    color: #666;
  }

  .status-normal {
    color: #52c41a;
  }

  .status-expired {
    color: #ff4d4f;
  }

  .status-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-left: 8px;
    border-radius: 50%;

    &.dot-normal {
      background-color: #52c41a;
    }

    &.dot-expired {
      background-color: #ff4d4f;
    }
  }

  .file-info {
    display: flex;
    align-items: center;

    .file-icon {
      margin-right: 8px;
      font-size: 16px;
      color: #ff4d4f;
      margin-right: 8px;
      font-size: 16px;
    }

    .file-icon2 {
      margin-right: 8px;
      font-size: 16px;
      color: #52c41a;
    }

    .file-name {
      margin-right: 8px;
      color: #262626;
    }
  }

  .user-extra {
    font-size: 12px;
    color: #8c8c8c;
  }
}

.info-row-group {
  display: flex;

  .info-row {
    flex: 1;
    padding-right: 20px;

    &:last-child {
      padding-right: 0;
    }
  }
}

.loadingIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 24px;
  color: #1890ff;
  transform: translate(-50%, -50%);
}

// 在现有样式基础上添加以下代码
:deep(.ant-form-item-control-input-content) {
  // 确保文本容器不限制高度，允许换行
  height: auto;
  min-height: 32px; // 保持与其他行的 baseline 对齐
}

.form-value {
  // 确保文本容器宽度自适应父元素
  display: inline-block;
  max-width: 100%; // 限制最大宽度为父容器宽度

  // 可选：设置行高，优化多行显示效果
  line-height: 1.5;

  // 关键：允许文本换行
  white-space: normal;
}
</style>
