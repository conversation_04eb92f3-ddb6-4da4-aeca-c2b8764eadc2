<template>
  <div class="main">
    <div class="main-content" :class="{ 'panel-collapsed': isPanelCollapsed }">
      <!-- 右侧内容区域 -->
      <div class="right-content">
        <SearchForm v-model:form="formArr" :page-type="PageType.CATEGORY_MANAGEMENT" @search="search" @setting="tableRef?.showTableSetting()" />
        <!-- 表格 -->
        <BaseTable
          ref="tableRef"
          v-model:form="formArr"
          :page-type="PageType.CATEGORY_MANAGEMENT"
          :get-list="GetCategoryList"
          :form-format="formFormat"
          :data-format="dataFormat"
          :tree-config="{
            childrenField: 'children',
            expandAll: false,
            accordion: false,
          }"
          :show-footer="false"
        >
          <!-- Custom slot templates for special column rendering -->
          <template #category_name="{ row }">
            <span>{{ row.category_name || '--' }}</span>
          </template>

          <template #number="{ row }">
            <span>{{ row.number || '--' }}</span>
          </template>

          <template #code="{ row }">
            <span>{{ row.code || '--' }}</span>
          </template>

          <template #category_template_name="{ row }">
            <span>{{ row.category_template_name || '--' }}</span>
          </template>

          <template #status="{ row }">
            <a-tag :color="row.status === 1 ? 'green' : 'red'">
              {{ row.status === 1 ? '启用' : '停用' }}
            </a-tag>
          </template>

          <template #create_at="{ row }">
            <span>{{ row.create_at ? row.create_at.slice(0, 16) : '--' }}</span>
          </template>

          <template #update_at="{ row }">
            <span>{{ row.update_at ? row.update_at.slice(0, 16) : '--' }}</span>
          </template>

          <template #operate="{ row }">
            <a-button type="text" @click="detail(row)" v-if="btnPermission[73001]">查看</a-button>
          </template>
        </BaseTable>

        <!-- 查看 -->
        <detail-drawer ref="detailDrawerRef" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import SearchForm from '@/components/SearchForm/index.vue'
import { PageType } from '@/common/enum'
import { GetCategoryList } from '@/servers/category'
import { buttonDebounce } from '@/utils/index'
import { onMounted, ref } from 'vue'
import DetailDrawer from './components/DetailDrawer.vue'

const { btnPermission } = usePermission()

// 查看
const detailDrawerRef = ref()

// 搜索表单配置
const formArr: any = ref([
  {
    label: '类目编号',
    value: '',
    type: 'batch-input',
    key: 'number',
    placeholder: '支持多编号输入查询，逗号隔开',
  },
  {
    label: '类目编码',
    value: '',
    type: 'batch-input',
    key: 'code',
    placeholder: '支持多编码输入查询，逗号隔开',
  },
  {
    label: '类目名称',
    value: null,
    type: 'input',
    key: 'category_name',
  },
  {
    label: '类目模板',
    value: null,
    type: 'input',
    key: 'category_template_name',
  },
  {
    label: '状态',
    value: 1, // 默认选中启用
    type: 'select',
    key: 'status',
    options: [
      { label: '启用', value: 1 },
      { label: '停用', value: 0 },
    ],
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'create_at',
    formKeys: ['create_start_at', 'create_end_at'],
    placeholder: ['创建开始时间', '创建结束时间'],
    maxRange: 365, // 支持一年的日期跨度
  },
  {
    label: '最后修改时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'update_at',
    formKeys: ['update_start_at', 'update_end_at'],
    placeholder: ['修改开始时间', '修改结束时间'],
    maxRange: 365, // 支持一年的日期跨度
  },
])

const isPanelCollapsed = ref(true)
const tableRef = ref()

// 初始化筛选条件
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.ATTR_MANAGEMENT) {
    const arr: any[] = []
    obj.ATTR_MANAGEMENT.forEach((x: any) => {
      formArr.value.forEach((y: any) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item: any) => {
      item.isShow = true
    })
  }
}

onMounted(() => {
  search()
  initScreening()
})

// 表单格式化
const formFormat = (obj: any) => {
  // 处理类目编号和类目编码的批量查询
  if (obj.number && typeof obj.number === 'string') {
    obj.number = obj.number.trim()
  }
  if (obj.code && typeof obj.code === 'string') {
    obj.code = obj.code.trim()
  }

  // 设置默认类型为成品
  obj.type = 1

  return { ...obj }
}

// 数据格式化 - 处理API返回的树形数据
const dataFormat = (data: any) => {
  if (!data) return []

  // API返回的数据已经是树形结构，直接使用
  if (Array.isArray(data)) {
    return data
  }

  // 如果data是对象且包含list属性
  if (data.list && Array.isArray(data.list)) {
    return data.list
  }

  return []
}

// 搜索
const search = () => {
  tableRef.value.search()
}

// 查看详情
const detail = (row: any) => {
  detailDrawerRef.value.open(row.id, false)
}

// 操作处理
const tapManipulateCore = (type: string, row: any = '') => {
  switch (type) {
    case 'add':
      // TODO: 实现新增属性功能
      console.log('新增属性')
      break
    default:
      break
  }
}
const tapManipulate = buttonDebounce(tapManipulateCore, 200)
</script>

<style lang="scss" scoped>
.main-content {
  display: flex;
  height: 100%;
  transition: all 0.3s;
}

.right-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  background-color: #fff;
}
</style>
