<template>
  <a-drawer v-model:open="visible" :width="'45vw'" :title="drawerTitle" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }" @close="handleClose">
    <template #footer>
      <a-space>
        <a-button @click="handleClose">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">保存</a-button>
      </a-space>
    </template>

    <div class="edit-form-container">
      <a-form ref="formRef" :model="formData" :rules="rules" layout="horizontal" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <!-- 基础品牌信息 -->
        <div class="form-section">
          <div class="drawer-title">基础品牌信息</div>

          <!-- 品牌编码 - 仅编辑模式显示且不可修改 -->
          <a-form-item v-if="isEdit" label="品牌编码" name="brand_number">
            <a-input v-model:value="formData.brand_number" placeholder="品牌编码" :disabled="true" readonly />
          </a-form-item>

          <a-form-item label="品牌名称" name="brand_name" required>
            <a-input v-model:value="formData.brand_name" placeholder="请输入品牌名称" :maxlength="100" />
          </a-form-item>

          <div class="logo-upload-row">
            <div class="logo-label">LOGO</div>
            <div class="logo-upload-content">
              <div class="flex">
                <a-upload
                  style="width: 100px"
                  v-model:file-list="logoFileList"
                  list-type="picture-card"
                  :before-upload="beforeLogoUpload"
                  :custom-request="handleLogoUpload"
                  @preview="handleLogoPreview"
                  @remove="handleLogoRemove"
                  @change="handleLogoChange"
                  :max-count="1"
                  accept=".png,.jpg,.jpeg"
                >
                  <div v-if="logoFileList.length === 0">
                    <plus-outlined />
                  </div>
                </a-upload>
                <span class="text-xs text-500 text-gray upload-tips">
                  支持格式：png/jpg/jpeg格式
                  <br />
                  最大文件大小：10MB
                </span>
              </div>
            </div>
          </div>

          <a-form-item label="状态" name="status" required>
            <a-switch v-model:checked="statusChecked" checked-children="启用" un-checked-children="停用" class="status-switch" @change="handleStatusChange" />
          </a-form-item>
        </div>

        <!-- 默认制造商 -->
        <div class="form-section">
          <div class="drawer-title">默认制造商</div>

          <a-form-item label="制造商名称" name="manufacturer_name">
            <a-input v-model:value="formData.manufacturer_name" placeholder="请输入制造商名称" :maxlength="100" />
          </a-form-item>

          <a-form-item label="MANUFACTURER" name="manufacturer_name_en">
            <a-input v-model:value="formData.manufacturer_name_en" placeholder="请输入MANUFACTURER" :maxlength="255" />
          </a-form-item>

          <a-form-item label="地址" name="address">
            <a-input v-model:value="formData.address" placeholder="请输入地址" :maxlength="100" />
          </a-form-item>

          <a-form-item label="ADDRESS" name="address_en">
            <a-input v-model:value="formData.address_en" placeholder="请输入ADDRESS" :maxlength="255" />
          </a-form-item>

          <a-form-item label="国家/地区" name="country">
            <a-select v-model:value="formData.country" placeholder="请选择国家/地区" show-search :filter-option="filterOption" :not-found-content="null" allow-clear @change="handleCountryChange">
              <a-select-option v-for="item in countryOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="联系人" name="contact_person">
            <a-input v-model:value="formData.contact_person" placeholder="请输入联系人" :maxlength="50" />
          </a-form-item>

          <a-form-item label="电话" name="phone">
            <a-input v-model:value="formData.phone" placeholder="请输入电话" :maxlength="20" />
          </a-form-item>

          <a-form-item label="邮箱" name="email">
            <a-input v-model:value="formData.email" placeholder="请输入邮箱" :maxlength="50" />
          </a-form-item>
        </div>

        <!-- 证书信息 -->
        <div class="form-section">
          <div class="drawer-title">证书信息</div>

          <!-- 品牌授权书上传 -->
          <div class="auth-upload-header">
            <div class="auth-upload-left">
              <span class="required-mark">*</span>
              <span class="auth-label">品牌授权书上传</span>
            </div>
            <div class="auth-upload-center">
              <info-circle-outlined class="info-icon" />
              <span class="auth-tip">请使用模板上传品牌授权书，支持 PDF 格式</span>
            </div>
            <div class="auth-upload-right">
              <span class="template-download" @click="downloadTemplate">模板下载</span>
            </div>
          </div>

          <!-- 授权期限 -->
          <div class="auth-period-row">
            <div class="auth-period-label">
              <span class="required-mark">*</span>
              <span>授权期限</span>
            </div>
            <div class="auth-period-inputs">
              <a-form-item name="auth_start_at" required style="margin-bottom: 0">
                <a-date-picker v-model:value="formData.auth_start_at" placeholder="请选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
              </a-form-item>
              <span class="date-separator">至</span>
              <a-form-item name="auth_end_at" required style="margin-bottom: 0">
                <a-date-picker v-model:value="formData.auth_end_at" placeholder="请选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
              </a-form-item>
            </div>
            <!-- 右上角查看版本记录按钮 -->
            <!-- <div v-if="isEdit && formData.id" class="version-history-btn" @click.stop="showVersionHistory">
                  查看版本记录
                </div> -->
          </div>

          <!-- 文件上传区域 -->
          <div class="file-upload-full-width">
            <div class="file-upload-section">
              <div class="upload-area" @click="triggerFileUpload" @drop="handleDrop" @dragover="handleDragOver">
                <!-- 右上角查看版本记录按钮 -->
                <!-- <div v-if="isEdit && formData.id" class="version-history-btn" @click.stop="showVersionHistory">
                  查看版本记录
                </div> -->
                <div class="upload-content">
                  <div class="upload-text">选择 拖拽或粘贴文件至此即可添加</div>
                  <div class="upload-sub-text">仅支持单个文件上传，重新上传将覆盖原文件</div>
                </div>
              </div>

              <a-upload
                ref="fileUploadRef"
                v-model:file-list="authFileList"
                name="file"
                :before-upload="beforeAuthUpload"
                :custom-request="handleAuthUpload"
                :show-upload-list="false"
                style="display: none"
              />

              <div v-if="formData.brand_file_id" class="file-info">
                <div class="file-item">
                  <!-- <file-pdf-outlined class="file-icon" /> -->
                  <CheckCircleOutlined class="file-icon2" />
                  <span class="file-name">{{ formData.brand_file_name || '授权书文件.pdf' }}</span>
                  <!-- <a-button type="link" size="small" @click="downloadFile">下载</a-button> -->
                  <a-button type="link" size="small" danger @click="removeAuthFile">删除</a-button>
                </div>
              </div>

              <!-- 当前授权文件显示 - 在上传框外面的左下角 -->
              <div v-if="isEdit && formData.auth_file_id && formData.auth_file_original_name" class="current-auth-file">
                <!-- <file-pdf-outlined class="file-icon" /> -->
                <CheckCircleOutlined class="file-icon2" />
                <span class="file-name">{{ formData.auth_file_original_name }}</span>
                <check-circle-outlined class="success-icon" />
              </div>
            </div>
          </div>
        </div>
      </a-form>
    </div>
  </a-drawer>

  <!-- 图片预览组件 -->
  <a-image
    :width="0"
    :style="{ display: 'none' }"
    :preview="{
      visible: imageVisible,
      onVisibleChange: setImageVisible,
    }"
    :src="previewImageUrl"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, InfoCircleOutlined, CheckCircleOutlined } from '@ant-design/icons-vue'
import { Add, Update, Details, UploadLogo, UploadAuthorization } from '@/servers/Brand'
import { GetCommon } from '@/servers/Common'
import { filterOption } from '@/utils/index'
import dayjs from 'dayjs'

const emit = defineEmits(['save-success'])

// 组件状态
const visible = ref(false)
const loading = ref(false)
const isEdit = ref(false)
const formRef = ref()
const fileUploadRef = ref()

// 文件上传
const logoFileList = ref<any[]>([])
const authFileList = ref<any[]>([])

// 状态开关
const statusChecked = ref(true)

// 图片预览控制
const imageVisible = ref(false)
const previewImageUrl = ref('')

// 下拉选项数据
const countryOptions = ref<any[]>([])

// 表单数据
const formData = reactive({
  id: 0,
  brand_number: '',
  brand_name: '',
  logo_id: null,
  status: '1',
  manufacturer_name: '',
  manufacturer_name_en: '',
  address: '',
  address_en: '',
  country: '',
  country_region_id: '',
  country_region_name: '',
  contact_person: '',
  phone: '',
  email: '',
  auth_start_at: null,
  auth_end_at: null,
  brand_file_id: null,
  brand_file_name: '',
  auth_file_id: null,
  auth_file_original_name: '',
})

// 表单验证规则
const rules = {
  phone: [
    {
      required: false, // 电话非必填，根据实际需求调整
      message: '请输入电话',
      trigger: 'blur',
    },
    {
      pattern: /^(\d{3,4}-)?\d{7,8}$|^1[3-9]\d{9}$/, // 支持固定电话和手机号
      message: '请输入正确的电话格式（如：010-12345678 或 13800138000）',
      trigger: 'blur',
    },
  ],
  brand_name: [
    { required: true, message: '请输入品牌名称', trigger: 'blur' },
    { max: 50, message: '品牌名称不能超过50个字符', trigger: 'blur' },
  ],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  manufacturer_name: [
    { message: '请输入制造商名称', trigger: 'blur' },
    { max: 100, message: '制造商名称不能超过100个字符', trigger: 'blur' },
  ],
  email: [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }],
  auth_start_at: [
    {
      required: true,
      message: '请选择授权开始时间',
      trigger: 'change',
    },
  ],
  auth_end_at: [
    {
      required: true,
      message: '请选择授权结束时间',
      trigger: 'change',
      validator: (_rule: any, value: any) => {
        if (!value) return Promise.reject(new Error('请选择授权结束时间'))
        if (formData.auth_start_at && dayjs(value).isBefore(dayjs(formData.auth_start_at))) {
          return Promise.reject(new Error('结束时间不能早于开始时间'))
        }
        return Promise.resolve()
      },
    },
  ],
}

// 计算属性
const drawerTitle = computed(() => {
  return isEdit.value ? '编辑品牌' : '新建品牌'
})

// 状态开关变化处理
const handleStatusChange = (checked: boolean) => {
  formData.status = checked ? '1' : '0'
}

// 国家/地区选择变化处理
const handleCountryChange = (value: string) => {
  if (value) {
    // 根据选择的值找到对应的国家/地区信息
    const selectedCountry = countryOptions.value.find((item) => item.value === value)
    if (selectedCountry) {
      formData.country = value
      formData.country_region_id = selectedCountry.value
      formData.country_region_name = selectedCountry.label
    }
  } else {
    // 清空选择
    formData.country = ''
    formData.country_region_id = ''
    formData.country_region_name = ''
  }
}

// 打开抽屉
const open = async (type: string, row?: any) => {
  isEdit.value = type === 'edit'
  visible.value = true

  if (isEdit.value && row) {
    // 编辑模式，加载数据
    await loadBrandDetails(row.id)
  } else {
    // 新建模式，重置表单
    resetForm()
  }
}

// 加载品牌详情
const loadBrandDetails = async (id: string) => {
  try {
    const res = await Details({ id })
    const data = res.data

    Object.assign(formData, {
      id: data.id,
      brand_number: data.brand_number,
      brand_name: data.brand_name,
      logo_id: data.logo_id,
      status: data.status,
      manufacturer_name: data.manufacturer_name,
      manufacturer_name_en: data.manufacturer_name_en,
      address: data.address,
      address_en: data.address_en,
      country: data.country_region_id ? String(data.country_region_id) : data.country || '',
      country_region_id: data.country_region_id || data.country,
      country_region_name: data.country_region_name || '',
      contact_person: data.contact_person,
      phone: data.phone,
      email: data.email,
      auth_start_at: data.auth_start_at ? dayjs(data.auth_start_at) : null,
      auth_end_at: data.auth_end_at ? dayjs(data.auth_end_at) : null,
      brand_file_id: data.brand_file_id,
      brand_file_name: data.auth_file_original_name,
      auth_file_id: data.auth_file_id,
      auth_file_original_name: data.auth_file_original_name,
    })

    // 更新状态开关
    statusChecked.value = data.status === '启用' || data.status === 1

    // 如果有LOGO，设置文件列表
    if (data.logo_id) {
      const thumbnailUrl = await generateThumbnailUrl(data.logo_id)
      logoFileList.value = [
        {
          uid: data.logo_id.toString(),
          name: data.brand_name || 'logo',
          status: 'done',
          url: thumbnailUrl,
          thumbUrl: thumbnailUrl,
          response: { id: data.logo_id },
        },
      ]
    }

    // 如果有授权文件，设置文件列表
    if (data.brand_file_id) {
      authFileList.value = [
        {
          uid: data.brand_file_id.toString(),
          name: data.brand_file_name || '授权文件',
          status: 'done',
          url: `/api/Files/ViewByFileId?fileId=${data.brand_file_id}`,
          response: { id: data.brand_file_id },
        },
      ]
    }
  } catch (error) {
    message.error('加载品牌详情失败')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: 0,
    brand_number: '',
    brand_name: '',
    logo_id: null,
    status: '1',
    manufacturer_name: '',
    manufacturer_name_en: '',
    address: '',
    address_en: '',
    country: '',
    country_region_id: '',
    country_region_name: '',
    contact_person: '',
    phone: '',
    email: '',
    auth_start_at: null,
    auth_end_at: null,
    brand_file_id: null,
    brand_file_name: '',
    auth_file_id: null,
    auth_file_original_name: '',
  })

  // 清理生成的URL，防止内存泄漏
  logoFileList.value.forEach((file) => {
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }
    if (file.thumbUrl && file.thumbUrl.startsWith('blob:')) {
      URL.revokeObjectURL(file.thumbUrl)
    }
  })

  logoFileList.value = []
  authFileList.value = []
  statusChecked.value = true

  // 清除表单验证
  formRef.value?.clearValidate()
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 自定义验证：检查授权书是否已上传
    // 新建模式检查brand_file_id，编辑模式检查brand_file_id或auth_file_id
    const hasAuthFile = formData.brand_file_id || (isEdit.value && formData.auth_file_id)
    if (!hasAuthFile) {
      message.error('请上传授权书文件')
      return
    }

    loading.value = true

    // 准备提交数据
    const submitData = {
      ...formData,
      auth_start_at: formData.auth_start_at ? dayjs(formData.auth_start_at).format('YYYY-MM-DD HH:mm:ss') : null,
      auth_end_at: formData.auth_end_at ? dayjs(formData.auth_end_at).format('YYYY-MM-DD HH:mm:ss') : null,
    }

    // 新增模式：不传id，修改模式：传id
    if (isEdit.value) {
      // 编辑模式，保留id
      console.log('编辑品牌，提交数据:', submitData)
      await Update(submitData)
      message.success('编辑成功')
    } else {
      // 新增模式，移除id字段（无论id是0还是其他值）
      const { id, ...addData } = submitData
      console.log('新增品牌，提交数据:', addData)
      await Add(addData)
      message.success('新建成功')
    }

    emit('save-success')
    handleClose()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    loading.value = false
  }
}

// LOGO上传前检查
const beforeLogoUpload = (file: File) => {
  console.log('文件验证开始:', {
    fileName: file.name,
    fileType: file.type,
    fileSize: file.size,
  })

  // 检查文件扩展名，只允许 png/jpg/jpeg
  const fileName = file.name.toLowerCase()
  const allowedExtensions = ['.png', '.jpg', '.jpeg']
  const hasValidExtension = allowedExtensions.some((ext) => fileName.endsWith(ext))

  if (!hasValidExtension) {
    console.log('文件扩展名验证失败:', fileName)
    message.error('只能上传 PNG/JPG/JPEG 格式的图片文件!')
    // 清空文件列表，确保无效文件不会显示
    setTimeout(() => {
      logoFileList.value = []
    }, 0)
    return false
  }

  // 检查MIME类型，双重验证
  const allowedTypes = ['image/png', 'image/jpeg']
  const isValidMimeType = allowedTypes.includes(file.type.toLowerCase())

  if (!isValidMimeType) {
    console.log('MIME类型验证失败:', file.type)
    message.error('只能上传 PNG/JPG/JPEG 格式的图片文件!')
    // 清空文件列表，确保无效文件不会显示
    setTimeout(() => {
      logoFileList.value = []
    }, 0)
    return false
  }

  // 检查文件大小，不能超过10MB
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    console.log('文件大小验证失败:', file.size / 1024 / 1024, 'MB')
    message.error('图片大小不能超过10MB!')
    // 清空文件列表，确保无效文件不会显示
    setTimeout(() => {
      logoFileList.value = []
    }, 0)
    return false
  }

  console.log('文件验证通过')
  return true // 允许上传
}

// 处理LOGO上传
const handleLogoUpload = async (options: any) => {
  try {
    const uploadData = new FormData()
    uploadData.append('files', options.file) // 注意：API需要的字段名是 'files'

    const res = await UploadLogo(uploadData)
    if (res.data && res.data.length > 0) {
      formData.logo_id = res.data[0].id

      // 生成缩略图URL
      const thumbnailUrl = await generateThumbnailUrl(res.data[0].id)

      // 更新文件列表，用于显示
      logoFileList.value = [
        {
          uid: res.data[0].id.toString(),
          name: res.data[0].original_name || options.file.name,
          status: 'done',
          url: thumbnailUrl, // 使用生成的缩略图URL
          thumbUrl: thumbnailUrl, // Ant Design需要的缩略图字段
          response: res.data[0],
        },
      ]

      message.success('LOGO上传成功')
    } else {
      message.error('LOGO上传失败：返回数据格式错误')
    }
  } catch (error) {
    console.error('LOGO上传失败:', error)
    message.error('LOGO上传失败')
  }
}

// 图片预览控制
const setImageVisible = (visible: boolean) => {
  imageVisible.value = visible
}

// 处理LOGO预览
const handleLogoPreview = async (file: any) => {
  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建预览URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/ViewByFileId' : '/api/Files/ViewByFileId'

    const fileId = file.response?.id || formData.logo_id
    const url = `${baseUrl}?fileId=${fileId}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: loginToken,
      },
    })

    if (!response.ok) {
      message.warning('获取LOGO失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    // 检查文件类型，如果是图片则使用图片预览组件
    const contentType = response.headers.get('content-type') || ''
    if (contentType.startsWith('image/')) {
      previewImageUrl.value = previewUrl
      imageVisible.value = true
      // 30秒后释放内存
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    } else {
      // 非图片文件在新窗口打开
      window.open(previewUrl, '_blank')
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    }
  } catch (error) {
    console.error('LOGO预览失败:', error)
    message.error('LOGO预览失败')
  }
}

// 处理LOGO删除
const handleLogoRemove = () => {
  formData.logo_id = null
  logoFileList.value = []
  message.success('LOGO已删除')
}

// 处理LOGO文件变化
const handleLogoChange = (info: any) => {
  console.log('文件变化事件:', info)

  // 如果文件状态是error或者removed，直接返回
  if (info.file.status === 'error' || info.file.status === 'removed') {
    return
  }

  // 如果是新添加的文件，进行验证
  if (info.file.status === 'uploading' || !info.file.status) {
    const file = info.file.originFileObj || info.file

    // 再次验证文件格式
    const fileName = file.name.toLowerCase()
    const allowedExtensions = ['.png', '.jpg', '.jpeg']
    const hasValidExtension = allowedExtensions.some((ext) => fileName.endsWith(ext))

    if (!hasValidExtension) {
      console.log('在change事件中检测到无效文件:', fileName)
      message.error('只能上传 PNG/JPG/JPEG 格式的图片文件!')

      // 立即从文件列表中移除无效文件
      logoFileList.value = logoFileList.value.filter((item) => item.uid !== info.file.uid)
      return
    }

    // 验证MIME类型
    const allowedTypes = ['image/png', 'image/jpeg']
    if (file.type && !allowedTypes.includes(file.type.toLowerCase())) {
      console.log('在change事件中检测到无效MIME类型:', file.type)
      message.error('只能上传 PNG/JPG/JPEG 格式的图片文件!')

      // 立即从文件列表中移除无效文件
      logoFileList.value = logoFileList.value.filter((item) => item.uid !== info.file.uid)
    }
  }
}

// 生成缩略图URL
const generateThumbnailUrl = async (fileId: number): Promise<string> => {
  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建预览URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/ViewByFileId' : '/api/Files/ViewByFileId'

    const url = `${baseUrl}?fileId=${fileId}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: loginToken,
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    return URL.createObjectURL(blob)
  } catch (error) {
    console.error('生成缩略图失败:', error)
    return ''
  }
}

// 授权书上传前检查
const beforeAuthUpload = (file: File) => {
  const isPdf = file.type === 'application/pdf'
  if (!isPdf) {
    message.error('只能上传PDF文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }

  return true // 允许上传
}

// 处理授权书上传
const handleAuthUpload = async (options: any) => {
  try {
    const uploadData = new FormData()
    uploadData.append('files', options.file) // 注意：API需要的字段名是 'files'

    const res = await UploadAuthorization(uploadData)
    if (res.data && res.data.length > 0) {
      // 更新品牌文件信息（用于提交）
      formData.brand_file_id = res.data[0].id
      formData.brand_file_name = options.file.name

      // 更新授权文件信息（用于显示）
      formData.auth_file_id = res.data[0].id
      formData.auth_file_original_name = options.file.name

      message.success('授权书上传成功')
    } else {
      message.error('授权书上传失败：返回数据格式错误')
    }
  } catch (error) {
    console.error('授权书上传失败:', error)
    message.error('授权书上传失败')
  }
}

// 下载模板文件
const downloadTemplate = () => {
  try {
    // 使用public目录中的静态文件
    const templateUrl = '/templates/品牌授权书一级模板（中文）.docx'

    // 创建一个临时的a标签来触发下载
    const link = document.createElement('a')
    link.href = templateUrl
    link.download = '品牌授权书一级模板（中文）.docx'
    link.style.display = 'none'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    message.success('模板下载成功')
  } catch (error) {
    console.error('模板下载失败:', error)
    message.error('模板下载失败')
  }
}

// 删除授权书文件
const removeAuthFile = () => {
  // 清理品牌文件信息（用于提交）
  formData.brand_file_id = null
  formData.brand_file_name = ''

  // 清理授权文件信息（用于显示）
  formData.auth_file_id = null
  formData.auth_file_original_name = ''
}

// 触发文件上传
const triggerFileUpload = () => {
  fileUploadRef.value?.$el.querySelector('input[type="file"]')?.click()
}

// 处理拖拽上传
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()

  const files = e.dataTransfer?.files
  if (files && files.length > 0) {
    const file = files[0]
    if (beforeAuthUpload(file)) {
      handleAuthUpload({ file })
    }
  }
}

// 获取下拉选项数据
const getDropdownOptions = async () => {
  try {
    const res = await GetCommon()
    if (res.success) {
      // 获取国家/地区列表
      countryOptions.value =
        res.data.countryregion_list
          ?.filter((item: any) => item && item.label && item.value)
          .map((item: any) => ({
            label: item.label,
            value: String(item.value), // 确保value是字符串类型
          })) || []
    }
  } catch (error) {
    console.error('获取下拉选项失败:', error)
  }
}

// 组件挂载时获取下拉选项
onMounted(() => {
  getDropdownOptions()
})

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.edit-form-container {
  padding: 0 8px;

  // 水平布局样式
  :deep(.ant-form-horizontal) {
    .ant-form-item {
      margin-bottom: 20px;

      .ant-form-item-label {
        padding-right: 8px;
        text-align: left;

        > label {
          font-size: 14px;
          color: #262626;

          &.ant-form-item-required::before {
            color: #ff4d4f;
          }
        }
      }

      .ant-form-item-control {
        .ant-form-item-control-input {
          .ant-input,
          .ant-select,
          .ant-date-picker {
            width: 100%;
          }
        }
      }
    }
  }
}

.form-section {
  margin-bottom: 32px;

  .section-title {
    padding-bottom: 8px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    border-bottom: 1px solid #f0f0f0;
  }
}

// LOGO上传行样式
.logo-upload-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;

  .logo-label {
    width: 16.67%; // 相当于 span: 4
    padding-right: 8px;
    font-size: 14px;
    line-height: 32px;
    color: #262626;
  }

  .logo-upload-content {
    flex: 1;
    width: 83.34%; // 相当于 span: 20

    .flex {
      display: flex;
      gap: 16px;
      align-items: flex-start;
    }

    .upload-tips {
      margin-left: 8px;
      font-size: 12px;
      line-height: 1.5;
      color: #666;
    }

    // 确保上传组件的样式正确
    :deep(.ant-upload-list-picture-card .ant-upload-list-item) {
      width: 100px;
      height: 100px;
    }

    :deep(.ant-upload-select-picture-card) {
      width: 100px;
      height: 100px;
    }
  }
}

.upload-section {
  .logo-uploader {
    :deep(.ant-upload) {
      width: 80px;
      height: 80px;
    }
  }

  .logo-preview {
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
  }

  .upload-tips {
    margin-top: 8px;
    font-size: 12px;
    line-height: 1.4;
    color: #999;
  }
}

.status-switch {
  :deep(.ant-switch-checked) {
    background-color: #52c41a;
  }

  :deep(.ant-switch) {
    background-color: #ff4d4f;
  }
}

// 证书信息样式
.auth-upload-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .auth-upload-left {
    display: flex;
    align-items: center;

    .required-mark {
      margin-right: 4px;
      font-size: 14px;
      color: #ff4d4f;
    }

    .auth-label {
      font-size: 14px;
      color: #262626;
    }
  }

  .auth-upload-center {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center; // 新增，让子元素水平居中
    margin-left: 24px;

    .info-icon {
      margin-right: 6px;
      font-size: 14px;
      color: #999;
    }

    .auth-tip {
      font-size: 12px;
      color: #999;
    }
  }

  .auth-upload-right {
    .template-download {
      font-size: 12px;
      color: #1890ff;
      cursor: pointer;

      &:hover {
        color: #40a9ff;
      }
    }
  }
}

// 授权期限行样式
.auth-period-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .auth-period-label {
    display: flex;
    align-items: center;
    width: 16.67%; // 相当于 span: 4
    padding-right: 8px;
    font-size: 14px;
    color: #262626;

    .required-mark {
      margin-right: 4px;
      font-size: 14px;
      color: #ff4d4f;
    }
  }

  .auth-period-inputs {
    display: flex;
    flex: 1;
    align-items: center;
    width: 83.34%; // 相当于 span: 20

    .date-separator {
      margin: 0 12px;
      font-size: 14px;
      color: #262626;
    }

    :deep(.ant-form-item) {
      margin-bottom: 0;
    }

    :deep(.ant-date-picker) {
      width: 150px;
    }
  }
}

// 文件上传全宽度容器
.file-upload-full-width {
  width: 100%;
  margin-bottom: 20px;
}

.file-upload-section {
  .upload-area {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    transition: border-color 0.3s ease;

    &:hover {
      border-color: #1890ff;
    }

    .version-history-btn {
      position: absolute;
      top: 8px;
      right: 12px;
      padding: 4px 8px;
      font-size: 12px;
      color: #1890ff;
      cursor: pointer;
      background: rgb(24 144 255 / 10%);
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        color: #40a9ff;
        background: rgb(24 144 255 / 20%);
      }
    }

    .upload-content {
      .upload-text {
        margin-bottom: 8px;
        font-size: 14px;
        color: #262626;
      }

      .upload-sub-text {
        font-size: 12px;
        color: #999;
      }
    }
  }

  .file-info {
    margin-top: 12px;
  }

  .file-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 6px;

    .file-icon {
      margin-right: 8px;
      font-size: 16px;
      color: #ff4d4f;
    }

    .file-icon2 {
      margin-right: 8px;
      font-size: 16px;
      color: #52c41a;
    }

    .file-name {
      flex: 1;
      margin-right: 8px;
      font-size: 14px;
    }
  }

  .upload-tips {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
  }

  // 当前授权文件显示样式
  .current-auth-file {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-top: 12px;
    font-size: 14px;
    background: #f6ffed;

    // border: 1px solid #b7eb8f;
    border-radius: 6px;

    .file-icon {
      margin-right: 8px;
      font-size: 16px;
      color: #ff4d4f;
    }

    .file-name {
      flex: 1;
      margin-right: 8px;
      overflow: hidden;
      color: #262626;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .success-icon {
      font-size: 16px;
      color: #52c41a;
    }
  }
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}
</style>
