<template>
  <div class="logoutMainBox">
    <div class="logoutBox">
      <div class="titleBox">
        <img src="../assets/image/logo-o.png" alt="" />
        <div>
          <div>退出登录</div>
          <div>供应商后台</div>
        </div>
      </div>

      <!-- 处理状态显示 -->
      <div class="status-container">
        <div v-if="processing" class="processing-status">
          <div class="loading-icon">
            <div class="spinner"></div>
          </div>
          <div class="status-text">
            <div class="main-text">正在处理退出登录...</div>
            <div class="sub-text">请稍候</div>
          </div>
        </div>

        <div v-else class="success-status">
          <div class="success-icon">
            <div class="check-icon">✓</div>
          </div>
          <div class="status-text">
            <div class="main-text">退出登录成功</div>
            <div class="sub-text">您已成功退出系统</div>
          </div>
        </div>
      </div>

      <!-- 重新登录按钮 -->
      <a-button v-if="!processing" @click="goToLogin" class="relogin-btn">重新登录</a-button>
    </div>

    <div class="beian">
      <span>© 广东云跨易网络科技有限公司 版权所有</span>
      <div class="line"></div>
      <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备2024317142号-1</a>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { LogoutCallback, LoginRedirect } from '@/servers/UmcAuth'
import { message } from 'ant-design-vue/es'
import { beforLogout } from '@/utils'

const processing = ref(true) // 控制是否正在处理

onMounted(() => {
  handleUmcLogoutCallback()
})
// 动态获取当前页面的IP和端口
const getCurrentHostInfo = () => {
  const protocol = window.location.protocol // http: 或 https:
  const hostname = window.location.hostname // IP地址或域名
  const port = window.location.port // 端口号

  // 构建完整的主机信息
  let hostInfo = `${protocol}//${hostname}`
  if (port) {
    hostInfo += `:${port}`
  }

  return hostInfo
}
// 跳转到登录页
const goToLogin = async () => {
  // router.push('/login')
  // window.location.href  = `${import.meta.env.VITE_APP_BASE_API}/XY/UmcAuth/LoginRedirect`
  // 调用LoginRedirect接口获取登录地址
  let ipParam: string | undefined
  if (import.meta.env.VITE_APP_ENV === 'development') {
    // 开发环境：传递当前页面的IP和端口
    ipParam = getCurrentHostInfo()
    console.log('开发环境，传递IP参数:', ipParam)
  }
  const response = await LoginRedirect(ipParam)

  if (response.success && response.data) {
    // 有登录地址，直接跳转

    console.log('获取到UMC登录地址:', response.data)
    window.location.href = response.data
  } else {
    // 没有登录地址或接口失败，显示错误信息
    const errorMessage = response.message || '获取登录地址失败'
    message.error(errorMessage)
    console.error('LoginRedirect接口失败:', response)
  }
}

// 处理UMC退出登录回调
const handleUmcLogoutCallback = async () => {
  try {
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('处理UMC退出登录回调')
    }

    // 优先从sessionStorage获取LoginToken，如果没有再从localStorage获取
    let LoginToken = sessionStorage.getItem('logoutLoginToken') || ''

    if (!LoginToken) {
      let userData = localStorage.getItem('userData') || ''
      if (userData) {
        userData = JSON.parse(userData) as any
        LoginToken = (userData as any).login_token || ''
      }
    }

    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('退出登录回调 - 检查LoginToken:', LoginToken ? `${LoginToken.substring(0, 20)}...` : '无')
      console.log('LoginToken来源:', sessionStorage.getItem('logoutLoginToken') ? 'sessionStorage' : 'localStorage')
    }

    // 如果没有LoginToken，说明localStorage已被清除，直接完成退出登录流程
    if (!LoginToken) {
      if (import.meta.env.VITE_APP_ENV === 'development') {
        console.log('LoginToken已不存在，直接完成退出登录流程')
      }

      // 显示退出成功消息
      message.success('退出登录成功')

      // 停止处理状态，显示成功页面
      processing.value = false
      return
    }

    // 获取URL中的UMC参数
    const urlParams = new URLSearchParams(window.location.search)
    const umcParams = {
      app_key: urlParams.get('app_key') || '',
      timestamp: urlParams.get('timestamp') || '',
      signature: urlParams.get('signature') || '',
      accountId: urlParams.get('accountId') || urlParams.get('accountld') || '', // 注意可能是accountld
    }

    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('UMC回调参数:', umcParams)
    }

    // 调用退出登录回调接口，传递UMC参数和LoginToken
    const res = await LogoutCallback(umcParams, LoginToken)

    if (res.success) {
      // 清理本地数据
      beforLogout()

      // 清理sessionStorage中的LoginToken
      sessionStorage.removeItem('logoutLoginToken')

      // 显示退出成功消息
      message.success('退出登录成功')

      // 停止处理状态，显示成功页面
      processing.value = false
    } else {
      // 即使接口失败，也要清理本地数据
      beforLogout()

      // 清理sessionStorage中的LoginToken
      sessionStorage.removeItem('logoutLoginToken')

      message.success(res.message || '退出登录完成')

      // 接口失败时停止处理并显示成功页面（因为本地数据已清理）
      processing.value = false
    }
  } catch (error) {
    console.error('UMC退出登录回调处理失败:', error)

    // 即使出错，也要清理本地数据
    beforLogout()

    // 清理sessionStorage中的LoginToken
    sessionStorage.removeItem('logoutLoginToken')

    message.success('退出登录完成')

    // 出错时停止处理并显示成功页面（因为本地数据已清理）
    processing.value = false
  }
}
</script>

<style lang="scss" scoped>
.logoutMainBox {
  position: relative;
  width: 100%;
  min-width: 1180px;
  height: 100vh;
  background-color: #fff;
  background-image: url('../assets/image/login-bg.webp');
  background-size: cover;

  .logoutBox {
    position: absolute;
    top: 0;
    right: 360px;
    bottom: 0;
    width: 480px;
    height: 490px;
    padding: 60px;
    margin: auto;
    background: rgb(255 255 255 / 90%);
    border-radius: 10px;
    box-shadow: 0 0 20px 0 rgb(0 0 0 / 10%);

    .titleBox {
      display: flex;
      gap: 16px;
      align-items: center;
      margin-bottom: 40px;
      font-size: 18px;
      font-weight: 400;
      line-height: 30px;
      color: #1a1a1a;

      img {
        width: 60px;
        height: 60px;
      }
    }

    .status-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30px;

      .processing-status,
      .success-status {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .loading-icon {
        margin-bottom: 16px;

        .spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #409eff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }

      .success-icon {
        margin-bottom: 16px;

        .check-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          font-size: 24px;
          font-weight: bold;
          color: white;
          background: #52c41a;
          border-radius: 50%;
        }
      }

      .status-text {
        text-align: center;

        .main-text {
          margin-bottom: 4px;
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }

        .sub-text {
          font-size: 14px;
          color: #666;
        }
      }
    }

    .relogin-btn {
      width: 100%;
      height: 44px;
      font-size: 16px;
      font-weight: 500;
      color: #fff;
      background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
      border: none;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover:not(.loading) {
        background: linear-gradient(135deg, #36a3f7 0%, #409eff 100%);
        box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
        transform: translateY(-1px);
      }
    }
  }

  .beian {
    position: absolute;
    right: 0;
    bottom: 30px;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    font-size: 12px;
    color: #999;

    .line {
      position: relative;
      top: 1px;
      width: 1px;
      height: 10px;
      margin: 0 12px;
      background: #999;
    }

    a {
      color: #999;
      text-decoration: none;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
