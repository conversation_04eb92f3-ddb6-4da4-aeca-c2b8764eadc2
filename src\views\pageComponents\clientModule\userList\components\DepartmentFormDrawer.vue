<template>
  <a-drawer
    v-model:open="visible"
    :title="isEdit ? '编辑部门' : '新建部门'"
    width="500px"
    placement="right"
    :maskClosable="false"
    :closable="true"
    @close="handleClose"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      layout="horizontal"
    >
    <a-form-item label="部门类型" name="type">
        <a-select
          v-model:value="formData.type"
          placeholder="请选择部门类型"
          :options="departmentTypeOptions"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="部门名称" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入部门名称"
          :maxlength="50"
          show-count
        />
      </a-form-item>

       <a-form-item label="所属企业" name="company_id">
        <a-tree-select
          v-model:value="formData.company_id"
          :tree-data="companyOptions"
          :fieldNames="{ label: 'department_name', value: 'id', children: 'childs' }"
          placeholder="选择所属企业"
          tree-default-expand-all
          
          
        />
      </a-form-item>

      <a-form-item label="上级部门" name="p_id">
        <a-tree-select
          v-model:value="formData.p_id"
          :tree-data="parentDeptOptions"
          :fieldNames="{ label: 'department_name', value: 'id', children: 'childs' }"
          placeholder="选择上级部门"
          allow-clear
          tree-default-expand-all
          
        />
      </a-form-item>

     

      <a-form-item label="部门负责人" name="header_ids">
        <a-select
          v-model:value="formData.header_ids"
          mode="multiple"
          placeholder="选择部门负责人"
          :options="userOptions"
          :field-names="{ label: 'real_name', value: 'id' }"
          show-search
          :filter-option="filterUserOption"
          allow-clear
        />
      </a-form-item>

      <!-- <a-form-item label="OA系统ID" name="oa_id">
        <a-input
          v-model:value="formData.oa_id"
          placeholder="请输入OA系统ID（可选）"
          :maxlength="20"
        />
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注信息（可选）"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </a-form-item> -->
    </a-form>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <a-button @click="handleClose">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance, Rule } from 'ant-design-vue'
import { 
  AddDepartment, 
  EditDepartment, 
  GetCompanyTree,
  type TreeNode 
} from '@/servers/CompanyArchitecture'
import { GetList as GetUserList } from '@/servers/UserManagerNew'

// Props
interface Props {
  // 父部门ID，如果指定则为添加子部门
  parentId?: string
  // 当前选中的企业ID
  companyId?: string
}

const props = withDefaults(defineProps<Props>(), {
  parentId: '',
  companyId: ''
})

// Emits
const emit = defineEmits<{
  'success': []
  'close': []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const isEdit = ref(false)
const editId = ref('')

const formRef = ref<FormInstance>()
const formData = reactive({
  name: '',
  type: '',
  p_id: '',
  company_id: '',
  header_ids: [] as string[],
  oa_id: '',
  remark: ''
})

// 选项数据
const parentDeptOptions = ref<TreeNode[]>([])
const companyOptions = ref<TreeNode[]>([])
const userOptions = ref<any[]>([])

// 部门类型选项
const departmentTypeOptions = ref([
  { label: '部门', value: '3' },
  { label: '单位(分公司)', value: '2' },
 
])

// 表单验证规则
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度为2-50个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择部门类型', trigger: 'change' }
  ],
  company_id: [
    { required: true, message: '请选择所属企业', trigger: 'change' }
  ]
}

// 计算属性
const parentId = computed(() => props.parentId)

// 监听器
watch(
  () => formData.company_id,
  (newVal) => {
    if (newVal) {
      loadUserOptions(newVal)
    } else {
      userOptions.value = []
    }
  }
)

// 方法定义
const open = async (editData?: any) => {
  visible.value = true
  isEdit.value = !!editData
  editId.value = editData?.id || ''
  
  // 加载基础数据
  await loadCompanyOptions()
  await loadParentDeptOptions()
  
  if (editData) {
    // 编辑模式，填充数据
    Object.assign(formData, {
      name: editData.department_name || '',
      type: editData.type || '',
      p_id: editData.p_id || '',
      company_id: editData.company_id || '',
      header_ids: editData.header_ids || [],
      oa_id: editData.oa_id || '',
      remark: editData.remark || ''
    })
  } else {
    // 新建模式，设置默认值
    resetForm()
    
    
    if (parentId.value) {
      formData.p_id = parentId.value
    }
    if (props.companyId) {
      // formData.company_id = props.companyId
    }
  }
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: '',
    p_id: '',
    company_id: '',
    header_ids: [],
    oa_id: '',
    remark: ''
  })
  formRef.value?.clearValidate()
}

const loadCompanyOptions = async () => {
  try {
    const res = await GetCompanyTree({ id: '' })
    const data = res.data || []
    
    // 只显示企业节点
    const filterCompanies = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.filter(node => node.type === 1).map(node => ({
        ...node,
        childs: node.childs ? filterCompanies(node.childs) : []
      }))
    }
    
    companyOptions.value = filterCompanies(data)
    console.log('companyOptions',companyOptions.value);
    
  } catch (error) {
    console.error('加载企业选项失败:', error)
    message.error('加载企业选项失败')
  }
}

const loadParentDeptOptions = async () => {
  try {
    const res = await GetCompanyTree({ id: '' })
    const data = res.data || []
    
    // 过滤掉当前编辑的部门及其子部门
    const filterDepts = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.filter(node => {
        if (isEdit.value && node.id === editId.value) {
          return false
        }
        return true
      }).map(node => ({
        ...node,
        childs: node.childs ? filterDepts(node.childs) : []
      }))
    }
    
    parentDeptOptions.value = filterDepts(data)
  } catch (error) {
    console.error('加载部门选项失败:', error)
    message.error('加载部门选项失败')
  }
}

const loadUserOptions = async (companyId: string) => {
  try {
    const res = await GetUserList({
      page: 1,
      pageSize: 1000,
      company_id: companyId
    })
    userOptions.value = res.data?.list || []
  } catch (error) {
    console.error('加载用户选项失败:', error)
    message.error('加载用户选项失败')
  }
}

const filterUserOption = (input: string, option: any) => {
  return option.real_name?.toLowerCase().includes(input.toLowerCase())
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    const submitData = {
      id: isEdit.value ? editId.value : '',
      p_id: formData.p_id,
      company_id: formData.company_id,
      name: formData.name,
      type: formData.type,
      header_ids: formData.header_ids,
      oa_id: formData.oa_id
    }
    
    if (isEdit.value) {
      await EditDepartment(submitData)
      message.success('部门更新成功')
    } else {
      await AddDepartment(submitData)
      message.success('部门创建成功')
    }
    
    emit('success')
    handleClose()
  } catch (error: any) {
    console.error('提交失败:', error)
    const errorMessage = error?.message || (isEdit.value ? '部门更新失败' : '部门创建失败')
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
  emit('close')
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
:deep(.ant-drawer-body) {
  padding: 24px;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-select-selector) {
  min-height: 32px;
}

:deep(.ant-tree-select-selector) {
  min-height: 32px;
}
</style>
