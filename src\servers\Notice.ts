import { request } from './request'

export const Detail = (data) => request({ url: '/notice/detail', data })

export const Log = (data) => request({ url: '/api/Notice/GetOpLogInfos', data })

// === 以下接口已迁移到新的 /XY/Notify/ 路径，使用新的 Notify.ts 文件 ===

// 添加系统通知 - 使用新接口
export { Add as AddNotify } from './Notify'
// 编辑系统通知 - 使用新接口
export { Update as EditNotify } from './Notify'
// 删除系统通知 - 使用新接口
export { Delete as DeleteNotify } from './Notify'
// 获取系统通知列表 - 使用新接口
export { GetList as GetNotifyList } from './Notify'
// 撤回发布 - 使用新接口
export { Revocation as RevocationNotify } from './Notify'
// 获取通知相关下拉框 - 使用新接口
export { GetDropsSource } from './Notify'
// 保存并发布 - 使用新接口
export { SaveAndPublish } from './Notify'
// 发布通知 - 使用新接口
export { Publish } from './Notify'
// 查看通知 - 使用新接口
export { GetNotifyDetails as GetNotice } from './Notify'

// === 以下接口已迁移到新的 /XY/Notify/ 路径，使用新的 Notify.ts 文件 ===

// 获取通知列表 - 使用新接口
export { GetnoticeList } from './Notify'
// 新增保存系统通知 - 使用新接口
export { AddNotification } from './Notify'
// 编辑保存系统通知 - 使用新接口
export { UpdateNotification } from './Notify'
// 撤回系统通知 - 使用新接口
export { revocation } from './Notify'
// 删除系统通知 - 使用新接口
export { deleteNotification } from './Notify'
// 获取最新通知 - 使用新的 Notify 接口
export { GetNewNotify } from './Notify'
// 批量发布系统通知 - 使用新接口
export { BatchPublishNotify } from './Notify'
// 获取系统通知详情 - 使用新接口
export { DetailByEdit } from './Notify'
// 关闭系统通知 - 使用新的 Common 接口
export { ReadNotice } from './Common'
