const otherRouter = [
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    redirect: '/404',
  },
  {
    path: '/systemManagement',
    title: '系统管理',
    name: '系统管理',
    children: [
      {
        path: '/roleManagement',
        title: '角色管理',
        name: '角色管理',
        component: () => import('@/views/pageComponents/systemManagement/roleManagement/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/watermarkManagement',
        title: '通用设置',
        name: '通用设置',
        component: () => import('@/views/pageComponents/systemManagement/watermarkManagement/index.vue'),
        // component: () => import('@/views/pageComponents/systemManagement/signalrTest/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/clientModule',
    title: '用户管理1',
    name: '用户管理1',
    children: [
      {
        path: '/userLists',
        title: '用户管理',
        name: '用户管理',
        component: () => import('@/views/pageComponents/clientModule/userList/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/supplierproductManagement',
    title: '供应商商品管理',
    name: '供应商商品管理',
    children: [
      {
        path: '/supplierProductList',
        title: '供应商商品库',
        name: '供应商商品库',
        component: () => import('@/views/pageComponents/supplierProductManagement/supplierProductList/index.vue'),
        meta: {
          KeepAlive: false,
        },
      },
      {
        path: '/supplierProductAuditList',
        title: '商品审核',
        name: '商品审核',
        component: () => import('@/views/pageComponents/supplierProductManagement/supplierProductAuditList/index.vue'),
        meta: {
          KeepAlive: false,
        },
      },
    ],
  },
  {
    path: '/complianceManagement',
    title: '合规管理',
    name: '合规管理',
    children: [
      {
        path: '/certificateList',
        title: '资质/证书管理',
        name: '资质/证书管理',
        component: () => import('@/views/pageComponents/complianceManagement/certificateList/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
    meta: {
      KeepAlive: true,
    },
  },
  {
    path: '/warehouseManagement',
    title: '仓库管理',
    name: '仓库管理',
    children: [
      {
        path: '/warehouse',
        title: '库存管理',
        name: '库存管理',
        component: () => import('@/views/pageComponents/warehouseManagement/inventory/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
    meta: {
      KeepAlive: true,
    },
  },
  {
    path: '/supplierManagement',
    title: '供应商管理',
    name: '供应商管理',
    children: [
      {
        path: '/supplierSettlementApproval',
        title: '供应商入驻审核',
        name: '供应商入驻审核',
        component: () => import('@/views/pageComponents/supplierManagement/supplierSettlementApproval/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/supplierInfo',
        title: '供应商信息',
        name: '供应商信息',
        component: () => import('@/views/pageComponents/supplierManagement/supplierInfo/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/productManagement',
    title: '商品管理',
    name: '商品管理',
    children: [
      {
        path: '/brand',
        title: '品牌库',
        name: '品牌库',
        component: () => import('@/views/pageComponents/productManagement/brand/index.vue'),
        meta: {
          KeepAlive: false,
        },
      },
      {
        path: '/attr',
        title: '商品属性管理',
        name: '商品属性管理',
        component: () => import('@/views/pageComponents/productManagement/attr/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/categoryTemplate',
        title: '类目模板管理',
        name: '类目模板管理',
        component: () => import('@/views/pageComponents/productManagement/categoryTemplate/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/category',
        title: '类目管理',
        name: '类目管理',
        component: () => import('@/views/pageComponents/productManagement/category/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/productLabel',
        title: '商品标签管理',
        name: '商品标签管理',
        component: () => import('@/views/pageComponents/productManagement/productLabel/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/notifyManagement',
    title: '通知管理',
    name: '通知管理',
    children: [
      {
        path: '/systemNotify',
        title: '系统通知',
        name: '系统通知',
        component: () => import('@/views/pageComponents/notifyManagement/systemNotify/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/basicInfoManagement',
    title: '基础资料管理',
    name: '基础资料管理',
    children: [
      {
        path: '/countryRegion',
        title: '国家/地区',
        name: '国家/地区',
        component: () => import('@/views/pageComponents/BasicInfoManagement/countryRegion/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/currency',
        title: '货币管理',
        name: '货币管理',
        component: () => import('@/views/pageComponents/BasicInfoManagement/currency/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/exchangeRate',
        title: '汇率管理',
        name: '汇率管理',
        component: () => import('@/views/pageComponents/BasicInfoManagement/exchangeRate/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/language',
        title: '语言管理',
        name: '语言管理',
        component: () => import('@/views/pageComponents/BasicInfoManagement/language/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
]
export default otherRouter
