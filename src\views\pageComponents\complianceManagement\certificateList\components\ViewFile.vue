<template>
  <a-modal v-model:open="openModal" title="查看文件" @ok="showModal" centered @cancel="handleCancel">
    <BaseFileUploadMode v-model:file-list="fileList" :multiple="true" :max-count="3" disabled />

    <template #footer>
      <a-button type="default" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseFileUploadMode from '@/components/BaseFileUploadMode/index.vue'

// 文件列表
const fileList = defineModel<any[]>('fileList', { required: true })
// 是否显示弹窗
const openModal = ref(false)
// 显示弹窗
const showModal = () => {
  openModal.value = true
}
// 关闭弹窗
const handleCancel = () => {
  openModal.value = false
}
// 确定
const handleSubmit = () => {
  openModal.value = false
}
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss"></style>
