// 供应商商品管理-商品审核
import { request } from './request'

// 获取商品审核列表
export const GetProductApprovalList = (data) => {
  return request({ url: '/api/ProductInfo/GetList ', data }, 'POST')
}

// 商品审核通过
export const AuditPass = (data) => {
  return request({ url: '/api/ProductInfo/AuditPass', data }, 'POST')
}

// 商品审核驳回
export const OverruleSelection = (data) => {
  return request({ url: '/api/ProductInfo/OverruleSelection', data }, 'POST')
}

// 商品审核拒绝
export const RefuseSelection = (data) => {
  return request({ url: '/api/ProductInfo/RefuseSelection', data }, 'POST')
}

// 红点数
export const GetRedNumber = () => {
  return request({ url: '/api/ProductInfo/GetManagementAuditLabelStatusCount' }, 'GET')
}
