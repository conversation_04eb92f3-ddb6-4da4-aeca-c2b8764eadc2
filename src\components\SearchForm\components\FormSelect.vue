<template>
  <a-select
    :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
    v-model:value="item.value"
    :placeholder="item.label"
    :filter-option="filterOption"
    :disabled="(item as any).disabled"
    allowClear
    showArrow
    class="w-140px"
    v-bind="item"
  ></a-select>
</template>

<script setup lang="ts">
import { filterOption } from '@/utils/index'
import { FormItemType } from '../type'

defineEmits<{
  (e: 'serach'): void
}>()

const item = defineModel<FormItemType<'select'>>('item', { required: true })
</script>

<style scoped></style>
