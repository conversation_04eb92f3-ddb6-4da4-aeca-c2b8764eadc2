<template>
  <div>
    <a-form-item-rest>
      <a-radio-group :disabled="disabled" class="listTypeRadio" v-model:value="listType">
        <a-radio-button :value="1">
          <div class="iconBox">
            <appstore-outlined class="icon" />
          </div>
        </a-radio-button>
        <a-radio-button :value="2">
          <div class="iconBox">
            <BarsOutlined class="icon" />
          </div>
        </a-radio-button>
      </a-radio-group>
    </a-form-item-rest>
    <a-upload-dragger class="draggerUploader" v-model:fileList="fileList" name="file" :multiple="true"
      :before-upload="beforeUpload" @mouseenter="imgMouseEnter" :customRequest="null" action="#" :showUploadList="false"
      :disabled="loading || disabled">
      <div class="filePreviewMainBox" style="user-select: none;">
        <a-row v-show="listType == 1" :gutter="16">
          <a-col v-for="(item, index) in fileList" :key="index" :span="24 / column">
            <div @click.stop="preview(item, index)" class="filePreviewCard">
              <div class="preview" :style="'display: flex;align-items: center;justify-content: center;'">
                <img style="width: 100%;" v-if="item.url" :src="item.url" alt="">
                <!-- <div v-show="['jpg', 'jpeg', 'png', 'bmp', 'txt', 'pdf'].indexOf(item.name.split('.').pop().toLowerCase()) === -1"> -->
                <img v-if="showCardIcon(item.file ? item.file.name : item.name)" class="otherFile"
                  :src="getIconPath(item.file ? item.file.name : item.name)" alt="" />
                <!-- </div> -->
                <LoadingOutlined v-show="item.loading" class="loadingIcon" />
                <div class="previewMasker">
                  <eye-outlined class="icon" />
                  <div class="tip">预览</div>
                  <div v-show="!loading" class="delBtn" @click.stop="delItem(item, index)"><delete-outlined
                      class="delBtnIcon" /></div>
                </div>
              </div>
              <div class="title">
                <LoadingOutlined v-if="!item.isRemote" style="font-size: 12px;margin-right: 4px;color: #1890ff;" />
                <img class="icon" :src="getIconPath(item.file ? item.file.name : item.name)" alt="" />
                <span v-if="item.file">{{ item.file.name }}</span>
                <span v-if="item.name">{{ item.name }}</span>
              </div>
            </div>
          </a-col>
        </a-row>
        <div v-show="listType == 2" class="listItemBox" @click.stop="null">
          <div @click="preview(item, index)" class="listItem" v-for="(item, index) in fileList" :key="index">
            <div style="display: flex;align-items: center;">
              <LoadingOutlined v-if="!item.isRemote" style="font-size: 12px;margin-right: 4px;color: #1890ff;" />
              <img class="icon" :src="getIconPath(item.file ? item.file.name : item.name)" alt="" />
              <div v-if="item.file" class="title">{{ item.file.name }}</div>
              <div v-if="item.name" class="title">{{ item.name }}</div>
            </div>
            <div v-show="!loading" @click.stop="delItem(item, index)">
              <delete-outlined class="delBtnIcon" />
            </div>
          </div>
        </div>
      </div>
      <div v-show="fileList.length === 0" class="uploaderTip" v-html="tip"></div>
    </a-upload-dragger>
    <input class="pasteInput" :id="`pasteInput${inputId}`" />
    <a-form-item-rest>
      <a-upload class="btnUploader" v-model:file-list="fileList" name="file" :multiple="true"
        :before-upload="beforeUpload" :customRequest="null" action="#" :showUploadList="false" :disabled="disabled">
        <a-button :disabled="loading || disabled" class="btnUploaderBtn" block>
          <upload-outlined></upload-outlined>
          <span>点击上传</span>
        </a-button>
      </a-upload>
    </a-form-item-rest>
  </div>
  <a-image-preview-group
    :preview="{ visible: pdfPreviewerVisible, onVisibleChange: (value) => { pdfPreviewerVisible = value } }">
    <a-image v-for="(item, index) in pdfPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
  </a-image-preview-group>
  <a-image-preview-group
    :preview="{ current: imgPreviewerCurrent, visible: imgPreviewerVisible, onVisibleChange: (value) => { imgPreviewerVisible = value } }">
    <a-image v-for="(item, index) in imgPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
  </a-image-preview-group>
  <a-modal v-model:open="vedioVisible" title="预览">
    <video v-if="videoSrc" :src="videoSrc" controls style="width: 100%; max-width: 600px;"></video>
    <template #footer>
      <a-button @click="vedioVisible = false">关闭</a-button>
    </template>
  </a-modal>
  <!-- <a-modal :width="1000" v-model:open="excelVisible" title="预览">
    <div style="height: calc(100vh - 300px);padding: 20px 0;">
      <vue-office-excel v-if="excelVisible" :src="excelUrl" style="height: 100%;width: 100%;" />
    </div>
    <template #footer>
      <a-button @click="excelVisible = false">关闭</a-button>
    </template>
  </a-modal>
  <a-modal :width="1000" v-model:open="wordVisible" title="预览">
    <div style="height: calc(100vh - 300px);padding: 20px 0;">
      <vue-office-docx v-if="wordVisible" :src="wordUrl" style="height: 100%;width: 100%;" />
    </div>
    <template #footer>
      <a-button @click="wordVisible = false">关闭</a-button>
    </template>
  </a-modal> -->
  <!-- 提示 -->
  <ConfirmModal ref="confirmModalRef" />
</template>

<script lang="ts" setup>
import ConfirmModal from '@/components/ConfirmModal.vue'
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash'
import { encodeUrlFilename, decodeUrlFilename } from '@/utils/index';
const emits = defineEmits(['update:value', 'change']);
import { LoadingOutlined, EyeOutlined, UploadOutlined, DeleteOutlined, BarsOutlined, AppstoreOutlined } from '@ant-design/icons-vue';
// import * as pdfjsLib from 'pdfjs-dist/build/pdf';
const appStore = useAppStore();
import eventBus from '@/utils/eventBus';
import useAppStore from '@/store/modules/app';
// pdfjsLib.GlobalWorkerOptions.workerSrc = "/pdf.worker.min.js";
// import VueOfficeExcel from '@vue-office/excel'
// import '@vue-office/excel/lib/index.css'
// import VueOfficeDocx from '@vue-office/docx'
// import '@vue-office/docx/lib/index.css'
const props = defineProps({
  tip: {
    type: String,
    default: '<span>粘贴文件或将文件拖拽至此即可添加<span>'
  },
  loading: {
    type: Boolean,
    default: false
  },
  uploadFn: {
    type: Function as PropType<(params: {
      file: File
      attr_id?: number
      category_template_id?: number
    }) => Promise<{ data: { url: string } }>>,
  },
  attr_id: {
    type: Number
  },
  category_template_id: {
    type: Number
  },
  disabled: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 5,
  },
  size: {
    type: Number,
  },
  size_unit: {
    type: Number,
  },
  column: {
    type: Number,
    default: 4
  },
  type: {
    type: Array,
    default: ['image/bmp', 'image/webp', 'image/gif', 'image/png', 'image/jpeg', 'application/pdf']
  },
  value: {
    type: Array,
    default: []
  }
})
const videoSrc = ref(null)
const vedioVisible = ref(false)
// const excelVisible = ref(false)
// const excelUrl = ref(null)
// const wordVisible = ref(false)
// const wordUrl = ref(null)
const confirmModalRef = ref(null)
const inputId = ref(null)
const fileList = ref([])
const pdfPreviewerVisible = ref(false)
const pdfPreviewerArray = ref([])
const imgPreviewerVisible = ref(false)
const imgPreviewerArray = ref([])
const imgPreviewerCurrent = ref(0)
const listType = ref(1)
const processedFiles = new Set<string>()
const timer = ref(null)
const currentFilesNum = ref(0)
onMounted(() => {
  inputId.value = Date.now() + (Math.floor(Math.random() * (9999999999 - 1000000000 + 1)) + 1000000000)
})
const updateToParent = () => {
  emits('update:value', fileList.value)
  emits('change')
}
const imgMouseEnter = () => {
  document.getElementById(`pasteInput${inputId.value}`).focus()
  document.getElementById(`pasteInput${inputId.value}`).addEventListener('paste', (e) => getClipboardFiles(e), { once: true })
}
const delItem = (item, index) => {
  if (item.fileId ? item.fileId : item.id) {
    eventBus.emit('removeTemplateFilesUploading', item.fileId ? item.fileId : item.id)
  }
  const fileIdentifier = `${fileList.value[index].name}-${fileList.value[index].size}`
  fileList.value.splice(index, 1)
  updateToParent()
  if (processedFiles.has(fileIdentifier)) {
    processedFiles.delete(fileIdentifier)
  }
}
const getClipboardFiles = (e: ClipboardEvent) => {
  e.preventDefault()
  const items = e.clipboardData?.items
  if (items) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      if (item.kind === 'file') {
        const file = item.getAsFile()
        if (file) {
          const fileIdentifier = `${file.name}-${file.size}`
          if (!processedFiles.has(fileIdentifier)) {
            processedFiles.add(fileIdentifier)
            beforeUpload(Object.assign(file, { uid: Date.now() + (Math.floor(Math.random() * (9999999999 - 1000000000 + 1)) + 1000000000) }), items)
          }
        }
      }
    }
  }
}
const getBase64 = file => {
  return new Promise(resolve => {
    const fileReader = new FileReader();
    fileReader.readAsDataURL(file);
    fileReader.onload = () => {
      resolve(fileReader.result);
    };
  });
};
const preview = (item, index) => {
  if (item.loading) return
  if (['pdf', 'application/pdf'].indexOf(item.type) != -1) {
    previewPdf(item)
  } else if (['image', 'png', 'jpg', 'jpeg', 'bmp', 'gif'].indexOf(item.type) != -1 || item.type.indexOf('image') != -1) {
    previewImg(item)
  } else if (['text', 'txt'].indexOf(item.type) != -1) {
    previewText(item)
  } else if (['mp4'].indexOf(item.type) != -1 || item.type.indexOf('video/mp4') != -1) {
    videoSrc.value = item.url
    vedioVisible.value = true
  } else {
    message.warning('此类型文件不支持预览')
  }
  //  else if (['xlsx', 'xls', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'].indexOf(item.type) != -1) {
  //   excelUrl.value = item.url
  //   excelVisible.value = true
  // } else if (['doc', 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].indexOf(item.type) != -1) {
  //   wordUrl.value = item.url
  //   wordVisible.value = true
  // } 
}
const previewImg = (item) => {
  var arr = fileList.value.filter(e => ['image', 'png', 'jpg', 'jpeg', 'bmp', 'gif'].indexOf(e.type) != -1 || item.type.indexOf('image') != -1).map(a => a.url)
  imgPreviewerVisible.value = true
  imgPreviewerArray.value = [...arr]
  imgPreviewerCurrent.value = arr.findIndex(e => e === item.url)
}
const previewText = (item) => {
  imgPreviewerVisible.value = true
  imgPreviewerArray.value = [item.url]
  imgPreviewerCurrent.value = 0
}
const beforeUpload = (file, uploadFileList, dontCheckRepeat = false) => {
  let promise = new Promise<void>((resolve, reject) => {
    if (!timer.value) {
      currentFilesNum.value = fileList.value.length
      timer.value = setTimeout(() => {
        timer.value = null
      }, 100);
    }
    if (uploadFileList.length + currentFilesNum.value > props.limit) {
      processedFiles.delete(`${file.name}-${file.size}`)
      message.error({ content: `上传文件数量不能超过${props.limit}个`, key: 'attachmentLimit' })
      return reject
    }
    if ((props.size_unit == 1 ? file.size / 1024 : props.size_unit == 2 ? file.size / 1024 / 1024 : file.size / 1024 / 1024 / 1024) > props.size) {
      processedFiles.delete(`${file.name}-${file.size}`)
      message.error({ content: `文件大小不能超过${props.size}${props.size_unit == 1 ? 'KB' : props.size_unit == 2 ? 'MB' : 'GB'}`, key: 'msg' })
      return reject
    }
    const typeCheck = props.type.indexOf(file.name.split('.')[file.name.split('.').length - 1]) != -1
    if (!typeCheck) {
      processedFiles.delete(`${file.name}-${file.size}`)
      message.error({ content: `请上传${props.type.join()}文件`, key: 'msg' })
      return reject
    }
    // 同名文件检查
    if (!dontCheckRepeat) {
      const sameNameIndex = fileList.value.findIndex(e => e.file ? e.file.name === file.name : e.name === file.name);
      if (sameNameIndex !== -1) {
        confirmModalRef.value.confirm({
          title: '更新文件',
          type: 'warning',
          width: 400,
          content: [
            { text: `文件 ${file.name} 已存在，是否更新该文件？` }
          ],
          onOk: (close) => {
            close();
            delItem(fileList.value[sameNameIndex], sameNameIndex);
            beforeUpload(file, uploadFileList, true)
          },
          onCancel: () => {
            processedFiles.delete(`${file.name}-${file.size}`)
          }
        })
        return reject
      }
    }
    const fileId = Math.floor(Math.random() * 9000000000) + 1000000000
    const fileObj = { id: fileId, file, type: file.type, doneImgLength: 1, doneImgArr: [], url: null, pdfUrl: null, loading: true, lastModified: file.lastModified, size: file.size };
    fileList.value.push(fileObj);
    try {
      eventBus.emit('setTemplateFilesUploading', fileId)
      props.uploadFn({
        file,
        attr_id: props.attr_id,
        category_template_id: props.category_template_id
      }).then(res => {
        fileList.value.find(item => item.id === fileId).remoteUrl = res.data.url;
        fileList.value.find(item => item.id === fileId).isRemote = true
        updateToParent()
        setTimeout(() => {
          eventBus.emit('removeTemplateFilesUploading', fileId)
        }, 20);
      }).catch(() => {
        delItem(fileList.value.find(item => item.id === fileId), fileList.value.findIndex(item => item.id === fileId))
        setTimeout(() => {
          eventBus.emit('removeTemplateFilesUploading', fileId)
        }, 20);
      })
    } catch (error) {
      if (fileList.value.find(item => item.id === fileId)) {
        fileList.value.find(item => item.id === fileId).isRemote = true
      }
      eventBus.emit('removeTemplateFilesUploading', fileId)
    }
    getBase64(file).then(url => {
      if (file.type === 'application/pdf') {
        getPdfMinPreviewUrl(url).then(res => {
          fileList.value.find(item => item.id === fileId).pdfUrl = url
          fileList.value.find(item => item.id === fileId).url = res
          fileList.value.find(item => item.id === fileId).loading = false
          updateToParent()
        })
      } else if (file.type === 'text/plain') {
        getTxtAsImage(file).then(res => {
          fileList.value.find(item => item.id === fileId).url = res
          fileList.value.find(item => item.id === fileId).loading = false
          updateToParent()
        })
      } else {
        fileList.value.find(item => item.id === fileId).url = url
        fileList.value.find(item => item.id === fileId).loading = false
        updateToParent()
      }
    })
    return reject
  })
  return promise
};
const getPdfMinPreviewUrl = async (fileUrl) => {
  return new Promise(async (resolve, reject) => {
    const loadingTask = pdfjsLib.getDocument(fileUrl);
    const pdf = await loadingTask.promise;
    const page = await pdf.getPage(1);
    const viewport = page.getViewport({ scale: 0.2 })
    const canvas = document.createElement('canvas')
    canvas.height = viewport.height
    canvas.width = viewport.width
    const context = canvas.getContext('2d');
    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };
    await page.render(renderContext).promise
    resolve(canvas.toDataURL('image/png'))
  })
}
const getTxtAsImage = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsText(file)
    reader.onload = () => {
      const textContent = reader.result as string
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      const fontSize = 14
      const lineHeight = 24
      const padding = 20
      const lines = textContent.split('\n')
      canvas.width = 800
      canvas.height = 500
      context.fillStyle = '#fff'
      context.fillRect(0, 0, canvas.width, canvas.height)
      context.font = `${fontSize}px Arial`
      context.fillStyle = '#000'
      context.textBaseline = 'top'
      lines.forEach((line, index) => {
        context.fillText(line, padding, padding + index * lineHeight)
      })
      const imageUrl = canvas.toDataURL('image/png')
      resolve(imageUrl)
    }
    reader.onerror = () => {
      reject('文件读取失败')
    }
  })
}
const previewPdf = async (item) => {
  pdfPreviewerArray.value = [...item.doneImgArr]
  pdfPreviewerVisible.value = true
  const loadingTask = pdfjsLib.getDocument(item.pdfUrl);
  const pdf = await loadingTask.promise;
  for (let currentPage = item.doneImgLength; currentPage <= pdf.numPages; currentPage++) {
    if (pdfPreviewerVisible.value) {
      const page = await pdf.getPage(currentPage);
      const viewport = page.getViewport({ scale: 3 })
      const canvas = document.createElement('canvas')
      canvas.height = viewport.height
      canvas.width = viewport.width
      const context = canvas.getContext('2d');
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };
      await page.render(renderContext).promise
      item.doneImgArr.push(canvas.toDataURL('image/png'))
      item.doneImgLength = currentPage + 1
      pdfPreviewerArray.value.push(canvas.toDataURL('image/png'))
    }
  }
};
const getIconPath = (name) => {
  const type = name.split('.').pop().toLowerCase()
  let pathName = ''
  if (['zip', 'rar', '7z'].indexOf(type) != -1) {
    pathName = 'zip'
  } else if (['m4a', 'mp3', 'wav'].indexOf(type) != -1) {
    pathName = 'audio'
  } else if (['xls', 'xlsx'].indexOf(type) != -1) {
    pathName = 'excel'
  } else if (['ppt', 'pptx'].indexOf(type) != -1) {
    pathName = 'ppt'
  } else if (['mp4', 'avi', 'mov', 'wmv', 'flv'].indexOf(type) != -1) {
    pathName = 'video'
  } else if (['doc', 'docx'].indexOf(type) != -1) {
    pathName = 'word'
  } else if (['csv', 'txt'].indexOf(type) != -1) {
    pathName = 'txt'
  } else if (['bmp', 'png', 'jpg', 'jpeg', 'gif'].indexOf(type) != -1) {
    pathName = 'image'
  } else if (['pdf'].indexOf(type) != -1) {
    pathName = 'pdf'
  } else if (['psd'].indexOf(type) != -1) {
    pathName = 'psd'
  }
  return `filePic/${pathName}.png`
}
const showCardIcon = (name) => {
  const type = name.split('.').pop().toLowerCase()
  let Boolean = true
  if (['bmp', 'png', 'jpg', 'jpeg', 'gif'].indexOf(type) != -1) {
    Boolean = false
  } else if (['pdf'].indexOf(type) != -1) {
    Boolean = false
  }
  return Boolean
}
watch(
  () => props.value,
  async () => {
    var baseUrl = await appStore.getExteriorDormanName();
    if (props.value && props.value.length) {
      fileList.value = cloneDeep(props.value)
      try {
        fileList.value.forEach((x, index) => {
          if (!x.isRemote) return
          // x.remoteUrl = decodeUrlFilename(x.pdfUrl ? x.pdfUrl : x.url)
          const completeUrl = encodeUrlFilename(x.url) // 需要先拼成完整路径再显示
          if (x.name.split('.').pop().toLowerCase() === 'pdf' && !x.hasLoad && !x.file) {
            fileList.value[index].loading = true
            fileList.value[index].doneImgArr = []
            fileList.value[index].doneImgLength = 1
            fileList.value[index].hasLoad = true
            getPdfMinPreviewUrl(completeUrl)
              .then((res) => {
                fileList.value[index].pdfUrl = completeUrl as string
                fileList.value[index].url = res as string
                fileList.value[index].loading = false
              })
              .catch(() => {
                fileList.value[index].error = true
                fileList.value[index].loading = false
              })
          } else if (x.name.split('.').pop().toLowerCase() === 'txt' && !x.file && !x.hasLoad) {
            fileList.value[index].loading = true
            fileList.value[index].loading = true
            fileList.value[index].hasLoad = true
            getTxtAsImage(completeUrl)
              .then((res) => {
                fileList.value[index].url = res as string
                fileList.value[index].loading = false
              })
              .catch(() => {
                fileList.value[index].error = true
                fileList.value[index].loading = false
              })
          } else if (!x.hasLoad) {
            fileList.value[index].url = completeUrl
            fileList.value[index].hasLoad = true
          }
        })
      } catch (error) {
        // console.log(error);
      }
    } else {
      fileList.value = []
    }
  }, { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
.uploaderDescription {
  color: rgba(0, 0, 0, 0.5);
}

.uploaderTip {
  height: 120px;
  // line-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: rgba(0, 0, 0, 0.5);
  margin: -16px 0;
  user-select: none;
}

.filePreviewMainBox {
  margin: 0 1vw;
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;

  .listItemBox {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .listItem {
      cursor: pointer;
      background-color: #fff;
      padding: 0 12px;
      border: 1px solid #d9d9d9;
      border-radius: 3px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      height: 30px;
      transition: all 0.3s;

      .title {
        max-width: 450px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &:hover {
        border-color: #40a9ff;
      }

      .icon {
        width: 14px;
        height: 14px;
        margin-right: 4px;
        font-size: 16px;
      }

      .delBtnIcon {
        font-size: 15px;
        color: rgba(255, 77, 79, 0.8);

        &:hover {
          color: rgba(255, 77, 79, 1);
        }
      }
    }
  }

  .filePreviewCard {
    width: 150px;
    cursor: pointer;

    .preview {
      width: 100%;
      height: 96px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background-color: #fff;
      overflow: hidden;
      position: relative;

      .previewMasker {
        width: 100%;
        height: 100%;
        position: absolute;
        background-color: rgba(0, 0, 0, 0.5);
        top: 0;
        transition: opacity 0.3s;
        opacity: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        .delBtn {
          width: 25px;
          height: 25px;
          border-radius: 4px;
          position: absolute;
          right: 5px;
          top: 5px;
          background-color: rgba(255, 77, 79, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s;

          .delBtnIcon {
            color: #fff;
            font-size: 14px;
          }

          &:hover {
            background-color: rgba(255, 77, 79, 1);
          }
        }

        .icon {
          font-size: 16px;
          color: #fff;
        }

        .tip {
          color: #fff;
          font-size: 14px;
          margin-left: 8px;
        }
      }
    }

    &:hover {
      .preview {
        border-color: #40a9ff;
      }

      .previewMasker {
        opacity: 1;
      }

      .title {
        color: #40a9ff;
        text-decoration: underline;
      }
    }

    .title {
      overflow: hidden;
      text-overflow: ellipsis;
      text-wrap: nowrap;
      line-height: 36px;
      font-size: 13px;
      text-align: left;

      .icon {
        width: 14px;
        height: 14px;
        margin-right: 4px;
        font-size: 16px;
        margin-bottom: 2px;
      }
    }
  }
}

.loadingIcon {
  color: #1890ff;
  font-size: 30px;
  position: absolute;
}

.btnUploaderBtn {
  margin-top: 12px;
}

.pasteInput {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1px;
  height: 1px;
  pointer-events: none;
  opacity: 0;
}

.listTypeRadio {
  margin-bottom: 12px;

  .iconBox {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      font-size: 17px;
    }
  }
}

.pdfIcon {
  color: rgb(254, 83, 89);
}

.imgIcon {
  color: rgb(37, 179, 158);
}

.txtIcon {
  color: rgb(74, 144, 255);
}

// .draggerUploader {
//   display: inline-block;
//   background-color: red;
//   width: 100%;
// }
.otherFile {
  position: absolute;
  inset: 0;
  width: 50px;
  height: 50px;
  margin: auto;
}
</style>