<template>
  <div>
    <a-upload ref="uploadRef" class="base-file-upload" list-type="picture-card" v-bind="$attrs" v-model:fileList="fileList" :before-upload="handleBeforeUpload">
      <slot></slot>
      <template #itemRender="{ file }">
        <a-image :src="file.url || file.thumbUrl" />
        <div v-if="file.status === 'uploading'" class="upload-uploading">
          <LoadingOutlined class="c-primary mr-4" />
          <span class="c-#666">上传中</span>
        </div>
        <div v-else class="base-file-upload-mask">
          <a-space v-show="!disabled">
            <EyeOutlined class="c-#fff cursor-pointer" @click="handlePreview(file)" />
            <DeleteOutlined class="c-#fff cursor-pointer" @click="handleDelete(file)" />
          </a-space>
          <a-space v-show="disabled">
            <EyeOutlined class="c-#fff cursor-pointer" @click="handlePreview(file)" />
          </a-space>
        </div>
      </template>
    </a-upload>
    <a-image-preview-group
      :preview="{
        visible: pdfPreviewerVisible,
        onVisibleChange: (value) => {
          pdfPreviewerVisible = value
        },
      }"
    >
      <a-image v-for="(item, index) in pdfPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
    </a-image-preview-group>
    <a-image-preview-group
      :preview="{
        current: imgPreviewerCurrent,
        visible: imgPreviewerVisible,
        onVisibleChange: (value) => {
          imgPreviewerVisible = value
        },
      }"
    >
      <a-image v-for="(item, index) in imgPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
    </a-image-preview-group>
  </div>
  <a-modal v-model:open="videoPreviewerVisible" title="视频预览" centered width="600px" :footer="null">
    <video :src="videoPreviewerArray" autoplay controls class="w-540px h-540px"></video>
  </a-modal>
</template>

<script setup lang="ts">
import * as pdfjsLib from 'pdfjs-dist/build/pdf'
import { LoadingOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
  disabled?: boolean
  beforeUpload?: (file: any) => any
}>()

// const getPdfMinPreviewUrl = async (fileUrl) => {
//   return new Promise((resolve) => {
//     ;(async () => {
//       const loadingTask = pdfjsLib.getDocument(fileUrl)
//       const pdf = await loadingTask.promise
//       const page = await pdf.getPage(1)
//       const viewport = page.getViewport({ scale: 0.2 })
//       const canvas = document.createElement('canvas')
//       canvas.height = viewport.height
//       canvas.width = viewport.width
//       const context = canvas.getContext('2d')
//       const renderContext = {
//         canvasContext: context,
//         viewport,
//       }
//       await page.render(renderContext).promise
//       resolve(canvas.toDataURL('image/png'))
//     })()
//   })
// }

const videoPreviewerVisible = ref(false)
const videoPreviewerArray = ref<any>('')

const uploadRef = ref<any>()

const fileList = defineModel<any[]>('fileList', { required: true })

const emit = defineEmits(['delete'])

const pdfPreviewerVisible = ref(false)
const pdfPreviewerArray = ref<any[]>([])
const imgPreviewerVisible = ref(false)
const imgPreviewerArray = ref<any[]>([])
const imgPreviewerCurrent = ref(0)

const handlePreview = (item: any) => {
  console.log(item, 'item1111')

  if (item.type.indexOf('pdf') != -1) {
    previewPdf(item)
  } else if (item.type.indexOf('image') != -1) {
    previewImg(item)
  } else if (item.type.indexOf('text') != -1) {
    previewText(item)
  } else if (item.type.indexOf('mp4') != -1) {
    videoPreviewerVisible.value = true
    console.log(item, 'item2222')

    videoPreviewerArray.value = item.videoUrl || item.url
  }
}

const previewImg = (item) => {
  console.log(item)

  const arr = fileList.value.filter((e) => e.type.indexOf('image') != -1).map((a) => a.url || a.thumbUrl)
  imgPreviewerVisible.value = true
  imgPreviewerArray.value = [...arr]
  imgPreviewerCurrent.value = arr.findIndex((e) => e === item.url || e === item.thumbUrl)
}

const previewText = (item) => {
  imgPreviewerVisible.value = true
  imgPreviewerArray.value = [item.url]
  imgPreviewerCurrent.value = 0
}

const getPdfMinPreviewUrl = async (fileUrl) => {
  return new Promise((resolve) => {
    ;(async () => {
      const loadingTask = pdfjsLib.getDocument(fileUrl)
      const pdf = await loadingTask.promise
      const page = await pdf.getPage(1)
      const viewport = page.getViewport({ scale: 0.2 })
      const canvas = document.createElement('canvas')
      canvas.height = viewport.height
      canvas.width = viewport.width
      const context = canvas.getContext('2d')
      const renderContext = {
        canvasContext: context,
        viewport,
      }
      await page.render(renderContext).promise
      resolve(canvas.toDataURL('image/png'))
    })()
  })
}

const previewPdf = async (item) => {
  pdfPreviewerArray.value = [...item.url]
  pdfPreviewerVisible.value = true
  const loadingTask = pdfjsLib.getDocument(await getPdfMinPreviewUrl(item.url))
  const pdf = await loadingTask.promise
  for (let currentPage = item.doneImgLength; currentPage <= pdf.numPages; currentPage++) {
    if (pdfPreviewerVisible.value) {
      const page = await pdf.getPage(currentPage)
      const viewport = page.getViewport({ scale: 3 })
      const canvas = document.createElement('canvas')
      canvas.height = viewport.height
      canvas.width = viewport.width
      const context = canvas.getContext('2d')
      const renderContext = {
        canvasContext: context,
        viewport,
      }
      await page.render(renderContext).promise
      item.doneImgArr.push(canvas.toDataURL('image/png'))
      item.doneImgLength = currentPage + 1
      pdfPreviewerArray.value.push(canvas.toDataURL('image/png'))
    }
  }
}

const handleDelete = (file) => {
  emit('delete', file)
}

const handleBeforeUpload = async (file: any) => {
  if (props.beforeUpload) {
    await props.beforeUpload(file)
  }
  setTimeout(() => {
    fileList.value = fileList.value.filter((i) => i.status !== 'uploading')
  }, 10)
  return false
}
</script>

<style scoped lang="scss">
.base-file-upload {
  :deep(.ant-image-mask) {
    display: none !important;
  }
  :deep(.ant-upload-list-item-container) {
    position: relative;
  }
  .base-file-upload-mask {
    display: flex;
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    align-items: center;
    justify-content: center;
    z-index: 2;
    &:hover {
      opacity: 1;
    }
  }
  .upload-uploading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
