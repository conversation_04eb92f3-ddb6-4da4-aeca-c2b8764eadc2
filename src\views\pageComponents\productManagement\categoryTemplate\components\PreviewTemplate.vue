<template>
  <a-drawer
    destroyOnClose
    :footerStyle="{ paddingLeft: '24px' }"
    v-model:open="templatePreviewVisible"
    width="1200"
    :title="previewOrDetail == 1 ? '预览模板' : '查看模板'"
    placement="right"
    :maskClosable="false"
  >
    <!-- <a-spin v-show="mounteLoading" style="margin: 12px 0 16px 0;" /> -->
    <PreviewForm
      :drawerStatus="3"
      :currencySymbolOptions="currencySymbolOptions"
      :meteringUnitOptions="meteringUnitOptions"
      :activeLanguage="activeKey"
      :category_template_id="category_template_id"
      :canUseLanguages="canUseLanguages"
      :mappingFnArrForReference="mappingFnArrForReference"
      ref="PreviewFormRef"
      :languageStickey="-24"
    >
      <template #title>
        <div :style="!code ? 'margin-bottom: -24px' : ''" style="background-color: #fff; z-index: 10; margin-top: -24px; padding-bottom: 20px; padding-top: 24px">
          <div style="font-size: 14px; font-weight: bold; color: #333" v-if="previewOrDetail == 2">
            {{ title }}
            <span style="padding-left: 36px">模板编码：{{ code }}</span>
          </div>
          <div id="description" style="color: #333; word-break: break-all; margin-top: 12px" v-if="previewOrDetail == 2 && description">{{ description }}</div>
        </div>
      </template>
    </PreviewForm>
    <a-form :colon="false" :label-col="{ style: { width: '120px', marginRight: '20px' } }">
      <div v-if="previewOrDetail == 2">
        <div class="title">其他信息</div>
        <a-form-item label="版本号">
          <div class="detailValue w200">{{ target.version_number }} ({{ target.version_number_status === 1 ? '草稿' : '正式' }})</div>
        </a-form-item>
        <div style="display: flex">
          <a-form-item label="创建时间">
            <div class="detailValue w200">{{ target.create_at }}</div>
          </a-form-item>
          <a-form-item label="创建人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.create_user_real_name }}</div>
                <!-- <div v-show="target.create_user_scope == 1">所属公司：{{ target.create_user_company_name ? target.create_user_company_name : '--' }}</div>
                <div v-show="target.create_user_scope != 1">所属客户：{{ target.create_user_customer_name ? target.create_user_customer_name : '--' }}</div>
                <div>所在部门：{{ target.create_user_department ? target.create_user_department : '--' }}</div>
                <div>岗<span style="visibility: hidden;">占位</span>位：{{ target.create_user_jobtitlename ? target.create_user_jobtitlename : '--' }}</div> -->
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.create_user_real_name ? target.create_user_real_name : '--' }}</span>
                <!-- <span v-if="target.create_user_department || target.create_user_jobtitlename" class="detailValueDescription">（
                  <span v-if="target.create_user_jobtitlename">{{ target.create_user_jobtitlename }}&nbsp;|&nbsp;</span>
                  <span v-if="target.create_user_department">{{ target.create_user_department }} </span>
                ）</span> -->
              </div>
            </a-tooltip>
          </a-form-item>
        </div>
        <div style="display: flex">
          <a-form-item label="最后修改时间">
            <div class="detailValue w200">{{ target.update_at }}</div>
          </a-form-item>
          <a-form-item label="最后修改人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.update_user_real_name }}</div>
                <!-- <div v-show="target.update_user_scope == 1">所属公司：{{ target.update_user_company_name ? target.update_user_company_name : '--' }}</div>
                <div v-show="target.update_user_scope != 1">所属客户：{{ target.update_user_customer_name ? target.update_user_customer_name : '--' }}</div>
                <div>所在部门：{{ target.update_user_department ? target.update_user_department : '--' }}</div>
                <div>岗<span style="visibility: hidden;">占位</span>位：{{ target.update_user_jobtitlename ? target.update_user_jobtitlename : '--' }}</div> -->
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.update_user_real_name ? target.update_user_real_name : '--' }}</span>
                <!-- <span v-if="target.update_user_department || target.update_user_jobtitlename" class="detailValueDescription">（
                  <span v-if="target.update_user_jobtitlename">{{ target.update_user_jobtitlename }}&nbsp;|&nbsp;</span>
                  <span v-if="target.update_user_department">{{ target.update_user_department }} </span>
                ）</span> -->
              </div>
            </a-tooltip>
          </a-form-item>
        </div>
      </div>
    </a-form>
    <a-back-top
      type="primary"
      :target="getContainer"
      :visibility-height="100"
      :style="{ right: '560px', top: '70px', width: '80px', height: '28px', 'border-radius': '14px', 'background-color': '#fff' }"
    />
    <template #footer>
      <!-- <a-button style="margin-right: 10px;" id="templatePreviewSubmit" type="primary" @click="submitTest">模拟提交</a-button> -->
      <a-button id="templatePreviewCancel" @click="templatePreviewVisible = false">关闭</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { handleDataSourceResponseData } from '@/utils/index'
import { cloneDeep } from 'lodash'
import {
  GetMultiAttrValueSelectOptionByLanguages,
  GetLanguageSelectOption,
  GetMeteringUnitSelectOption,
  GetBrandDataSourceList,
  GetManufactureDataSourceList,
  GetDevUserDataSourceList,
  GetProductUserDataSourceList,
  GetDesignUserDataSourceList,
  GetCopywriterUserDataSourceList,
  GetJdCustomerDataSourceList,
  GetSrmSupplierDataSourceList,
} from '@/servers/categoryTemplate'
import { GetPlmEnum } from '@/servers/Common'
import PreviewForm from './PreviewForm.vue'
const props = defineProps({
  languageStickey: {
    type: Number,
  },
  category_template_id: {
    type: Number,
  },
})
const mappingFnArrForReference = [
  { enum: 1, fn: GetBrandDataSourceList, searchKey: 'brand_name' },
  { enum: 2, fn: GetManufactureDataSourceList, searchKey: 'manufacturer_name' },
  { enum: 3, fn: GetDevUserDataSourceList, searchKey: 'real_name' },
  { enum: 4, fn: GetProductUserDataSourceList, searchKey: 'real_name' },
  { enum: 5, fn: GetDesignUserDataSourceList, searchKey: 'real_name' },
  { enum: 6, fn: GetCopywriterUserDataSourceList, searchKey: 'real_name' },
  { enum: 1001, fn: GetJdCustomerDataSourceList, searchKey: 'jd_customer_name' },
  { enum: 1002, fn: GetSrmSupplierDataSourceList, searchKey: 'name' },
]
const templatePreviewVisible = ref(false)
const PreviewFormRef = ref<any>(null)
const languageOptions = ref<any[]>([])
const canUseLanguages = ref<any[]>([])
const currencySymbolOptions = ref<any[]>([])
const meteringUnitOptions = ref<any[]>([])
const activeKey = ref<number | null>(null)
const previewOrDetail = ref(1)
const description = ref<string | null>(null)
const title = ref<string | null>(null)
const code = ref<string | null>(null)
const templateId = ref<number | null>(null)
const mounteLoading = ref(false)
const target = ref<any>({})
onMounted(() => {
  GetLanguageSelectOption().then((res) => {
    res.data.forEach((x) => {
      languageOptions.value.push({ label: x.language_name, value: x.id })
    })
  })
  GetMeteringUnitSelectOption().then((res) => {
    res.data.forEach((x) => {
      x.label = x.description ? `${x.metering_unit_name}(${x.description})` : x.metering_unit_name
      x.value = x.id
    })
    meteringUnitOptions.value = res.data
  })
  GetPlmEnum().then((res) => {
    currencySymbolOptions.value = res.data.attr.currency_symbol_list
  })
})
const getContainer = () => {
  return document.querySelector('.ant-drawer-body')
}
const open = async (data, page, permission = null, info) => {
  templatePreviewVisible.value = true
  mounteLoading.value = true
  previewOrDetail.value = page
  target.value = {}
  if (page == 2) {
    description.value = info?.description
    title.value = info?.name
    code.value = info?.code
    templateId.value = info?.id
    for (const key in data) {
      if (key != 'content') {
        target.value[key] = data[key]
      }
    }
  }
  const templateFormData: any[] = []
  canUseLanguages.value = []

  // 检查 data.content 是否存在
  if (!data || !data.content || !Array.isArray(data.content)) {
    console.error('数据格式错误: data.content 不存在或不是数组', {
      data,
      hasData: !!data,
      hasContent: !!(data && data.content),
      isContentArray: !!(data && data.content && Array.isArray(data.content)),
    })
    mounteLoading.value = false
    return
  }

  data.content.forEach((item) => {
    if (item.children && Array.isArray(item.children)) {
      item.children.sort((a, b) => a.y - b.y || a.x - b.x)
    }
  })
  setTimeout(async () => {
    for (const x of data.content) {
      var obj = {
        name: x.attrGroupName,
        id: x.id,
        type: x.type,
        language_config: x.language_config || x.languages,
        group: {},
      }
      if (x.type == 100) {
        obj['is_customize'] = x.is_customize
        obj['is_display_border'] = x.is_display_border
        obj['is_display_serial_number'] = x.is_display_serial_number
        obj['is_support_adding_rows'] = x.is_support_adding_rows
        obj['table_rows'] = x.table_rows
      }
      if (x.children && x.children.length != 0) {
        // 常规分组
        if (x.type != 100) {
          for (const y of x.children) {
            if (y.type != 1000) {
              // 取出语言并集
              y.language_config.forEach((l) => {
                l.value = null
                if (!canUseLanguages.value.find((p) => p.value === l.language_id)) {
                  canUseLanguages.value.push(languageOptions.value.find((o) => o.value === l.language_id))
                }
              })
              // 跟据不同类型处理
              await processChild(y)
              if (!obj.group[Number(y.y).toFixed(0)]) {
                obj.group[Number(y.y).toFixed(0)] = [y]
              } else {
                obj.group[Number(y.y).toFixed(0)].push(y)
              }
            }
          }
        } else if (x.type == 100) {
          console.log('aaa', x)

          // 动态表格
          // 获取表头
          obj['columns'] = []
          obj['data'] = []
          var firstRow = {}
          for (const child of x.children) {
            if (child.type != 1000) {
              child.language_config.forEach((lc) => {
                lc.value = null
                if (!obj['columns'].find((k) => k.language_id === lc.language_id)) {
                  obj['columns'].push({
                    language_id: lc.language_id,
                    column: [{ id: child.id, is_must: child.is_must, title: lc.attr_name, key: child.id, width: child.type != 4 ? 160 : 250, tips: showTips(child, lc) }],
                  })
                } else {
                  obj['columns']
                    .find((k) => k.language_id === lc.language_id)
                    .column.push({ id: child.id, is_must: child.is_must, title: lc.attr_name, key: child.id, width: child.type != 4 ? 160 : 250, tips: showTips(child, lc) })
                }
                if (!canUseLanguages.value.find((p) => p.value === lc.language_id)) {
                  canUseLanguages.value.push(languageOptions.value.find((o) => o.value === lc.language_id))
                }
              })
              await processChild(child)
              firstRow[child.id] = cloneDeep(child)
            }
          }
          obj['columns'].forEach((j) => {
            if (x.is_display_serial_number) {
              j.column.unshift({ title: '序号', key: 'index', width: 50, maxWidth: 50, minWidth: 50, fixed: 'left' })
            }
            if (x.is_support_adding_rows) {
              j.column.push({ title: '操作', key: 'operate', width: 44, maxWidth: 44, minWidth: 44, fixed: 'right' })
            }
          })
          for (let index = 0; index < obj['table_rows']; index++) {
            obj['data'].push(cloneDeep(firstRow))
          }
          obj['standByRow'] = cloneDeep(firstRow)
        }
      }
      templateFormData.push(obj)
    }
    activeKey.value = canUseLanguages.value.length ? canUseLanguages.value[0].value : null
    if (PreviewFormRef.value && typeof PreviewFormRef.value.setData === 'function') {
      PreviewFormRef.value.setData(templateFormData)
    }
    mounteLoading.value = false
  }, 50)
}
const showTips = (child, config) => {
  var tip = null
  if ([6, 7].indexOf(child.type) != -1 && child.display_type == 1) {
    return config.attr_tips
  } else if ([9, 10, 11].indexOf(child.type) != -1) {
    return config.attr_tips
  }
  return tip
}
const processChild = async (y) => {
  // 解析 type_json 字符串为对象
  if (typeof y.type_json === 'string') {
    try {
      y.type_json = JSON.parse(y.type_json)
    } catch (error) {
      console.error('解析 type_json 失败:', y.type_json, error)
      y.type_json = {}
    }
  }

  if (y.type == 1 || y.type == 2) {
    // 填入默认值
    try {
      if (y.type_json.default_value_config && Array.isArray(y.type_json.default_value_config)) {
        y.type_json.default_value_config.forEach((m) => {
          y.language_config.forEach((p) => {
            if (m.language_id === p.language_id) {
              if (m.default_value) {
                p.value = m.default_value
              } else {
                p.value = null
              }
            }
          })
        })
      }
    } catch (error) {
      console.error('处理默认值配置失败:', error)
    }
  } else if (y.type == 5) {
    // 日期
    y.language_config.forEach((p) => {
      p.value = null
      if (y.type_json.default_value) {
        p.value = getTimeDefaultValue(y.type_json.style_type)
      }
    })
  } else if (y.type == 6) {
    // 单选
    y.language_config.forEach((p) => {
      p.value = null
      if (y.type_json.option_list && Array.isArray(y.type_json.option_list)) {
        const defaultOption = y.type_json.option_list.find((a) => a.is_def)
        if (defaultOption) {
          p.value = defaultOption.value
        }
      }
    })
  } else if (y.type == 7) {
    // 多选
    y.language_config.forEach((p) => {
      p.value = []
      if (y.type_json.option_list && Array.isArray(y.type_json.option_list)) {
        y.type_json.option_list.forEach((k) => {
          if (k.is_def) {
            p.value.push(k.value)
          }
        })
      }
    })
  } else if (y.type == 12) {
    // 引用
    // 处理初始值
    y.language_config.forEach((p) => {
      if (y.type_json.option_type === 1) {
        p.value = null
      } else {
        p.value = []
      }
    })
    // 选项处理
    if (y.multi_attr_value_options && y.multi_attr_value_options.length) {
      y.multi_attr_value_options.forEach((x) => {
        if (x.options && x.options.length != 0) {
          x.options.forEach((y) => {
            y.label = y.mult_attr_value_name
            y.value = y.id
          })
        }
      })
      y.type_json.options = y.multi_attr_value_options
    } else {
      if (y.type_json.available_reference_type === 1) {
        y.type_json.options = await getMultiAttrValueSelectOption(y.type_json.multi_attr_id)
      } else {
        y.type_json.options = []
        y.type_json.page = 1
        y.type_json.searchStr = null
        y.type_json.searching = false

        const mappingFn = mappingFnArrForReference.find((e) => e.enum === y.type_json.data_source)
        if (!mappingFn) {
          console.error('找不到对应的数据源映射函数:', y.type_json.data_source)
          return
        }

        y.type_json.remoteFn = mappingFn.fn
        var remoteData = await y.type_json.remoteFn({ page: 1, pageSize: 20, status: 1 })
        var firstPageOriginData = remoteData.data
        y.type_json.total = firstPageOriginData.total
        var firstPageData = handleDataSourceResponseData(firstPageOriginData.list, y.type_json.data_source)
        canUseLanguages.value.forEach((language) => {
          if (y.type_json.data_source === 2) {
            // 制造商特殊处理
            if (language.value === 2) {
              y.type_json.options.push({
                language_id: language.value,
                options: firstPageData.map((e) => ({ label: (e as any).en_label, value: e.value })),
              })
            } else {
              y.type_json.options.push({
                language_id: language.value,
                options: firstPageData.map((e) => ({ label: e.label, value: e.value })),
              })
            }
          } else {
            y.type_json.options.push({
              language_id: language.value,
              options: firstPageData,
            })
          }
        })
      }
    }
  }
}
const getMultiAttrValueSelectOption = (id) => {
  return new Promise((resolve, reject) => {
    GetMultiAttrValueSelectOptionByLanguages({ multi_attr_id: id }).then((res) => {
      res.data.forEach((x) => {
        if (x.options && x.options.length != 0) {
          x.options.forEach((y) => {
            y.label = y.mult_attr_value_name
            y.value = y.id
          })
        }
      })
      resolve(res.data)
    })
  })
}

const submitTest = async () => {
  // try {
  //   await templateFormRef.value.validateFields()
  // } catch (error) {
  // }
}

const getTimeDefaultValue = (type) => {
  const now = dayjs() // 获取当前时间
  switch (type) {
    case 1:
      return now.format('YYYY-MM-DD HH:mm:ss') // 返回完整日期时间
    case 2:
      return now.format('YYYY-MM') // 返回年月
    case 3:
      return now.format('YYYY-MM-DD') // 返回年月日
    case 4:
      return now.format('HH:mm:ss') // 返回时间
    default:
      return null
  }
}
const close = () => {
  templatePreviewVisible.value = false
}
// 暴露方法
defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-float-btn-body) {
  border-radius: 14px;
}
.title {
  padding: 6px 20px;
  padding-right: 0;
  font-size: 14px;
  background: #f4f7fe;
  color: #333;
  border-radius: 4px;
  user-select: none;
  margin-bottom: 12px;
}
.description {
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  padding-left: 20px;
  white-space: nowrap;
}
.description2 {
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  margin-top: 8px;
  display: inline-block;
  word-break: break-all;
}
::v-deep(.ant-form-item-label) {
  overflow: visible;
  white-space: wrap;
}
.formTable {
  .ant-form-item {
    margin-bottom: 0;
    width: 100%;
  }
}
.totalBox {
  display: flex;
  padding: 8px;
  min-height: 32px;
  align-items: center;
  justify-content: space-between;
  box-sizing: content-box;
  background-color: #fafafa;
  margin-bottom: 20px;
  .text {
    text-align: center;
    width: 100%;
  }
  .btnBox {
    width: 44px;
    min-width: 44px;
  }
}
.textBtn {
  cursor: pointer;
  font-size: 12px;
  color: #409eff;
  transition: color 0.3s;
  &:hover {
    color: #69b9ff;
  }
}
::v-deep(.ant-radio-wrapper) {
  span {
    word-break: break-all;
  }
}
::v-deep(.ant-checkbox-wrapper) {
  span {
    word-break: break-all;
    white-space: initial;
  }
}
.w200 {
  width: 200px;
}
.detailValueDescription {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.5);
}
.detailValueDescription2 {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.5);
  font-weight: normal;
  padding-left: 20px;
}
.is_must {
  padding-left: 6px;
  color: #ff4d4f;
  font-size: 12px;
  position: absolute;
  top: 0px;
  font-family: SimSun, sans-serif;
}
</style>
