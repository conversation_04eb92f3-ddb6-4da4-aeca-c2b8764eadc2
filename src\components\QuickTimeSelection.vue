<template>
  <div class="quick-time-selection">
    <a-radio-group v-model:value="timeScope" button-style="solid" @change="handleTimeChange">
      <a-radio-button :value="1">今日</a-radio-button>
      <a-radio-button :value="2">昨日</a-radio-button>
      <a-radio-button :value="3">近7天</a-radio-button>
      <a-radio-button :value="4">近30天</a-radio-button>
    </a-radio-group>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'

// 定义时间范围类型
export interface TimeRange {
  startDate: string
  endDate: string
  label: string
}

// 定义组件属性
interface Props {
  modelValue?: number // 改为 number 类型，对应 timeScope
  defaultScope?: number
}

const props = withDefaults(defineProps<Props>(), {
  defaultScope: 0,
})

const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const timeScope = ref(props.defaultScope)
const customDateRange = ref<[Dayjs, Dayjs] | null>(null)
const dateFormat = 'YYYY-MM-DD HH:mm:ss'

// 计算当前选择的时间范围
const currentTimeRange = computed(() => {
  const now = dayjs()

  switch (timeScope.value) {
    case 1: // 今日
      return {
        startDate: now.startOf('day').format(dateFormat),
        endDate: now.format(dateFormat),
        label: '今日',
      }
    case 2: {
      // 昨日
      const yesterday = now.subtract(1, 'day')
      return {
        startDate: yesterday.startOf('day').format(dateFormat),
        endDate: yesterday.endOf('day').format(dateFormat),
        label: '昨日',
      }
    }
    case 3: // 近7天
      return {
        startDate: now.subtract(6, 'day').startOf('day').format(dateFormat),
        endDate: now.endOf('day').format(dateFormat),
        label: '近7天',
      }
    case 4: // 近30天
      return {
        startDate: now.subtract(29, 'day').startOf('day').format(dateFormat),
        endDate: now.endOf('day').format(dateFormat),
        label: '近30天',
      }
    case 5: // 本月
      return {
        startDate: now.startOf('month').format(dateFormat),
        endDate: now.endOf('month').format(dateFormat),
        label: '本月',
      }
    case 6: {
      // 上月
      const lastMonth = now.subtract(1, 'month')
      return {
        startDate: lastMonth.startOf('month').format(dateFormat),
        endDate: lastMonth.endOf('month').format(dateFormat),
        label: '上月',
      }
    }
    case 7: // 自定义
      if (customDateRange.value) {
        return {
          startDate: customDateRange.value[0].startOf('day').format(dateFormat),
          endDate: customDateRange.value[1].endOf('day').format(dateFormat),
          label: '自定义',
        }
      }
      return {
        startDate: now.startOf('day').format(dateFormat),
        endDate: now.endOf('day').format(dateFormat),
        label: '自定义',
      }
    default:
      return {
        startDate: '',
        endDate: '',
        label: '',
      }
  }
})

// 监听时间范围变化并发射事件
watch(
  currentTimeRange,
  (newValue) => {
    emit('update:modelValue', newValue)
    emit('change', newValue)
  },
  //   { immediate: true },
)

// 处理快捷时间选择变化
const handleTimeChange = () => {}

// 暴露方法给父组件
defineExpose({
  getTimeRange: () => currentTimeRange.value,
  setTimeScope: (scope: number) => {
    timeScope.value = scope
  },
  setCustomDateRange: (startDate: string, endDate: string) => {
    timeScope.value = 7
    customDateRange.value = [dayjs(startDate), dayjs(endDate)]
  },
})
</script>
