<template>
  <a-modal v-model:open="openModal" title="提示" @ok="showModal" centered @cancel="handleCancel">
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleBan" :loading="saveLoading">确定</a-button>
    </template>
    <div class="flex items-center mb-15px">
      <ExclamationCircleOutlined class="mr-5px text-20px c-#faad14" />
      <span>禁用该供应商，会下架该供应商所有上架中的商品！</span>
    </div>
    <span class="c-#D9001B pl-25px">谨慎操作，商品下架后，选品网商城将不在展示该供应商的商品数据</span>
  </a-modal>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { ToggleSupplierStatus } from '@/servers/supplierSettlementApproval'

const emit = defineEmits(['search'])
const openModal = ref(false)
// 保存loading
const saveLoading = ref(false)
const supplierId = ref(0)
const showModal = (id: number) => {
  openModal.value = true
  supplierId.value = id
}
const handleCancel = () => {
  openModal.value = false
}
// 禁用供应商
const handleBan = () => {
  saveLoading.value = true
  const params = {
    id: supplierId.value,
  }
  ToggleSupplierStatus(params)
    .then(() => {
      message.success('禁用成功')
      emit('search')
      openModal.value = false
    })
    .finally(() => {
      saveLoading.value = false
    })
}
defineExpose({
  showModal,
})
</script>
<style scoped lang="scss"></style>
