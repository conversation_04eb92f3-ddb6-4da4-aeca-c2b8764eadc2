<template>
  <!-- <a-radio-group v-model:value="activeKey" style="margin-bottom: 16px">
    <a-radio-button v-for="(item, index) in canUseLanguages" :key="index" :value="item.value">{{ item.label }}</a-radio-button>
  </a-radio-group> -->
  <div v-show="visible" :style="languageStickey || languageStickey === 0 ? `position: sticky;background: #fff;z-index: 10;top:${languageStickey}px;margin-top:-1px` : ''">
    <slot name="title"></slot>
    <a-tabs v-if="canUseLanguages.length && !removeTabs" @change="changeActiveKey" size="small" v-model:activeKey="activeKey" type="card">
      <template #leftExtra>
        <!-- <a-dropdown v-model:open="searchDropdownVisible" :overlayStyle="{  }" :trigger="['click']"> -->
          <a-button style="font-size: 12px;width: 20px;margin-right: 4px;margin-bottom: 4px;display: flex;justify-content: center;align-items: center;" @click="searchDropdownVisible = !searchDropdownVisible">
            <!-- <DoubleLeftOutlined style="transform: rotate(270deg);transition: all 0.2s;" :style="searchDropdownVisible ? `transform: rotate(90deg)` : ''"  /> -->
            <SearchOutlined />
          </a-button>
          <!-- <template #overlay>
            <div style="width: 100%;height: 200px;position: relative;top: -4px;background-color: #fff;border-bottom-left-radius: 4px;border-bottom-right-radius: 4px;border: 1px solid rgba(5, 5, 5, 0.06);border-top: none;">
              <a-button @click="searchDropdownVisible = false">关闭</a-button>
            </div>
          </template>
        </a-dropdown> -->
      </template>
      <a-tab-pane v-for="(item, index) in canUseLanguages" :key="item.value" :tab="item.label">
        <div style="padding-bottom: 12px;position: absolute;top: 0;transition: all 0.2s;border: 1px solid rgba(5, 5, 5, 0.06);border-top: none;box-shadow: rgba(0, 0, 0, 0.05) 0px 10px 10px;background-color: #fff;width: 100%;z-index: 10;" :style="!searchDropdownVisible ? 'opacity:0;overflow:hidden;pointer-events: none;' : ''" >
          <div class="searchDropdown">
            <div style="position: sticky;top: 0;background-color: #fff;padding-top: 12px;padding-bottom: 8px;border-bottom: 1px solid rgba(5, 5, 5, 0.06);margin-bottom: 8px;">
              <a-input :id="`searchAttrInput_${item.value}`" allow-clear v-model:value="searchDropdownInputValue" placeholder="搜索属性" style="width: 200px;"></a-input>
            </div>
            <div class="searchDropdownContentBox">
              <template v-if="searchDropdownData.find(e => e.language_id === activeKey)">
                <template v-for="(child, childIndex) in getsearchDropdownFilterData(searchDropdownData.find(e => e.language_id === activeKey).items)" :key="childIndex" >
                  <div  v-if="child.title" class="searchDropdownItem">
                    <div @click="linkToTargetFn(child.id, 1)" class="searchDropdownItemTitle">{{ child.title }} ({{ child.children.length }})</div>
                    <div class="searchDropdownSubitem">
                      <div @click="() => { templateFormData.find(e => e.id === child.id).showChild = true;linkToTargetFn(subChild.id, 2); }" v-for="(subChild, subChildIndex) in child.children" :key="subChildIndex" class="subitem">{{ subChild.label }}</div>
                    </div>
                  </div>
                </template>
                <div v-if="!getsearchDropdownFilterData(searchDropdownData.find(e => e.language_id === activeKey).items).length" style="color: #606266;margin-top: 12px;">无符合条件的搜索结果</div>
              </template>
              <div v-else style="color: #606266;margin-top: 12px;">请为相关属性维护对应语言</div>
            </div>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
  <div v-show="visible" v-if="canUseLanguages.length" style="border-left: 1px solid rgba(5, 5, 5, 0.06);border-right: 1px solid rgba(5, 5, 5, 0.06);">
    <slot></slot>
  </div>
  <div v-show="visible" @click="searchDropdownVisible = false" class="tabContent" id="tabContent" v-if="templateFormData.length">
    <a-form hideRequiredMark layout="vertical" :colon="false" ref="templateFormRef" :model="templateFormData">
      <template v-for="(item, itemIndex) in templateFormData" :key="itemIndex">
        <template v-if="item.language_config.find(e => e.language_id == activeKey)">
          <div class="title" style="justify-content: space-between;display: flex;align-items: center;">
            <span :class="{'highlightItem': linkToTarget == item.id }" :id="`preview_group_${item.id}`">{{ item.language_config.find(e => e.language_id == activeKey).attr_name }}</span>
            <div @click="item.showChild = !item.showChild" style="cursor: pointer;" class="showChildIcon">
              <CaretDownOutlined v-show="item.showChild"/>
              <CaretRightOutlined v-show="!item.showChild"/>
            </div>
          </div>
          <!-- 常规分组 -->
          <template v-if="item.type == 99">
            <a-row :class="{'addPadding': drawerStatus === 3}" v-show="item.showChild" :gutter="20" v-for="(child, childIndex) in item.group" :key="childIndex">
              <a-col v-for="(subChild, subChildIndex) in child" :key="subChildIndex" :span="subChild.w * 6">
                <!-- 单行文本 -->
                <template v-if="subChild.type === 1">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item
                      :class="{'smallMarginBottom': drawerStatus == 3}"
                      :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']"
                      v-if="x.language_id === activeKey"
                      :rules="[
                        { required: subChild.is_must, message: `请输入${x.attr_name}` },
                        { validator: (_rule, value) => validateStr(_rule, value, subChild.type_json.word_limit_count), message: `输入内容不可超过${subChild.type_json.word_limit_count}字符` }
                      ]"
                    >
                    <template #label>
                      <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                    </template>
                    <!-- <template #label>
                      <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                        <template #title>
                          <span>{{ x.attr_name }}</span>
                        </template>
                        <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </a-tooltip>
                    </template> -->
                    <a-input v-if="drawerStatus != 3" allow-clear v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`" />
                    <span v-else style="position: relative;top: -8px;word-break: break-all;">{{ x.value ? x.value : '--' }}</span>
                  </a-form-item>
                  </template>
                </template>
                <!-- 多行文本 -->
                <template v-if="subChild.type === 2">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item v-if="x.language_id === activeKey"
                      :class="{'smallMarginBottom': drawerStatus == 3}"
                      :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']"
                      :rules="[
                        { required: subChild.is_must, message: `请输入${x.attr_name}` },
                        { validator: (_rule, value) => validateStr(_rule, value, subChild.type_json.word_limit_count), message: `输入内容不可超过${subChild.type_json.word_limit_count}字符` }
                      ]"
                      >
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <a-textarea v-if="drawerStatus != 3" :auto-size="{ minRows: 4, maxRows: 4 }" :rows="4" allow-clear v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`" />
                      <span v-else style="position: relative;top: -8px;word-break: break-all;">{{ x.value ? x.value : '--' }}</span>
                    </a-form-item>
                  </template>
                </template>
                <!-- 数值 -->
                <template v-if="subChild.type === 3">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item
                      :class="{'smallMarginBottom': drawerStatus == 3}"
                      :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']"
                      v-if="x.language_id === activeKey"
                      :rules="[
                        { required: subChild.is_must, message: `请输入${x.attr_name}` },
                      ]"
                    >
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <a-input-number
                        v-if="drawerStatus != 3"
                        @change="changeOtherLanguageValue(subChild)"
                        string-mode
                        @blur="handleNumberBlur(x, subChild)"
                        v-model:value="x.value"
                        :formatter="value => {
                          if (!(subChild.type_json.display_format == 1) || !value) return value;
                          const [intPart, decimalPart] = value.toString().split('.');
                          return decimalPart !== undefined 
                            ? intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '.' + decimalPart
                            : intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                        }"
                        :parser="value => subChild.type_json.display_format == 1 ? value.replace(/,/g, '') : value"
                        :controls="false"
                        :min="subChild.type_json.min_value"
                        :max="subChild.type_json.max_value"
                        style="width: 100%;"
                        :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`"
                      />
                      <span v-else style="position: relative;top: -8px;word-break: break-all;">{{ x.value ? x.value : '--' }}</span>
                    </a-form-item>
                  </template>
                </template>
                <!-- 单位 -->
                <template v-if="subChild.type === 4">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item
                      :class="{'smallMarginBottom': drawerStatus == 3}"
                      :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']"
                      v-if="x.language_id === activeKey"
                      :rules="[
                        { required: subChild.is_must, message: `请输入${x.attr_name}` },
                      ]"
                    >
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <a-input-number
                        v-if="drawerStatus != 3"
                        @change="() => { changeOtherLanguageValue(subChild);changeTargetValueForRules(subChild, item.group) }"
                        string-mode
                        v-model:value="x.value"
                        :formatter="value => {
                          if (!(subChild.type_json.display_format == 1) || !value) return value;
                          const [intPart, decimalPart] = value.toString().split('.');
                          return decimalPart !== undefined 
                            ? intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '.' + decimalPart
                            : intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                        }"
                        :parser="value => subChild.type_json.display_format == 1 ? value.replace(/,/g, '') : value"
                        :controls="false"
                        :precision="subChild.type_json.unit_type == 2 ? 2
                        : (subChild.template_display_unit_precision || subChild.template_display_unit_precision === 0) ? subChild.template_display_unit_precision
                        : subChild.unit_input_attr_data.find(unit => unit.metering_unit_id === subChild.template_display_unit).decimal_quantity"
                        :min="subChild.type_json.min_value"
                        :max="subChild.type_json.max_value"
                        style="width: 100%;"
                        :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`"
                      >
                        <template #addonAfter>
                          <span v-if="subChild.type_json.unit_type == 1">{{ meteringUnitOptions.find(e => e.value === subChild.template_display_unit)?.label }}</span>
                          <span v-if="subChild.type_json.unit_type == 2">{{ currencySymbolOptions.find(e => e.value === subChild.template_display_unit)?.label }}</span>
                        </template>
                      </a-input-number>
                      <span v-else style="position: relative;top: -8px;word-break: break-all;">{{ x.value ? x.value : '--' }} 
                        <span v-if="subChild.type_json.unit_type == 1">{{ meteringUnitOptions.find(e => e.value === subChild.template_display_unit)?.label }}</span>
                        <span v-if="subChild.type_json.unit_type == 2">{{ currencySymbolOptions.find(e => e.value === subChild.template_display_unit)?.label }}</span>
                      </span>
                    </a-form-item>
                  </template>
                </template>
                <!-- 日期时间选择 -->
                <template v-if="subChild.type === 5">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item v-if="x.language_id === activeKey"
                      :class="{'smallMarginBottom': drawerStatus == 3}"
                      :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']"
                      :rules="[
                        { required: subChild.is_must, message: `请选择${x.attr_name}` },
                      ]"
                    >
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <a-date-picker :getPopupContainer="trigger => trigger.parentNode" v-if="drawerStatus != 3 && subChild.type_json.style_type == 1" @change="changeOtherLanguageValue(subChild)" :disabled-date="subChild.type_json.time_limit ? disabledDate : null" :disabled-time="subChild.type_json.time_limit ? disabledDateTime : null" :valueFormat="'YYYY-MM-DD HH:mm:ss'" v-model:value="x.value" style="width: 100%;" show-time :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`"/>
                      <a-date-picker :getPopupContainer="trigger => trigger.parentNode" v-if="drawerStatus != 3 && subChild.type_json.style_type == 2" @change="changeOtherLanguageValue(subChild)" :disabled-date="subChild.type_json.time_limit ? disabledDate : null" :disabled-time="subChild.type_json.time_limit ? disabledDateTime : null" :valueFormat="'YYYY-MM'" v-model:value="x.value" style="width: 100%;" picker="month" show-time :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`"/>
                      <a-date-picker :getPopupContainer="trigger => trigger.parentNode" v-if="drawerStatus != 3 && subChild.type_json.style_type == 3" @change="changeOtherLanguageValue(subChild)" :disabled-date="subChild.type_json.time_limit ? disabledDate : null" :disabled-time="subChild.type_json.time_limit ? disabledDateTime : null" :valueFormat="'YYYY-MM-DD'" v-model:value="x.value"  style="width: 100%;" :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`" />
                      <a-time-picker :getPopupContainer="trigger => trigger.parentNode" v-if="drawerStatus != 3 && subChild.type_json.style_type == 4" @change="changeOtherLanguageValue(subChild)" :disabled-date="subChild.type_json.time_limit ? disabledDate : null" :disabled-time="subChild.type_json.time_limit ? disabledDateTime : null" :valueFormat="'HH:mm:ss'" v-model:value="x.value" style="width: 100%;" :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`" />
                      <span v-if="drawerStatus === 3" style="position: relative;top: -8px;word-break: break-all;">{{ x.value ? x.value : '--' }}</span>
                    </a-form-item>
                  </template>
                </template>
                <!-- 单选 -->
                <template v-if="subChild.type === 6">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item :class="{'smallMarginBottom': drawerStatus == 3}" :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']" v-if="x.language_id === activeKey" :rules="[{ required: subChild.is_must,  message: `请选择${x.attr_name}` }]">
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <a-radio-group v-if="drawerStatus != 3 && subChild.display_type === 1" @change="changeOtherLanguageValue(subChild)" v-model:value="x.value" style="width: 100%;">
                        <a-radio v-for="(option, optionIndex) in subChild.type_json.option_list" :value="option.value">
                          {{ option.option_label_list.find(e => e.language_id === activeKey).label }}
                        </a-radio>
                        <div v-if="x.attr_tips" class="description" style="padding-left: 0;">{{ x.attr_tips }}</div>
                      </a-radio-group>
                      <a-select :getPopupContainer="trigger => trigger.parentNode" show-search :filter-option="filterOption" v-if="drawerStatus != 3 && subChild.display_type === 2" @change="changeOtherLanguageValue(subChild)" allow-clear v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`">
                        <a-select-option :label="option.option_label_list.find(e => e.language_id === activeKey)?.label" v-for="(option, optionIndex) in subChild.type_json.option_list" :value="option.value">
                          {{ option.option_label_list.find(e => e.language_id === activeKey).label }}
                        </a-select-option>
                      </a-select>
                      <span v-if="drawerStatus == 3" style="position: relative;top: -8px;word-break: break-all;">{{ x.value ? subChild.type_json.option_list.find(e => e.value === x.value).option_label_list.find(e => e.language_id === activeKey).label : '--' }}</span>
                    </a-form-item>
                  </template>
                </template>
                <!-- 多选 -->
                <template v-if="subChild.type === 7">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item :class="{'smallMarginBottom': drawerStatus == 3}" :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']" v-if="x.language_id === activeKey" :rules="[{ required: subChild.is_must, message: `请选择${x.attr_name}` }]">
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <div v-if="drawerStatus != 3 && subChild.display_type === 1">
                        <a-checkbox-group @change="changeOtherLanguageValue(subChild)" v-model:value="x.value" :options="subChild.type_json.option_list">
                          <template #label="{ value }">
                            <span>{{ subChild.type_json.option_list.find(e => e.value === value).option_label_list.find(e => e.language_id === activeKey).label }}</span>
                          </template>
                        </a-checkbox-group>
                        <div v-if="x.attr_tips" class="description" style="padding-left: 0;">{{ x.attr_tips }}</div>
                      </div>
                      <a-select :getPopupContainer="trigger => trigger.parentNode" show-search :filter-option="filterOption" v-if="drawerStatus != 3 && subChild.display_type === 2" @change="changeOtherLanguageValue(subChild)" mode="multiple" allow-clear v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`">
                        <a-select-option :label="option.option_label_list.find(e => e.language_id === activeKey)?.label" v-for="(option, optionIndex) in subChild.type_json.option_list" :value="option.value">
                          {{ option.option_label_list.find(e => e.language_id === activeKey).label }}
                        </a-select-option>
                      </a-select>
                      <div v-if="drawerStatus === 3 && x.value && x.value.length" style="display: flex;flex-wrap: wrap;gap: 4px;position: relative;top: -8px;">
                        <a-tag style="margin: 0;" v-for="(valLabel, valLabelIndex) in subChild.type_json.option_list.filter(e => x.value.indexOf(e.value) != -1).map(e => e.option_label_list.find(m => m.language_id === activeKey).label)" :key="valLabelIndex">{{ valLabel }}</a-tag>
                      </div>
                      <span v-if="drawerStatus === 3 && (!x.value || !x.value.length)" style="position: relative;top: -8px;">--</span>
                    </a-form-item>
                  </template>
                </template>
                <!-- 超链接 -->
                <template v-if="subChild.type === 8">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item
                      :class="{'smallMarginBottom': drawerStatus == 3}"
                      :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']"
                      v-if="x.language_id === activeKey"
                      :rules="[
                        { required: subChild.is_must, message: `请输入${x.attr_name}` },
                        { validator: validateLink, message: `请输入正确的网址` }
                      ]"
                      >
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <a-input v-if="drawerStatus != 3" allow-clear v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`" />
                      <a v-if="drawerStatus == 3 && x.value" style="position: relative;top: -8px;word-break: break-all;" target="_blank" :href="x.value" class="aLink">{{ x.value }}</a>
                      <span v-if="drawerStatus == 3 && !x.value" style="position: relative;top: -8px;">--</span>
                    </a-form-item>
                  </template>
                </template>
                <!-- 图片 -->
                <template v-if="subChild.type === 9">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item :class="{'smallMarginBottom': drawerStatus == 3}" :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']" v-if="x.language_id === activeKey" :rules="[{ required: subChild.is_must,  message: `请选择${x.attr_name}` }]">
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <MultipleUploader
                        v-if="drawerStatus != 3"
                        @change="templateFormRef.validateFields([[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']])"
                        :type="[['jpg','jpeg'], ['png'], ['gif']].filter((e, eindex) => subChild.type_json.image_types.indexOf(eindex + 1) != -1).flat()"
                        :size="subChild.type_json.image_size"
                        :size_unit="subChild.type_json.image_size_unit"
                        :limit="subChild.type_json.image_count"
                        style="width: 100%;"
                        v-model:value="x.value"
                        :column="6"
                        :uploadFn="(params) => uploadFn({ 
                          ...params, 
                          attr_id: Number(subChild.id.split('_')[1]), 
                          category_template_id: category_template_id 
                        })"
                      />
                      <span v-if="x.attr_tips && drawerStatus != 3" class="description2">{{ x.attr_tips }}</span>
                      <div v-if="drawerStatus === 3" class="removeOtherContent">
                        <FilesViewer
                          v-if="x.value && x.value.length"
                          style="width: 100%;"
                          v-model:value="x.value"
                        />
                        <span v-else>--</span>
                      </div>
                    </a-form-item>
                  </template>
                </template>
                <!-- 视频 -->
                <template v-if="subChild.type === 10">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item :class="{'smallMarginBottom': drawerStatus == 3}" :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']" v-if="x.language_id === activeKey" :rules="[{ required: subChild.is_must,  message: `请选择${x.attr_name}` }]">
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <MultipleUploader
                        v-if="drawerStatus != 3"
                        @change="templateFormRef.validateFields([[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']])"
                        :type="['mp4', 'avi', 'mov', 'wmv', 'flv'].filter((e, eindex) => subChild.type_json.video_types.indexOf(eindex + 1) != -1)"
                        :size="subChild.type_json.video_size"
                        :size_unit="subChild.type_json.video_size_unit"
                        :limit="subChild.type_json.video_count"
                        style="width: 100%;"
                        v-model:value="x.value"
                        :column="6"
                        :uploadFn="(params) => uploadFn({ 
                          ...params, 
                          attr_id: Number(subChild.id.split('_')[1]), 
                          category_template_id: category_template_id 
                        })"
                      />
                      <span v-if="x.attr_tips && drawerStatus != 3" class="description2">{{ x.attr_tips }}</span>
                      <div v-if="drawerStatus === 3" class="removeOtherContent">
                        <FilesViewer
                          v-if="x.value && x.value.length"
                          style="width: 100%;"
                          v-model:value="x.value"
                        />
                        <span v-else>--</span>
                      </div>
                    </a-form-item>
                  </template>
                </template>
                <!-- 文件 -->
                <template v-if="subChild.type === 11">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item :class="{'smallMarginBottom': drawerStatus == 3}" :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']" v-if="x.language_id === activeKey" :rules="[{ required: subChild.is_must,  message: `请选择${x.attr_name}` }]">
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <!-- <template #label>
                        <a-tooltip :mouseEnterDelay="x.attr_name.length <= 15 ? 10000 : 0.1">
                          <template #title>
                            <span>{{ x.attr_name }}</span>
                          </template>
                          <span>{{ x.attr_name.length > 15 ? x.attr_name.slice(0,15) + '...' : x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                        </a-tooltip>
                      </template> -->
                      <MultipleUploader
                        v-if="drawerStatus != 3"
                        @change="templateFormRef.validateFields([[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']])"
                        :type="[['doc','docx'], ['pdf'],['xls', 'xlsx'],['zip'],['rar'], ['psd']]
                        .filter((e, eindex) => subChild.type_json.file_type_ids.indexOf(eindex + 1) != -1)
                        .flat()"
                        :size="subChild.type_json.file_size"
                        :size_unit="subChild.type_json.file_size_unit"
                        :limit="subChild.type_json.file_count"
                        style="width: 100%;"
                        v-model:value="x.value"
                        :column="6"
                        :uploadFn="(params) => uploadFn({ 
                          ...params, 
                          attr_id: Number(subChild.id.split('_')[1]), 
                          category_template_id: category_template_id 
                        })"
                      />
                      <span v-if="x.attr_tips  && drawerStatus != 3" class="description2">{{ x.attr_tips }}</span>
                      <div v-if="drawerStatus === 3" class="removeOtherContent">
                        <FilesViewer
                          v-if="x.value && x.value.length"
                          style="width: 100%;"
                          v-model:value="x.value"
                        />
                        <span v-else>--</span>
                      </div>
                    </a-form-item>
                  </template>
                </template>
                <!-- 引用 -->
                <template v-if="subChild.type === 12">
                  <template v-for="(x, xi) in subChild.language_config" :key="xi">
                    <a-form-item :class="{'smallMarginBottom': drawerStatus == 3}" :name="[itemIndex, 'group', childIndex, subChildIndex, 'language_config', xi, 'value']" v-if="x.language_id === activeKey"
                      :rules="[
                        { required: subChild.is_must, message: `请选择${x.attr_name}` },
                      ]"
                      >
                      <template #label>
                        <span :class="{'highlightItem': linkToTarget == subChild.id }" :id="subChild.id">{{ x.attr_name }}<span class="is_must" v-show="subChild.is_must  && drawerStatus != 3">*</span></span>
                      </template>
                      <div v-if="drawerStatus != 3" >
                        <a-select
                          :getPopupContainer="trigger => trigger.parentNode"
                          v-if="subChild.type_json.available_reference_type === 1"
                          @change="changeOtherLanguageValue(subChild)"
                          show-search
                          :filter-option="filterOption"
                          allow-clear
                          :mode="subChild.type_json.option_type === 2 ? 'multiple' : null"
                          v-model:value="x.value"
                          :options="subChild.type_json.options.find(option => option.language_id === activeKey) ? subChild.type_json.options.find(option => option.language_id === activeKey).options : subChild.type_json.options[0].options"
                          :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`">
                        </a-select>
                        <a-select
                          :getPopupContainer="trigger => trigger.parentNode"
                          labelInValue
                          v-if="subChild.type_json.available_reference_type === 2 || subChild.type_json.available_reference_type === 3"
                          :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`"
                          v-model:value="x.value"
                          :options="subChild.type_json.options.find(option => option.language_id === activeKey).options"
                          allow-clear
                          show-search
                          :filter-option="false"
                          @search="(val) => { subChild.type_json.searchStr = val;debounceSearchDataSourceOptions(subChild) }"
                          :mode="subChild.type_json.option_type === 2 ? 'multiple' : null"
                          @change="(val, option) => { changeOtherLanguageValue(subChild);updateSelectedOptions(option, subChild) }"
                          @popupScroll="e => casNohandlePopupScroll(e, subChild)"
                        />
                      </div>
                      <div v-else style="position: relative;top: -8px;">
                        <span v-if="subChild.type_json.available_reference_type === 1">
                          <!-- 复合属性 -->
                          <div style="display: flex;flex-wrap: wrap;gap: 4px;" v-if="subChild.type_json.options.find(option => option.language_id === activeKey)">
                            <template v-if="x.value && x.value.length" v-for="(valueItem, valueItemIndex) in x.value">
                              <span v-show="subChild.type_json.option_type === 1">{{ subChild.type_json.options.find(option => option.language_id === activeKey).options.find(e => e.id === valueItem).mult_attr_value_name }}</span>
                              <a-tag style="margin: 0;" v-show="subChild.type_json.option_type === 2">{{ subChild.type_json.options.find(option => option.language_id === activeKey).options.find(e => e.id === valueItem).mult_attr_value_name }}</a-tag>
                            </template>
                            <span v-else>--</span>
                          </div>
                          <div v-else style="display: flex;flex-wrap: wrap;gap: 4px;">
                            <template v-if="x.value && x.value.length" v-for="(valueItem, valueItemIndex) in x.value">
                              <span v-show="subChild.type_json.option_type === 1">{{ subChild.type_json.options[0].options.find(e => e.id === valueItem).mult_attr_value_name }}</span>
                              <a-tag style="margin: 0;" v-show="subChild.type_json.option_type === 2">{{ subChild.type_json.options[0].options.find(e => e.id === valueItem).mult_attr_value_name }}</a-tag>
                            </template>
                            <span v-else>--</span>
                          </div>
                        </span>
                        <div v-else>
                          <!-- 数据源 -->
                          <!-- 多选 -->
                           <div v-if="subChild.type_json.option_type === 2" style="display: flex;flex-wrap: wrap;gap: 4px;">
                            <template v-if="x.value && x.value.length" v-for="(valueItem, valueItemIndex) in x.value">
                              <a-tag style="margin: 0;">{{ valueItem.label }}</a-tag>
                            </template>
                            <span v-else>--</span>
                          </div>
                          <!-- 单选 -->
                          <span v-else>{{ x.value ? x.value.label : '--' }}</span>
                        </div>
                      </div>
                    </a-form-item>
                  </template>
                </template>
              </a-col>
            </a-row>
          </template>
          <!-- 表格 -->
          <a-table v-show="item.showChild" :style="item.is_support_adding_rows && drawerStatus != 3 ? '' : 'margin-bottom:12px;'" :bordered="item.is_display_border" :scroll="{ x: item.columns.find(e => e.language_id === activeKey).column.length * 160 }" v-if="item.type == 100 && item.columns && item.columns.length && item.data.length" size="small" :pagination="false" :columns="item.columns.find(e => e.language_id === activeKey).column" :data-source="item.data" class="formTable">
            <template #headerCell="{ title, column }">
              <template v-if="column.tips && drawerStatus != 3">
                <span :class="{'highlightItem': linkToTarget == column.id && linkToTarget != null }" :id="column.id" ><span v-show="column.is_must && drawerStatus != 3" style="color: #ff4d4f;">*</span>{{ `${column.title}`}}</span>
                <div style="color: rgba(0,0,0,0.5);">{{ `(${column.tips})` }}</div>
                <!-- <a-tooltip :mouseEnterDelay="` (${column.tips})`.length <= 14 ? 10000 : 0.1">
                  <template #title>
                    <span>{{ column.tips }}</span>
                  </template>
                  <div style="color: rgba(0,0,0,0.5);">{{ ` (${column.tips})`.length > 14 ? ` (${column.tips})`.slice(0,14) + '...' : ` (${column.tips})` }}</div>
                </a-tooltip> -->
              </template>
              <template v-else>
                <span :class="{'highlightItem': linkToTarget == column.id && linkToTarget != null }" :id="column.id"><span v-show="column.is_must && drawerStatus != 3" style="color: #ff4d4f;">*</span>{{ `${column.title}`}}</span>
              </template>
            </template>
            <template #bodyCell="{ text, record, index, column }">
              <template v-if="column.key === 'index'">{{ index + 1 }}</template>
              <template v-else-if="column.key === 'operate'">
                <a-button v-show="item.data.length != 1" @click="() => { item.data.splice(index, 1) }">
                  <template #icon><MinusOutlined /></template>
                </a-button>
              </template>
              <!-- 单行文本 -->
              <template v-else-if="record[column.key].type === 1 || record[column.key].type === 2">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item
                    :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']"
                    v-if="x.language_id === activeKey"
                    :rules="[
                      { required: record[column.key].is_must, message: `请输入${x.attr_name}` },
                      { validator: (_rule, value) => validateStr(_rule, value, record[column.key].type_json.word_limit_count), message: `输入内容不可超过${record[column.key].type_json.word_limit_count}字符` }
                    ]"
                  >
                  <a-input v-if="drawerStatus != 3" allow-clear v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`" />
                  <span v-else>{{ x.value ? x.value : '--' }}</span>
                </a-form-item>
                </template>
              </template>
              <!-- 多行文本 -->
              <!-- <template v-if="record[column.key].type === 2">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item v-if="x.language_id === activeKey"
                    :rules="[
                      { required: record[column.key].is_must, message: `请输入${x.attr_name}` },
                      { validator: (_rule, value) => validateStr(_rule, value, record[column.key].type_json.word_limit_count), message: `输入内容不可超过${record[column.key].type_json.word_limit_count}字符` }
                    ]"
                    >
                    <a-textarea :auto-size="{ minRows: 4, maxRows: 4 }" :rows="4" allow-clear v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`" />
                  </a-form-item>
                </template>
              </template> -->
              <!-- 数值 -->
              <template v-else-if="record[column.key].type === 3">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item
                    :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']"
                    v-if="x.language_id === activeKey"
                    :rules="[
                      { required: record[column.key].is_must, message: `请输入${x.attr_name}` },
                    ]"
                  >
                    <a-input-number
                      v-if="drawerStatus != 3"
                      @change="changeOtherLanguageValue(record[column.key])"
                      string-mode
                      @blur="handleNumberBlur(x, record[column.key])"
                      v-model:value="x.value"
                      :formatter="value => {
                        if (!(record[column.key].type_json.display_format == 1) || !value) return value;
                        const [intPart, decimalPart] = value.toString().split('.');
                        return decimalPart !== undefined 
                          ? intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '.' + decimalPart
                          : intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                      }"
                      :parser="value => record[column.key].type_json.display_format == 1 ? value.replace(/,/g, '') : value"
                      :controls="false"
                      :min="record[column.key].type_json.min_value"
                      :max="record[column.key].type_json.max_value"
                      style="width: 100%;"
                      :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`"
                    />
                    <span v-else>{{ x.value ? x.value : '--' }}</span>
                  </a-form-item>
                </template>
              </template>
              <!-- 单位 -->
              <template v-else-if="record[column.key].type === 4">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item
                    :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']"
                    v-if="x.language_id === activeKey"
                    :rules="[
                      { required: record[column.key].is_must, message: `请输入${x.attr_name}` },
                    ]"
                  >
                    <a-input-number
                      v-if="drawerStatus != 3"
                      @change="changeOtherLanguageValue(record[column.key])"
                      string-mode
                      v-model:value="x.value"
                      :formatter="value => {
                        if (!(record[column.key].type_json.display_format == 1) || !value) return value;
                        const [intPart, decimalPart] = value.toString().split('.');
                        return decimalPart !== undefined 
                          ? intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '.' + decimalPart
                          : intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                      }"
                      :parser="value => record[column.key].type_json.display_format == 1 ? value.replace(/,/g, '') : value"
                      :controls="false"
                      :precision="record[column.key].type_json.unit_type == 2 ? 2
                        : (record[column.key].template_display_unit_precision || record[column.key].template_display_unit_precision === 0) ? record[column.key].template_display_unit_precision
                        : record[column.key].unit_input_attr_data.find(unit => unit.metering_unit_id === record[column.key].template_display_unit).decimal_quantity"
                      :min="record[column.key].type_json.min_value"
                      :max="record[column.key].type_json.max_value"
                      style="width: 100%;"
                      :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`"
                    >
                      <template #addonAfter>
                        <span v-if="record[column.key].type_json.unit_type == 1">{{ meteringUnitOptions.find(e => e.value === record[column.key].template_display_unit)?.label }}</span>
                        <span v-if="record[column.key].type_json.unit_type == 2">{{ currencySymbolOptions.find(e => e.value === record[column.key].template_display_unit)?.label }}</span>
                      </template>
                    </a-input-number>
                    <span v-else>{{ x.value ? x.value : '--' }} 
                      <span v-if="record[column.key].type_json.unit_type == 1">{{ meteringUnitOptions.find(e => e.value === record[column.key].template_display_unit)?.label }}</span>
                      <span v-if="record[column.key].type_json.unit_type == 2">{{ currencySymbolOptions.find(e => e.value === record[column.key].template_display_unit)?.label }}</span>
                    </span>
                  </a-form-item>
                </template>
              </template>
              <!-- 日期时间选择 -->
              <template v-else-if="record[column.key].type === 5">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item v-if="x.language_id === activeKey"
                    :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']"
                    :rules="[
                      { required: record[column.key].is_must, message: `请选择${x.attr_name}` },
                    ]"
                  >
                    <a-date-picker :getPopupContainer="trigger => trigger.parentNode" v-if="drawerStatus != 3 && record[column.key].type_json.style_type == 1" @change="changeOtherLanguageValue(record[column.key])" :disabled-date="record[column.key].type_json.time_limit ? disabledDate : null" :disabled-time="record[column.key].type_json.time_limit ? disabledDateTime : null" :valueFormat="'YYYY-MM-DD HH:mm:ss'" v-model:value="x.value" style="width: 100%;" show-time :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`"/>
                    <a-date-picker :getPopupContainer="trigger => trigger.parentNode" v-if="drawerStatus != 3 && record[column.key].type_json.style_type == 2" @change="changeOtherLanguageValue(record[column.key])" :disabled-date="record[column.key].type_json.time_limit ? disabledDate : null" :disabled-time="record[column.key].type_json.time_limit ? disabledDateTime : null" :valueFormat="'YYYY-MM'" v-model:value="x.value" style="width: 100%;" picker="month" show-time :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`"/>
                    <a-date-picker :getPopupContainer="trigger => trigger.parentNode" v-if="drawerStatus != 3 && record[column.key].type_json.style_type == 3" @change="changeOtherLanguageValue(record[column.key])" :disabled-date="record[column.key].type_json.time_limit ? disabledDate : null" :disabled-time="record[column.key].type_json.time_limit ? disabledDateTime : null" :valueFormat="'YYYY-MM-DD'" v-model:value="x.value" style="width: 100%;" :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`" />
                    <a-time-picker :getPopupContainer="trigger => trigger.parentNode" v-if="drawerStatus != 3 && record[column.key].type_json.style_type == 4" @change="changeOtherLanguageValue(record[column.key])" :disabled-date="record[column.key].type_json.time_limit ? disabledDate : null" :disabled-time="record[column.key].type_json.time_limit ? disabledDateTime : null" :valueFormat="'HH:mm:ss'" v-model:value="x.value" style="width: 100%;" :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`" />
                    <span v-if="drawerStatus === 3">{{ x.value ? x.value : '--' }}</span>
                  </a-form-item>
                </template>
              </template>
              <!-- 单选 -->
              <template v-else-if="record[column.key].type === 6">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']" v-if="x.language_id === activeKey" :rules="[{ required: record[column.key].is_must, message: `请选择${x.attr_name}` }]">
                    <a-radio-group @change="changeOtherLanguageValue(record[column.key])" v-if="record[column.key].display_type === 1 && drawerStatus != 3" v-model:value="x.value" style="width: 100%;">
                      <a-radio v-for="(option, optionIndex) in record[column.key].type_json.option_list" :value="option.value">
                        {{ option.option_label_list.find(e => e.language_id === activeKey).label }}
                      </a-radio>
                    </a-radio-group>
                    <!-- <div v-if="record[column.key].display_type === 1 && x.attr_tips" class="description">{{ x.attr_tips }}</div> -->
                    <a-select :getPopupContainer="trigger => trigger.parentNode" show-search :filter-option="filterOption" @change="changeOtherLanguageValue(record[column.key])" allow-clear v-if="record[column.key].display_type === 2 && drawerStatus != 3" v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`">
                      <a-select-option :label="option.option_label_list.find(e => e.language_id === activeKey)?.label" v-for="(option, optionIndex) in record[column.key].type_json.option_list" :value="option.value">
                        {{ option.option_label_list.find(e => e.language_id === activeKey).label }}
                      </a-select-option>
                    </a-select>
                    <span v-if="drawerStatus === 3">{{ x.value ? record[column.key].type_json.option_list.find(e => e.value === x.value).option_label_list.find(e => e.language_id === activeKey).label : '--' }}</span>
                  </a-form-item>
                </template>
              </template>
              <!-- 多选 -->
              <template v-else-if="record[column.key].type === 7">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']" v-if="x.language_id === activeKey" :rules="[{ required: record[column.key].is_must, message: `请选择${x.attr_name}` }]">
                    <div v-if="record[column.key].display_type === 1 && drawerStatus != 3">
                      <a-checkbox-group @change="changeOtherLanguageValue(record[column.key])" v-model:value="x.value" :options="record[column.key].type_json.option_list">
                        <template #label="{ value }">
                          <span>{{ record[column.key].type_json.option_list.find(e => e.value === value).option_label_list.find(e => e.language_id === activeKey).label }}</span>
                        </template>
                      </a-checkbox-group>
                      <!-- <div v-if="x.attr_tips" class="description">{{ x.attr_tips }}</div> -->
                    </div>
                    <a-select :getPopupContainer="trigger => trigger.parentNode" show-search :filter-option="filterOption" @change="changeOtherLanguageValue(record[column.key])" mode="multiple" allow-clear v-if="record[column.key].display_type === 2 && drawerStatus != 3" v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请选择${x.attr_name}`">
                      <a-select-option :label="option.option_label_list.find(e => e.language_id === activeKey)?.label" v-for="(option, optionIndex) in record[column.key].type_json.option_list" :value="option.value">
                        {{ option.option_label_list.find(e => e.language_id === activeKey).label }}
                      </a-select-option>
                    </a-select>
                    <div v-if="drawerStatus === 3 && x.value && x.value.length" style="display: flex;flex-wrap: wrap;gap: 4px;">
                      <a-tag style="margin: 0;" v-for="(valLabel, valLabelIndex) in record[column.key].type_json.option_list.filter(e => x.value.indexOf(e.value) != -1).map(e => e.option_label_list.find(m => m.language_id === activeKey).label)" :key="valLabelIndex">{{ valLabel }}</a-tag>
                    </div>
                    <span v-if="drawerStatus === 3 && (!x.value || !x.value.length)">--</span>
                  </a-form-item>
                </template>
              </template>
              <!-- 超链接 -->
              <template v-else-if="record[column.key].type === 8">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item
                    :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']"
                    v-if="x.language_id === activeKey"
                    :rules="[
                      { required: record[column.key].is_must, message: `请输入${x.attr_name}` },
                      { validator: validateLink, message: `请输入正确的网址` }
                    ]"
                    >
                    <a-input v-if="drawerStatus != 3" allow-clear v-model:value="x.value" :placeholder="x.attr_tips ? x.attr_tips : `请输入${x.attr_name}`" />
                    <a v-if="drawerStatus == 3 && x.value" target="_blank" :href="x.value" class="aLink">{{ x.value }}</a>
                    <span v-if="drawerStatus == 3 && !x.value">--</span>
                  </a-form-item>
                </template>
              </template>
              <!-- 图片 -->
              <template v-else-if="record[column.key].type === 9">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']" v-show="x.language_id === activeKey" :rules="[{ required:  record[column.key].is_must,  message: `请选择${x.attr_name}` }]">
                    <a-modal :width="drawerStatus == 3 ? 520 : 580" v-model:open="x.visible" :title="drawerStatus != 3 ? '图片选择' : '查看图片'">
                      <div v-if="drawerStatus != 3" style="width: 100%;margin-top:20px">
                        <MultipleUploader
                          @change="templateFormRef.validateFields([[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']])"
                          :type="[['jpg','jpeg'], ['png'], ['gif']].filter((e, eindex) => record[column.key].type_json.image_types.indexOf(eindex + 1) != -1).flat()"
                          :size="record[column.key].type_json.image_size"
                          :size_unit="record[column.key].type_json.image_size_unit"
                          :limit="record[column.key].type_json.image_count"
                          :column="3"
                          style=""
                          v-model:value="x.value"
                          :uploadFn="(params) => uploadFn({ 
                            ...params, 
                            attr_id: Number(record[column.key].id.split('_')[1]), 
                            category_template_id: category_template_id 
                          })"
                        />
                      </div>
                      <div v-else style="width: 100%;margin-top:20px">
                        <FilesViewer
                        v-model:value="x.value"
                        style="width: 100%;"
                      />
                      </div>
                      <template #footer>
                        <a-button @click="x.visible = false">关闭</a-button>
                      </template>
                    </a-modal>
                    <a-badge :count="x.value ? x.value.length : 0" :offset="[10, 0]">
                      <span class="textBtn" @click="x.visible = true" style="padding-left: 4px;">{{ drawerStatus != 3 ? '选择图片' : '查看图片' }}</span>
                    </a-badge>
                    <!-- <span v-if="x.attr_tips" class="description2">{{ x.attr_tips }}</span> -->
                  </a-form-item>
                </template>
              </template>
              <!-- 视频 -->
              <template v-else-if="record[column.key].type === 10">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']" v-show="x.language_id === activeKey" :rules="[{ required:  record[column.key].is_must,  message: `请选择${x.attr_name}` }]">
                    <a-modal :width="drawerStatus == 3 ? 520 : 580" v-model:open="x.visible" :title="drawerStatus != 3 ? '视频选择' : '查看视频'">
                      <div v-if="drawerStatus != 3" style="width: 100%;margin-top:20px">
                        <MultipleUploader
                          @change="templateFormRef.validateFields([[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']])"
                          :type="['mp4', 'avi', 'mov', 'wmv', 'flv'].filter((e, eindex) => record[column.key].type_json.video_types.indexOf(eindex + 1) != -1)"
                          :size="record[column.key].type_json.video_size"
                          :size_unit="record[column.key].type_json.video_size_unit"
                          :limit="record[column.key].type_json.video_count"
                          v-model:value="x.value"
                          :column="3"
                          :uploadFn="(params) => uploadFn({ 
                            ...params, 
                            attr_id: Number(record[column.key].id.split('_')[1]), 
                            category_template_id: category_template_id 
                          })"
                        />
                      </div>
                      <div v-else style="width: 100%;margin-top:20px">
                        <FilesViewer
                        v-model:value="x.value"
                        style="width: 100%;"
                      />
                      </div>
                      <template #footer>
                        <a-button @click="x.visible = false">关闭</a-button>
                      </template>
                    </a-modal>
                    <a-badge :count="x.value ? x.value.length : 0" :offset="[10, 0]">
                      <span class="textBtn" @click="x.visible = true" style="padding-left: 4px;">{{ drawerStatus != 3 ? '选择视频' : '查看视频' }}</span>
                    </a-badge>
                    <!-- <span v-if="x.attr_tips" class="description2">{{ x.attr_tips }}</span> -->
                  </a-form-item>
                </template>
              </template>
              <!-- 文件 -->
              <template v-else-if="record[column.key].type === 11">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']" v-show="x.language_id === activeKey" :rules="[{ required:  record[column.key].is_must,  message: `请选择${x.attr_name}` }]">
                    <a-modal :width="drawerStatus == 3 ? 520 : 580" v-model:open="x.visible" :title="drawerStatus != 3 ? '文件选择' : '查看文件'">
                      <div v-if="drawerStatus != 3" style="width: 100%;margin-top:20px">
                        <MultipleUploader
                          @change="templateFormRef.validateFields([[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']])"
                          :type="[['doc','docx'], ['pdf'],['xls', 'xlsx'],['zip'],['rar'], ['psd']]
                          .filter((e, eindex) => record[column.key].type_json.file_type_ids.indexOf(eindex + 1) != -1)
                          .flat()"
                          :size="record[column.key].type_json.file_size"
                          :size_unit="record[column.key].type_json.file_size_unit"
                          :limit="record[column.key].type_json.file_count"
                          v-model:value="x.value"
                          :column="3"
                          :uploadFn="(params) => uploadFn({ 
                            ...params, 
                            attr_id: Number(record[column.key].id.split('_')[1]), 
                            category_template_id: category_template_id 
                          })"
                        />
                      </div>
                      <div v-else style="width: 100%;margin-top:20px">
                        <FilesViewer
                        v-model:value="x.value"
                        style="width: 100%;"
                      />
                      </div>
                      <template #footer>
                        <a-button @click="x.visible = false">关闭</a-button>
                      </template>
                    </a-modal>
                    <a-badge :count="x.value ? x.value.length : 0" :offset="[10, 0]">
                      <span class="textBtn" @click="x.visible = true" style="padding-left: 4px;">{{ drawerStatus != 3 ? '选择文件' : '查看文件' }}</span>
                    </a-badge>
                    <!-- <span v-if="x.attr_tips" class="description2">{{ x.attr_tips }}</span> -->
                  </a-form-item>
                </template>
              </template>
              <!-- 引用 -->
              <template v-else-if="record[column.key].type === 12">
                <template v-for="(x, xi) in record[column.key].language_config" :key="xi">
                  <a-form-item
                    :name="[itemIndex, 'data', index, column.key, 'language_config', xi, 'value']"
                    v-if="x.language_id === activeKey"
                    :rules="[
                      { required: record[column.key].is_must, message: `请选择${x.attr_name}` },
                    ]"
                  >
                    <!-- 可编辑状态 -->
                    <div v-if="drawerStatus != 3">
                      <!-- 引用类型 1：静态选项 -->
                      <a-select
                        :getPopupContainer="trigger => trigger.parentNode"
                        v-if="record[column.key].type_json.available_reference_type === 1"
                        @change="changeOtherLanguageValue(record[column.key])"
                        show-search
                        :filter-option="filterOption"
                        allow-clear
                        :mode="record[column.key].type_json.option_type === 2 ? 'multiple' : null"
                        v-model:value="x.value"
                        :options="record[column.key].type_json.options.find(option => option.language_id === activeKey)?.options || record[column.key].type_json.options[0].options"
                        :placeholder="x.attr_tips || `请选择${x.attr_name}`"
                      />

                      <!-- 引用类型 2 或 3：需要远程搜索 -->
                      <a-select
                        v-else
                        :getPopupContainer="trigger => trigger.parentNode"
                        labelInValue
                        allow-clear
                        show-search
                        :filter-option="false"
                        :placeholder="x.attr_tips || `请选择${x.attr_name}`"
                        :mode="record[column.key].type_json.option_type === 2 ? 'multiple' : null"
                        v-model:value="x.value"
                        :options="record[column.key].type_json.options.find(option => option.language_id === activeKey)?.options || []"
                        @search="val => { record[column.key].type_json.searchStr = val; debounceSearchDataSourceOptions(record[column.key]) }"
                        @change="(val, option) => { changeOtherLanguageValue(record[column.key]); updateSelectedOptions(option, record[column.key]) }"
                        @popupScroll="e => casNohandlePopupScroll(e, record[column.key])"
                      />
                    </div>

                    <!-- 查看状态 -->
                    <span v-else>
                      <!-- 引用类型 1：静态选项 -->
                      <span v-if="record[column.key].type_json.available_reference_type === 1">
                      <!-- 复合属性 -->
                        <div style="display: flex;flex-wrap: wrap;gap: 4px;" v-if="record[column.key].type_json.options.find(option => option.language_id === activeKey)">
                          <template v-if="x.value && x.value.length" v-for="(valueItem, valueItemIndex) in x.value">
                            <span v-show="record[column.key].type_json.option_type === 1">{{ record[column.key].type_json.options.find(option => option.language_id === activeKey).options.find(e => e.id === valueItem).mult_attr_value_name }}</span>
                            <a-tag style="margin: 0;" v-show="record[column.key].type_json.option_type === 2">{{ record[column.key].type_json.options.find(option => option.language_id === activeKey).options.find(e => e.id === valueItem).mult_attr_value_name }}</a-tag>
                          </template>
                          <span v-else>--</span>
                        </div>
                        <div v-else style="display: flex;flex-wrap: wrap;gap: 4px;">
                          <template v-if="x.value && x.value.length" v-for="(valueItem, valueItemIndex) in x.value">
                            <span v-show="record[column.key].type_json.option_type === 1">{{ record[column.key].type_json.options[0].options.find(e => e.id === valueItem).mult_attr_value_name }}</span>
                            <a-tag style="margin: 0;" v-show="record[column.key].type_json.option_type === 2">{{ record[column.key].type_json.options[0].options.find(e => e.id === valueItem).mult_attr_value_name }}</a-tag>
                          </template>
                          <span v-else>--</span>
                        </div>
                        <!-- <span v-if="x.value">
                          <span v-if="record[column.key].type_json.options.find(option => option.language_id === activeKey)">
                            <span v-if="record[column.key].type_json.options.find(option => option.language_id === activeKey).options.find(e => e.id === x.value)">
                              {{ record[column.key].type_json.options.find(option => option.language_id === activeKey).options.find(e => e.id === x.value).mult_attr_value_name }}
                            </span>
                            <span v-else>
                              {{ record[column.key].type_json.options[0].options.find(e => e.id === x.value)?.mult_attr_value_name || '--' }}
                            </span>
                          </span>
                          <span v-else>
                            {{ record[column.key].type_json.options[0].options.find(e => e.id === x.value)?.mult_attr_value_name || '--' }}
                          </span>
                        </span>
                        <span v-else>--</span> -->
                      </span>
                      <!-- 引用类型 2 或 3：需要远程搜索 -->
                      <span v-else>
                        <!-- 多选 -->
                         <div v-if="record[column.key].type_json.option_type === 2" style="display: flex;flex-wrap: wrap;gap: 4px;">
                          <template v-if="x.value && x.value.length" v-for="(valueItem, valueItemIndex) in x.value">
                            <a-tag style="margin: 0;text-wrap: auto;">{{ valueItem.label }}</a-tag>
                          </template>
                          <span v-else>--</span>
                        </div>
                        <!-- 单选 -->
                        <span v-else>{{ x.value ? x.value.label : '--' }}</span>
                      </span>
                    </span>
                  </a-form-item>
                </template>
              </template>
            </template>
          </a-table>
          <!-- <div v-show="item.showChild" v-if="item.is_support_adding_rows && item.columns && drawerStatus != 3" class="totalBox">
            <div class="text">添加行</div>
            <div class="btnBox">
              <a-button id="casListSplice" @click="() => { item.data.push(cloneDeep(item.standByRow)) }" >
                <template #icon><PlusOutlined /></template>
              </a-button>
            </div>
          </div> -->
          <a-button @click="() => { item.data.push(cloneDeep(item.standByRow)) }" v-show="item.showChild" v-if="item.is_support_adding_rows && item.columns && drawerStatus != 3" style="width: 100%;margin-top: 4px;margin-bottom: 20px;">添加行</a-button>
        </template>
      </template>
    </a-form>
    <div v-if="!templateFormData.find(e => e.language_config.find(y => y.language_id === activeKey)) && drawerStatus != 3" style="color:#606266;top: 12px;margin-bottom: 16px;">请为相关属性维护对应语言</div>
  </div>
  <div style="border: 1px solid rgba(5, 5, 5, 0.06);border-top: none;margin-bottom: 20px;padding: 20px;" v-show="!templateFormData.length && visible">
    <a-spin></a-spin>
  </div>
</template>

<script setup lang="ts">
  import { debounce } from 'lodash';
  import FilesViewer from '@/components/FilesViewer.vue'
  import { pinyin } from 'pinyin-pro'
  import dayjs from 'dayjs';
  import { cloneDeep } from 'lodash'
  import { h } from 'vue';
  import { MinusOutlined, PlusOutlined, CaretDownOutlined, CaretRightOutlined, SearchOutlined, DoubleLeftOutlined } from '@ant-design/icons-vue';
  import type { FormInstance } from 'ant-design-vue';
  import MultipleUploader from '@/components/MultipleUploader.vue';
  import { validateStr, validateLink, filterOption, handleDataSourceResponseData } from '@/utils/index';
  const props = defineProps({
    removeTabs: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: true
    },
    canUseLanguages: {
      type: Array<{ value, label }>,
      default: []
    },
    meteringUnitOptions: {
      type: Array<{ value, label }>,
      default: []
    },
    currencySymbolOptions: {
      type: Array<{ value, label }>,
      default: []
    },
    category_template_id: {
      type: Number
    },
    uploadFn: {
      type: Function as PropType<(params: {
        file: File
        attr_id?: number
        category_template_id?: number
      }) => Promise<{ data: { url: string } }>>,
    },
    // 远程搜索数据源方法映射数组
    mappingFnArrForReference: {
      type: Array<any>,
      default: []
    },
    activeLanguage: {
      type: Number
    },
    languageStickey: {
      type: Number,
      default: 0
    },
    drawerStatus: {
      type: Number,
      default: 1
    }
  });
  const templateFormRef = ref<FormInstance>();
  const templateFormData = ref([])
  const activeKey = ref(null)
  const searchDropdownVisible = ref(false)
  const linkToTargetTimer = ref(null)
  const searchDropdownInputValue = ref(null)
  const searchDropdownData = ref([])
  const linkToTarget = ref(null)
  const setDataLoading = ref(false)
  const emits = defineEmits(['clear', 'changeActiveKey']);
  const validateTemplateValue = async () => {
    var currentTabsKey = activeKey.value;
    var canUseKey = props.canUseLanguages.map(e => e.value)
    var boolean = true;
    try {
      for (const e of canUseKey) {
        activeKey.value = e;
        await nextTick();
        await templateFormRef.value.validateFields();
      }
      activeKey.value = currentTabsKey;
    } catch (error) {
      scrollToFirstError();
      boolean = false;
    }
    return boolean;
  };
  const casNohandlePopupScroll = (e, subChild) => {
    const { target } = e;
    const { scrollTop, scrollHeight, clientHeight } = target;
    if (scrollHeight - scrollTop === clientHeight) {
      searchDataSourceOptions(subChild)
    }
  };
  const debounceSearchDataSourceOptions = debounce((subChild) => {
    searchDataSourceOptions(subChild, true)
  }, 300)
  const searchDataSourceOptions = async (subChild, needClear = false) => {
    if (subChild.type_json.searching || (subChild.type_json.total <= subChild.type_json.options[0].options.length && !needClear)) return
    subChild.type_json.searching = true
    var remoteFn = props.mappingFnArrForReference.find(e => e.enum === subChild.type_json.data_source).fn
    if (needClear) {
      subChild.type_json.page = 0
    }
    try {
      var res = await remoteFn({ page: subChild.type_json.page + 1, pageSize: 20, status: 1, [props.mappingFnArrForReference.find(e => e.enum === subChild.type_json.data_source).searchKey]: subChild.type_json.searchStr })
      var pageData = handleDataSourceResponseData( res.data.list, subChild.type_json.data_source );
      props.canUseLanguages.forEach(language => {
        var currentOptions = subChild.type_json.options.find(e => e.language_id === language.value).options
        if (subChild.type_json.data_source === 2) {
          // 制造商特殊处理
          if (language.value === 2) {
            if (!needClear) {
              subChild.type_json.options.find(e => e.language_id === language.value).options = currentOptions.concat(pageData.map(e => ({ label: (e as any).en_label, value: e.value })))
            } else {
              subChild.type_json.options.find(e => e.language_id === language.value).options = pageData.map(e => ({ label: (e as any).en_label, value: e.value }))
            }
          } else {
            if (!needClear) {
              subChild.type_json.options.find(e => e.language_id === language.value).options = currentOptions.concat(pageData.map(e => ({ label: e.label, value: e.value })))
            } else {
              subChild.type_json.options.find(e => e.language_id === language.value).options = pageData.map(e => ({ label: e.label, value: e.value }))
            }
          }
        } else {
          if (!needClear) {
            subChild.type_json.options.find(e => e.language_id === language.value).options = currentOptions.concat(pageData)
          } else {
            subChild.type_json.options.find(e => e.language_id === language.value).options = pageData
            console.log('覆盖赋值', subChild.type_json.options.find(e => e.language_id === language.value).options);
            
          }
        }
      })
      subChild.type_json.page ++
      subChild.type_json.searching = false
    } catch (error) {
      console.log(error);
      
      subChild.type_json.searching = false
    }
  }
  const scrollToFirstError = () => {
    document.querySelector('.ant-form-item-has-error')?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  };
  const changeActiveKey = (val) => {
    emits('changeActiveKey', val)
    if (searchDropdownVisible.value) {
      searchAttrInputFocus()
    }
  }
  const handleNumberBlur = (item, subChild) => {
    if (!item.value) return;
    const num = parseFloat(item.value);
    if (isNaN(num)) return;

    const precision = subChild.type_json.precision;
    const factor = Math.pow(10, precision); // 计算 10 的 `precision` 次方

    let adjustedValue;
    switch (subChild.type_json.rounding_type) {
      case 1: // 四舍五入
        adjustedValue = (Math.round(num * factor) / factor).toFixed(precision);
        break;
      case 2: // 舍位（向下取）
        adjustedValue = (Math.trunc(num * factor) / factor).toFixed(precision);
        break;
        case 3: // 进位（向上取）
      const currentPrecision = num.toString().split('.')[1]?.length || 0;
      if (currentPrecision === precision) {
        adjustedValue = num.toFixed(precision); // 小数位数等于precision，直接返回原数据
      } else {
        adjustedValue = (Math.ceil(num * factor) / factor).toFixed(precision); // 进位处理
      }
      break;
      default:
        adjustedValue = num.toFixed(precision);
    }
    item.value = adjustedValue;
    changeOtherLanguageValue(subChild)
  };
  const disabledDate = (current) => {
    // Can not select days before today and today
    return current && current < dayjs().endOf('day');
  };
  // 辅助函数：生成一个范围数组
  const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };
  const disabledDateTime = () => {
    const now = dayjs(); // 获取当前时间
    const currentHour = now.hour(); // 当前小时
    const currentMinute = now.minute(); // 当前分钟
    const currentSecond = now.second(); // 当前秒

    return {
      // 禁用当前小时及之前的小时
      disabledHours: () => range(0, currentHour + 1),
      // 禁用当前分钟及之前的分钟（仅在当前小时内）
      disabledMinutes: (selectedHour) => {
        if (selectedHour === currentHour) {
          return range(0, currentMinute + 1);
        }
        return [];
      },
      // 禁用当前秒及之前的秒（仅在当前小时和分钟内）
      disabledSeconds: (selectedHour, selectedMinute) => {
        if (selectedHour === currentHour && selectedMinute === currentMinute) {
          return range(0, currentSecond + 1);
        }
        return [];
      },
    };
  };
  const changeOtherLanguageValue = (target) => {
    var val = target.language_config.find(e => e.language_id === activeKey.value).value
    target.language_config.forEach(e => {
      if (props.canUseLanguages.find(a => a.value === e.language_id)) {
        e.value = val
      }
    })
  }
  const changeTargetValueForRules = debounce((subChild, group) => {
    // 长宽高影响体积/面积
    if (subChild.type === 4 && ['长', '宽', '高'].find(e => e === subChild.name)) {
      var targetArea = null
      var targetVolume = null
      // console.log(subChild, group);
      var obj = {
        '长': null, // 长
        '宽': null, // 宽
        '高': null, // 高
      }
      for (const key in group) {
        group[key].forEach(x => {
          // 长宽高赋值
          if (x.name === '长' && (x.language_config[0].value || x.language_config[0].value === 0) && x.language_config[0].value != '') {
            obj['长'] = x.language_config[0].value
          }
          if (x.name === '宽' && (x.language_config[0].value || x.language_config[0].value === 0) && x.language_config[0].value != '') {
            obj['宽'] = x.language_config[0].value
          }
          if (x.name === '高' && (x.language_config[0].value || x.language_config[0].value === 0) && x.language_config[0].value != '') {
            obj['高'] = x.language_config[0].value
          }
          // 面积
          if (x.name === '面积') {
            targetArea = x
          }
          // 体积
          if (x.name === '体积') {
            targetVolume = x
          }
        })
      }
      // 赋值
      if (obj['长'] && obj['宽']) {
        targetArea.language_config.forEach(e => {
          e.value = Number(Number(obj['长']) * Number(obj['宽'])).toFixed(2)
        })
      }
      if (obj['长'] && obj['宽'] && obj['高']) {
        targetVolume.language_config.forEach(e => {
          e.value = Number(Number(obj['长']) * Number(obj['宽']) * Number(obj['高'])).toFixed(2)
        })
      }
    }
  }, 300)
  const updateSelectedOptions = (selectedItems, subchild) => {
    const result = [];
    const typeJson = subchild.type_json;

    if (!typeJson || !Array.isArray(typeJson.options)) return;

    // 统一 selectedItems 为数组结构，方便后续处理
    const selectedArr = typeJson.option_type === 1
      ? selectedItems ? [selectedItems] : []
      : Array.isArray(selectedItems) ? selectedItems : [];

    // 生成 selectedOptions（多语言选项的筛选和去重）
    typeJson.options.forEach(langGroup => {
      const { language_id, options } = langGroup;

      const matched = options.filter(opt =>
        selectedArr.some(sel => sel.value === opt.value)
      );

      if (matched.length > 0) {
        const uniqueMap = new Map();
        matched.forEach(item => {
          uniqueMap.set(item.value, item);
        });

        result.push({
          language_id,
          options: Array.from(uniqueMap.values())
        });
      }
    });
    console.log('before:', subchild.type_json.selectedOptions);
    const standByResult = cloneDeep(result);

    if (subchild.type_json.selectedOptions && subchild.type_json.selectedOptions.length) {
      subchild.type_json.selectedOptions.forEach(x => {
        const idx = standByResult.findIndex(e => e.language_id === x.language_id);
        if (idx !== -1) {
          const merged = standByResult[idx].options.concat(x.options);

          // 去重（按 value）
          const uniqueMap = new Map();
          merged.forEach(opt => {
            uniqueMap.set(opt.value, opt);
          });

          standByResult[idx].options = Array.from(uniqueMap.values());
        } else {
          // 如果旧的 language_id 在新 result 中没有，就直接添加
          standByResult.push(x);
        }
      });
    }

    // 更新 selectedOptions
    subchild.type_json.selectedOptions = standByResult;
    console.log('after:', subchild.type_json.selectedOptions);
    
    // 根据 option_type，处理 language_config 中的 value
    subchild.language_config = subchild.language_config.map(item => {
      const selectedLang = subchild.type_json.selectedOptions.find(e => e.language_id === item.language_id);
      if (!selectedLang) return item;

      if (typeJson.option_type === 1) {
        // 单选：value 是对象
        const option = selectedLang.options.find(o => o.value === item.value.value);
        if (!option) return item;

        const label = option.label;
        return {
          ...item,
          value: {
            label,
            value: item.value.value,
            key: item.value.key,
            option: {
              label,
              value: item.value.value
            },
            originLabel: label
          }
        };
      } else if (typeJson.option_type === 2) {
        // 多选：value 是数组，遍历数组处理每个元素
        if (!Array.isArray(item.value)) return item;

        const newValueArray = item.value.map(valItem => {
          const option = selectedLang.options.find(o => o.value === valItem.value);
          if (!option) return valItem;

          const label = option.label;
          return {
            label,
            value: valItem.value,
            key: valItem.key,
            option: {
              label,
              value: valItem.value
            },
            originLabel: label
          };
        });

        return {
          ...item,
          value: newValueArray
        };
      } else {
        // 如果有其他类型，暂时不变
        return item;
      }
    });
  };


  const linkToTargetFn = (id, groupOrItems) => {
    setTimeout(() => {
      document.getElementById(groupOrItems === 1 ? `preview_group_${id}` : id).scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
      searchDropdownVisible.value = false
      clearTimeout(linkToTargetTimer.value)
      linkToTargetTimer.value = null
      nextTick(() => {
        linkToTarget.value = id
        linkToTargetTimer.value = setTimeout(() => {
          linkToTarget.value = null
        }, 3000);
      })
    }, 10);
  }
  const getsearchDropdownFilterData = (arr) => {
    const search = searchDropdownInputValue.value ? searchDropdownInputValue.value.trim().toLowerCase() : null
    if (!search) {
      return arr;
    }
    return arr
      .map(group => {
        const filteredChildren = group.children.filter((child) => {
          const label = child.label.toLowerCase();
          const labelPinyin = pinyin(child.label, { toneType: 'none' }).replace(/\s+/g, '').toLowerCase();
          const labelInitials = pinyin(child.label, { toneType: 'none' }).split(/\s+/).map(word => word.charAt(0)).join('').toLowerCase();
          return (
            label.includes(search) ||
            labelPinyin.includes(search) ||
            labelInitials.includes(search)
          );
        });
        if (filteredChildren.length > 0) {
          return {
            ...group,
            children: filteredChildren,
          };
        }
        return null;
      })
      .filter(group => group !== null);
  };

  const searchAttrInputFocus = () => {
    nextTick(() => {
      document.getElementById(`searchAttrInput_${activeKey.value}`).focus()
    })
  }
  const setData = (data) => {
    setDataLoading.value = true
    console.log('处理后数据', data);
    finalCheckData(data)
    try {
      activeKey.value = props.canUseLanguages[0].value
      templateFormData.value = cloneDeep(data)
      templateFormData.value.forEach(e => {
        e.showChild = true
      })
      console.log('模板数组', templateFormData.value);
      searchDropdownData.value = []
      props.canUseLanguages.forEach(language => {
        var obj = {
          language_id: language.value,
          items: []
        }
        templateFormData.value.forEach(x => {
          if (x.type === 99) {
            var itemObj = {
              title: null,
              id: x.id,
              children: []
            }
            x.language_config.forEach(y => {
              if (y.language_id === language.value) {
                itemObj.title = y.attr_name
              }
            })
            for (const key in x.group) {
              x.group[key].forEach(z => {
                z.language_config.forEach(m => {
                  if (m.language_id === language.value) {
                    itemObj.children.push({ label: m.attr_name, id: z.id })
                  }
                })
              })
            }
          } else {
            var itemObj = {
              title: null,
              id: x.id,
              children: []
            }
            x.language_config.forEach(y => {
              if (y.language_id === language.value) {
                itemObj.title = y.attr_name
              }
            })
            if (x.columns && x.columns.length) {
              x.columns.forEach(y => {
                if (y.language_id === language.value) {
                  y.column.forEach(m => {
                    if (m.key != "operate" && m.key != "index") {
                      itemObj.children.push({ label: m.title, id: m.id })
                    }
                  })
                }
              })
            }
          }
          obj.items.push(itemObj)
        })
        if (obj.items.find(e => e.title)) {
          searchDropdownData.value.push(obj)
        }
      })
      console.log('锚点数组', searchDropdownData.value);
      setDataLoading.value = false
    } catch (error) {
      console.log('配置失败', error);
      setDataLoading.value = false
    }
  }
  const getData = () => {
    return new Promise((resolve, reject) => {
      let timer; // 提前定义，避免访问时未初始化

      const checkReady = () => {
        if (!setDataLoading.value) {
          clearInterval(timer);
          resolve(templateFormData.value);
        }
      };

      // 立即检查一次
      checkReady();

      // 如果未准备好，每100ms检查一次
      timer = setInterval(checkReady, 100);
    });
  };
  const finalCheckData = (data) => {
    data.forEach(x => {
      if (x.type === 100) {
        x.data.forEach(y => {
          for (const key in y) {
            y[key].language_config.forEach(z => {
              if (!z.value && y[key].type === 12 && y[key].type_json.option_type === 2) {
                z.value = []
              }
            })
          }
        })
      }
    })
  }
  watch(() => searchDropdownVisible.value, (newVal, oldVal) => {
    if (newVal) {
      searchAttrInputFocus()
    }
  });
  // 暴露方法
  defineExpose({
    setData, getData, validateTemplateValue
  });
</script>

<style lang="scss" scoped>
  .title {
    padding: 6px 20px;
    font-size: 14px;
    background: #F4F7FE;
    color: #333;
    border-radius: 4px;
    user-select: none;
    margin-bottom: 12px;
  }
  .description {
    color: rgba(0,0,0,0.5);
    font-size: 12px;
    padding-left: 20px;
    white-space: nowrap;
    margin-top: 4px;
  }
  .description2 {
    color: rgba(0,0,0,0.5);
    font-size: 12px;
    margin-top: 8px;
    display: inline-block;
    word-break: break-all
  }
  ::v-deep(.ant-form-item-label) {
    overflow: visible;
    white-space: wrap;
  }
  .formTable {
    .ant-form-item {
      margin-bottom: 0;
      width: 100%;
    }
  }
  .totalBox {
    display: flex;
    padding: 8px;
    min-height: 32px;
    align-items: center;
    justify-content: space-between;
    box-sizing: content-box;
    background-color: #fafafa;
    margin-bottom: 20px;
    .text {
      text-align: center;
      width: 100%;
    }
    .btnBox {
      width: 44px;
      min-width: 44px;
    }
  }
  .textBtn {
    cursor: pointer;
    font-size: 12px;
    color: #409eff;
    transition: color 0.3s;
    &:hover {
      color: #69b9ff;
    }
  }
  ::v-deep(.ant-radio-wrapper) {
    span {
      word-break: break-all;
    }
  }
  ::v-deep(.ant-checkbox-wrapper) {
    span {
      word-break: break-all;
      white-space: initial;
    }
  }
  .w200 {
    width: 200px;
  }
  .detailValueDescription {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.5);
  }
  .detailValueDescription2 {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.5);
    font-weight: normal;
    padding-left: 20px;
  }
  .is_must {
    padding-left: 6px;
    color: #ff4d4f;
    font-size: 12px;
    position: absolute;
    top: 0px;
    font-family: SimSun, sans-serif;
  }
  ::v-deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  .tabContent {
    border: 1px solid rgba(5, 5, 5, 0.06);
    padding: 16px 16px 0 16px;
    margin-bottom: 16px;
    border-top: none;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    position: relative;
  }
  ::v-deep(.ant-tabs-tab) {
    border-bottom: none!important;
  }
  .showChildIcon {
    font-size: 14px;
    margin: 0 0 0 16px;
    color: rgba(0,0,0,0.9);
  }
  .searchDropdown {
    width: 100%;
    max-height: 400px;
    overflow: auto;
    background-color: #fff;
    padding: 0 12px;
    .searchDropdownItem {
      margin-bottom: 12px;
      padding: 0 2px;
      .searchDropdownItemTitle {
        font-size: 12px;
        font-weight: bold;
        margin-bottom: 4px;
        margin-top: 4px;
        color: #333;
        cursor: pointer;
        transition: color 0.3s;
        display: inline-block;
        &:hover {
          color: #409eff;
        }
      }
      .searchDropdownSubitem {
        display: flex;
        gap: 4px 10px;
        flex-wrap: wrap;
        .subitem {
          color: #606266;
          cursor: pointer;
          transition: color 0.3s;
          &:hover {
            color: #409eff;
          }
        }
      }
    }
  }
  .highlightItem {
    animation: backgroundcolorLoop 0.5s infinite alternate;
  }

  @keyframes backgroundcolorLoop {
    0% {
      background-color: rgba(0,0,0,0);
    }
    100% {
      background-color: yellow;
    }
  }
  .addPadding {
    padding: 0 12px;
  }
  ::v-deep(.smallMarginBottom) {
    margin-bottom: 12px;
  }
  .removeOtherContent > :not(:first-child) {
    display: none;
  }
  .aLink {
    text-decoration: underline;
    word-break: break-all;
  }
</style>