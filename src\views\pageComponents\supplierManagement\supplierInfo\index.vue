<template>
  <div class="main">
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.SUPPLIER_INFO" @search="search" @setting="tableRef?.showTableSetting()"></SearchForm>

    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.SUPPLIER_INFO" :get-list="GetUserList" :isCheckbox="true" :is-index="true" :loading="loading">
      <template #srm_is_pushed_string="{ row }">
        <span :class="row.srm_is_pushed_string == '同步成功' ? 'text-success' : 'c-red'">{{ row.srm_is_pushed_string }}</span>
      </template>
      <template #operate="{ row }">
        <a-button type="text" @click="handleExamine(row.id, 'view')" v-if="btnPermission[41001]">详情</a-button>
        <a-button type="text" @click="handleEdit(row.id)" v-if="btnPermission[41002]">编辑</a-button>
        <a-button type="text" @click="handleBan(row.id)" v-if="row.status == 1 && btnPermission[41003]">禁用</a-button>
        <a-button type="text" @click="handleEnable(row.id)" v-if="row.status != 1 && btnPermission[41003]">启用</a-button>
        <a-button type="text" @click="handleSynchronou(row.id)" v-if="row.srm_is_pushed === 0 && btnPermission[41004]">重新同步</a-button>
      </template>

      <template #mainCategorie="{ row }">
        <span v-for="(item, index) in row.main_categories_strings" :key="item">{{ item }}{{ index !== row.main_categories_strings.length - 1 ? '，' : '' }}</span>
      </template>
    </BaseTable>
    <SupplierExamine ref="supplierExamineRef" />
    <SupplierEdit ref="supplierEditRef" />
    <SupplierBan ref="supplierBanRef" @search="search" />
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { PageType } from '@/common/enum'
import SearchForm from '@/components/SearchForm/index.vue'
import { GetSupplierSettlementApprovalList, GetEnumList, GetMainCategoryList, ToggleSupplierStatus, PushSupplierToSRM } from '@/servers/supplierSettlementApproval'
import SupplierExamine from './components/SupplierExamineView.vue'
import SupplierBan from './components/BanModal.vue'
import SupplierEdit from './components/SupplierEdit.vue'

const { btnPermission } = usePermission()

const loading = ref(false)
const tableRef = ref()
const formRef = ref()
const enableLoading = ref(false)
const search = () => tableRef.value.search()
const GetUserList = async (params: any) => {
  if (params.main_categories) {
    params.main_categories = [params.main_categories]
  }
  params.list_type = 1
  if (params.status === null) delete params.status
  const res = await GetSupplierSettlementApprovalList(params)
  return {
    data: {
      list: res.data.list,
      total: res.data.total,
    },
  }
}
const supplierExamineRef = ref<any>(null)
const supplierBanRef = ref<any>(null)
const supplierEditRef = ref<any>(null)
const formArr = ref<any[]>([
  {
    label: '供应商编码',
    value: null,
    type: 'batch-input',
    key: 'number',
  },
  {
    label: '供应商名称',
    value: null,
    type: 'input',
    key: 'supplier_name',
  },
  {
    label: '供应商类别',
    value: null,
    type: 'select',
    key: 'company_type',
    options: [],
  },
  {
    label: '主营类目',
    type: 'select',
    key: 'main_categories',
    showTooltip: true,
    options: [],
    width: 200,
  },
  {
    label: '状态',
    type: 'select',
    key: 'status',
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 },
    ],
  },
  {
    label: '经营规模',
    type: 'select',
    key: 'business_scale',
    options: [],
    mode: 'multiple',
    maxTagCount: 0,
  },
  {
    label: '更新时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['modified_at_start', 'modified_at_end'],
    placeholder: ['更新开始时间', '更新结束时间'],
  },
  {
    label: 'SRM供应商编码',
    type: 'batch-input',
    key: 'srm_supplier_id',
    value: null,
  },
])

const handleExamine = (id: number, type: string) => {
  supplierExamineRef.value.showDrawer(id, type)
}
const handleBan = (id: number) => {
  supplierBanRef.value.showModal(id)
}
const handleEdit = (id: number) => {
  supplierEditRef.value.showDrawer(id)
}

// 获取下拉框
const getSelectList = async () => {
  const res = await GetEnumList()
  formArr.value[2].options = res.data.companytype_list.map((item: any) => ({
    label: item.label,
    value: Number(item.value),
  }))
  formArr.value.find((n) => n.key == 'business_scale')!.options = res.data.businessscale_list.map((item: any) => ({
    label: item.label,
    value: Number(item.value),
  }))
}
// 获取主营类目下拉框
const getMainCategoryList = async () => {
  const params = {
    page: 1,
    pageSize: 9999,
  }
  const res = await GetMainCategoryList(params)
  formArr.value[3].options = res.data.list.map((item: any) => ({
    label: item.content,
    value: item.id,
  }))
}
// 启用供应商
const handleEnable = (id: number) => {
  if (enableLoading.value) return
  enableLoading.value = true
  ToggleSupplierStatus({ id })
    .then(() => {
      message.success('启用成功')
      tableRef.value.maintainSearch()
    })
    .finally(() => {
      enableLoading.value = false
    })
}

// 重新同步
const handleSynchronou = (id: number) => {
  loading.value = true
  PushSupplierToSRM({ id })
    .then(() => {
      message.success('同步成功')
      tableRef.value.maintainSearch()
    })
    .finally(() => {
      loading.value = false
    })
}
onMounted(async () => {
  getSelectList()
  getMainCategoryList()
})
</script>

<style scoped lang="scss">
:deep(.ant-tabs-nav) {
  background-color: rgb(242 242 242);
}

:deep(.ant-tabs-tab) {
  display: flex;
  justify-content: center;
  width: 60px;
}
</style>
