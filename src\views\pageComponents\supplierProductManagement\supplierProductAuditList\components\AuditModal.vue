<template>
  <a-modal v-model:open="visible" :title="modalTitle" @ok="handleSubmit" @cancel="handleCancel" :confirm-loading="loading" width="500px" :okText="btnText">
    <div class="reject-modal-content">
      <div class="mb-16px">
        <label class="block text-sm font-medium text-gray-700 mb-8px">
          审核意见
          <span v-if="auditType != 'pass'" class="text-red-500">*</span>
        </label>

        <!-- 新增提示语区域 -->
        <div v-if="auditType !== 'pass'" class="text-gray-500 text-xs mb-8px">
          {{ auditType === 'overrule' ? '若内容不符合要求，审核人可驳回并反馈修改意见，提单人调整后可重新提交审核。' : '若商品不符合选品标准，审核人可拒绝，该商品将无法再次提交。' }}
        </div>

        <a-textarea v-model:value="formData.remake" placeholder="" :rows="4" :maxlength="200" show-count />
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { AuditPass, OverruleSelection, RefuseSelection } from '@/servers/supplierProductAuditList'

interface FormData {
  remake: string
}

const emit = defineEmits<{
  success: []
}>()

const visible = ref(false)
const loading = ref(false)
const auditType = ref('')
const productId = ref<number>(0)

const formData = ref<FormData>({
  remake: '',
})

const btnText = computed(() => {
  return auditType.value == 'pass' ? '通过' : auditType.value == 'overrule' ? '驳回' : '拒绝'
})

const modalTitle = computed(() => {
  return auditType.value == 'pass' ? '审核通过' : auditType.value == 'overrule' ? '审核驳回' : '拒绝选品'
})

// 显示弹窗
const showModal = (id: number, type: string) => {
  productId.value = id
  auditType.value = type
  formData.value.remake = ''
  visible.value = true
}

// 取消
const handleCancel = () => {
  visible.value = false
  formData.value.remake = ''
}

// 提交
const handleSubmit = async () => {
  if (auditType.value != 'pass' && !formData.value.remake.trim()) {
    message.error('请输入意见')
    return
  }

  loading.value = true

  try {
    const params = {
      id: productId.value,
      is_audit: true,
      remake: formData.value.remake.trim(),
    }

    const apiObj = {
      pass: AuditPass,
      overrule: OverruleSelection,
      refuse: RefuseSelection,
    }

    await apiObj[auditType.value](params)
    message.success('提交成功')

    visible.value = false
    emit('success')
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件调用
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss">
.reject-modal-content {
  padding: 8px 0;
}

.text-red-500 {
  color: #ef4444;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-500 {
  color: #6b7280;
}

.text-xs {
  font-size: 12px;
}

.mb-16px {
  margin-bottom: 16px;
}

.mb-8px {
  margin-bottom: 8px;
}

.block {
  display: block;
}

.text-sm {
  font-size: 14px;
}

.font-medium {
  font-weight: 500;
}
</style>
