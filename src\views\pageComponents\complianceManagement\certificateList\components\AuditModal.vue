<template>
  <a-modal v-model:open="openModal" @ok="showModal" centered @cancel="handleCancel">
    <template #title>
      <span>{{ auditType ? '审核通过' : '审核拒绝' }}</span>
    </template>
    <a-form ref="formRef" :model="formModel" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }" layout="vertical" :rules="rules">
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="审核意见" name="auditOpinion">
            <a-textarea v-model:value="formModel.auditOpinion" :rows="4" :maxlength="200" show-count></a-textarea>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button type="default" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { AuditCertificate } from '@/servers/Certificate'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refreshList', 'handleClose'])
// 是否显示弹窗
const openModal = ref(false)
// 通过或拒绝
const auditType = ref(false)
// 资质/证书id
const certificateId = ref<number>(0)

// 表单
const formRef = ref()
// 表单数据
const formModel = ref({
  auditOpinion: '',
})
// 规则
const rules = ref<{ auditOpinion: any[] }>({ auditOpinion: [] })
// 显示弹窗
const showModal = (id: number, type: string) => {
  openModal.value = true
  certificateId.value = id
  if (type === 'pass') {
    auditType.value = true
    rules.value.auditOpinion = [
      {
        required: false,
        validator: () => Promise.resolve(),
        trigger: 'blur',
      },
    ]
  } else {
    auditType.value = false
    rules.value.auditOpinion = [
      {
        required: true,
        validator: (_rule: any, value: string) => {
          if (!value || value.trim() === '') {
            return Promise.reject('请输入审核意见')
          }
          return Promise.resolve()
        },
        trigger: 'blur',
      },
    ]
  }
}

// 关闭弹窗
const handleCancel = () => {
  formModel.value.auditOpinion = ''
  openModal.value = false
}

// 确定
const handleSubmit = () => {
  formRef.value?.validate().then(async () => {
    const parms = {
      id: certificateId.value,
      approval_status: auditType.value ? 1 : 2,
      approval_content: formModel.value.auditOpinion,
    }
    await AuditCertificate(parms)
      .then(() => {
        message.success('审核成功')
        openModal.value = false
        emit('refreshList')
        emit('handleClose')
      })
      .catch(() => {})
  })
}

defineExpose({
  showModal,
})
</script>

<style scoped lang="scss">
:deep(.ant-form-item-control-input) {
  width: 472px;
}
</style>
