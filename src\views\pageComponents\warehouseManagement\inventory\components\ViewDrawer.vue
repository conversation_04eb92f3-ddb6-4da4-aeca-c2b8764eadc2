<template>
  <a-drawer width="1000" @close="handleClose" v-model:open="openDrawer">
    <template #title>
      <span>查看库存</span>
    </template>
    <div class="drawer-title">基础信息</div>
    <a-form ref="formRef" :model="stockDetails" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }" layout="vertical">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-form-item label="商品主图">
            <div class="w-30 h-30">
              <a-image
                v-if="imgUrl"
                :src="imgUrl"
                :width="30"
                :height="30"
                :preview="{
                  onVisibleChange: (previewVisible) => setPreviewVisible(previewVisible),
                  src: imgUrl,
                }"
              >
                <template #previewMask>
                  <EyeOutlined />
                </template>
              </a-image>
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="供应商商品编码">
            <span>{{ stockDetails.supplier_product_number || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="平台商品编码">
            <span>{{ stockDetails.product_number || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="商品名称">
            <span>{{ stockDetails.product_name || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="商品类目">
            <span>{{ stockDetails.category_string || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="供应商名称">
            <span>{{ stockDetails.supplier_name || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="供应商编号">
            <span>{{ stockDetails.supplier_number || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="更新时间">
            <span>{{ stockDetails.modified_at || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="聚水潭商品编码">
            <span>{{ stockDetails.jst_product_code || '-' }}</span>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="drawer-title">库存明细</div>
    <a-table :columns="columns" :data-source="data" :pagination="false" bordered size="small">
      <template #summary>
        <a-table-summary-row>
          <a-table-summary-cell align="center">合计</a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell></a-table-summary-cell>
          <a-table-summary-cell>
            <a-typography-text>{{ data.reduce((acc, curr) => acc + curr.inventory_num, 0) }}</a-typography-text>
          </a-table-summary-cell>
        </a-table-summary-row>
      </template>
    </a-table>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { EyeOutlined } from '@ant-design/icons-vue'
import { GetDetail, GetProductPreviewUrl } from '@/servers/ProductStock'

const imgUrl = ref('')
// 库存id
const stockId = ref(0)
// 库存详情
const stockDetails = ref<any>({})
// 库存明细
const columns = ref<any[]>([
  {
    title: '序号',
    dataIndex: 'seq',
    customRender: ({ index }: { index: number }) => index + 1,
    key: 'seq',
    align: 'center',
  },
  {
    title: '仓库编码',
    dataIndex: 'warehouse_id',
  },
  {
    title: '仓库名称',
    dataIndex: 'warehouse_name',
  },
  {
    title: '库存',
    dataIndex: 'inventory_num',
  },
])
// 库存明细数据
const data = ref<any[]>([])
// 抽屉是否打开
const openDrawer = ref(false)
// 点击预览时才调接口
const setPreviewVisible = async (visible: boolean) => {
  if (visible) {
    try {
      const imgRes = await GetProductPreviewUrl({
        fileId: stockDetails.value.main_images_id,
        width: 999,
        height: 999,
      })
      imgUrl.value = imgRes.data.view_url
    } catch (e) {
      imgUrl.value = stockDetails.value.images_view_url
    }
  }
}
// 显示抽屉
const showDrawer = async (id: number, row: any) => {
  console.log(row)
  stockId.value = id
  openDrawer.value = true
  stockDetails.value = row
  await getStockDetail()
  imgUrl.value = row.images_view_url
}
// 关闭抽屉
const handleClose = () => {
  openDrawer.value = false
}
// 获取库存明细列表详情
const getStockDetail = async () => {
  const params = {
    product_id: stockId.value,
  }
  const res = await GetDetail(params)
  data.value = res.data
}
defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped></style>
