<template>
  <div class="main">
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.LanguageTB" @search="search" @setting="tableRef?.showTableSetting()"></SearchForm>

    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.LanguageTB" :get-list="GetUserList" :isCheckbox="true">
      <template #right-btn>
        <a-button @click="tapSwitch(false, null)" v-if="btnPermission[54002]">批量停用</a-button>
        <a-button @click="tapSwitch(true, null)" v-if="btnPermission[54002]">批量启用</a-button>
      </template>
      <!-- 图片 -->
      <template #logo_url="{ row }">
        <a-image :width="30" :src="row.logo_url" />
      </template>
      <!-- 状态 -->
      <template #status="{ row }">
        <a-switch class="btn" @click="tapSwitch($event, row)" :disabled="!btnPermission[54002]" v-model:checked="[false, true][row.status]" c>
          <template #checkedChildren><check-outlined /></template>
          <template #unCheckedChildren><close-outlined /></template>
        </a-switch>
      </template>
      <template #operate="{ row }">
        <a-button class="mr-10px" @click="openDawer(row)" v-if="btnPermission[54001]">查看</a-button>
      </template>
    </BaseTable>
    <!-- 停用启用 -->
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <infoModel ref="InfoModelRef"></infoModel>
  </div>
</template>
<script lang="ts" setup>
import { PageType } from '@/common/enum'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { GetLanguageList, GetLanguageDetail, GetDropdownItems, GetLanguageBatchUpdateStatus } from '@/servers/BasicInfoManagement'
import { message } from 'ant-design-vue'
import infoModel from '../components/infoModel.vue'

const InfoModelRef = ref()
const tableRef = ref()
const formRef = ref()
const formArr = ref([
  {
    label: '语言编码',
    value: '',
    type: 'batch-input',
    key: 'codes',
  },
  {
    label: '语言名称',
    value: null,
    type: 'input',
    key: 'language_name',
  },
  // {
  //   label: '文本方向',
  //   type: 'select_one',
  //   value: null,
  //   key: 'text_direction',
  //   options: [
  //     {
  //       label: 565,
  //       value: 1,
  //     },
  //     {
  //       label: 25,
  //       value: 2,
  //     },
  //     {
  //       label: 3,
  //       value: 3,
  //     },
  //   ],
  //   width: 200,
  // },

  {
    label: '状态',
    type: 'select_one',
    value: null,
    key: 'status',
    options: [],
    width: 200,
  },
  {
    label: '创建日期',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['creation_time_start', 'creation_time_end'],
    placeholder: ['创建开始日期', '结束日期'],
  },
  {
    label: '修改日期',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['modification_time_start', 'modification_time_end'],
    placeholder: ['最后修改日期', '结束日期'],
  },
])
const { btnPermission } = usePermission()
const visibleData = ref({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {},
})
const Infotable = ref([
  {
    title: '基本信息',
    tableItem: [
      {
        width: 100,
        labelwidth: 150,
        label: '语言编码',
        key: 'code',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '语言名称',
        key: 'language_name',
        type: 'text',
      },
      // {
      //   width: 100,
      //   labelwidth: 150,
      //   label: '标志',
      //   key: 'logo_url',
      //   type: 'imgbox',
      // },
      // {
      //   width: 100,
      //   labelwidth: 150,
      //   label: '文本方向',
      //   key: 'text_direction_str',
      //   type: 'text',
      // },
      // {
      //   width: 100,
      //   labelwidth: 150,
      //   label: '默认语言',
      //   key: 'is_default_language',
      //   type: 'textoptions',
      //   options: [
      //     {
      //       label: '非默认',
      //       value: false,
      //     },
      //     {
      //       label: '默认',
      //       value: true,
      //     },
      //   ],
      // },
      {
        width: 100,
        labelwidth: 150,
        label: '状态',
        key: 'status',
        type: 'textoptions',
        options: [],
      },
    ],
  },
  {
    title: '其他信息',
    tableItem: [
      {
        width: 50,
        labelwidth: 150,
        label: '创建时间',
        key: 'create_at',
        type: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '创建人',
        key: 'creator',
        key2: 'job_of_creator',
        key3: 'depart_of_creator',
        type: 'namanddepartmenttext',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '最后修改时间',
        key: 'modified_at',
        type: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '最后修改人',
        key: 'modifier',
        key2: 'job_of_modifier',
        key3: 'depart_of_modifier',
        type: 'namanddepartmenttext',
      },
    ],
  },
])
onMounted(() => {
  GetDropdownItemsdata()
})
const GetDropdownItemsdata = async () => {
  const res = await GetDropdownItems()
  if (res.success == true) {
    res.data.status_list.forEach((item) => {
      item.value = parseInt(item.value)
    })
    formArr.value.forEach((item) => {
      if (item.key == 'status') {
        item.options = res.data.status_list
      }
    })
    Infotable.value.forEach((item) => {
      item.tableItem.forEach((titem) => {
        if (titem.key == 'status') {
          titem.options = res.data.status_list
        }
      })
    })
  } else {
    // message.warning(res.message)
  }
}
const openDawer = async (item) => {
  const res = await GetLanguageDetail({ id: item.id })
  if (res.success == true) {
    const data = {
      ...res.data,
      ...res.data.audit_info,
    }
    InfoModelRef.value.init(Infotable.value, data, '查看语言', 'LanguageTB')
  } else {
    // message.warning(res.message)
  }
}
const search = () => tableRef.value.search()
const GetUserList = async (params: any) => {
  params.is_get_total = true
  const res = await GetLanguageList(params)
  // if (res.success != true) {
  //   message.warning(res.message)
  // }
  return {
    data: {
      list: res.data.list,
      total: res.data.total,
    },
  }
}
const tapSwitch = (value, titem) => {
  console.log('tapSwitch', value, titem, tableRef.value.checkItemsArr)
  const ids: any = []
  if (titem == null) {
    tableRef.value.checkItemsArr.forEach((item) => {
      ids.push((item as any).id)
    })
  } else {
    ids.push(titem.id)
  }
  if (ids.length == 0) {
    message.warning('请选择操作数据')
    return
  }
  if (value) {
    // 启用
    visibleData.value.isShow = true
    visibleData.value.title = '启用语言'
    visibleData.value.content = `即将启用该语言，启用后：
    - 新的内容将可以使用该语言。
    - 现有内容中仍会保留该语言的相关数据。
  确定要启用该语言吗？`
    visibleData.value.confirmBtnText = '启用'
    visibleData.value.isCancelBtn = true
    visibleData.value.okType = 'primary'
    visibleData.value.okFn = async () => {
      const params = {
        ids,
        status: '启用',
      }
      const res = await GetLanguageBatchUpdateStatus(params)
      if (res.success == true) {
        search()
        message.success('操作成功')
      } else {
        // message.warning(res.message)
      }
      visibleData.value.isShow = false
    }
  }
  if (!value) {
    // 停用
    visibleData.value.isShow = true
    visibleData.value.title = '停用语言'
    visibleData.value.content = `即将停用该语言，停用后：
    - 新的内容将无法使用该语言。
    - 现有内容中仍会保留该语言的相关数据。
 确定要停用该语言吗？`
    visibleData.value.confirmBtnText = '停用'
    visibleData.value.isCancelBtn = true
    visibleData.value.okType = 'primary'
    visibleData.value.okFn = async () => {
      const params = {
        ids,
        status: '停用',
      }
      const res = await GetLanguageBatchUpdateStatus(params)
      if (res.success == true) {
        search()
        message.success('操作成功')
      } else {
        // message.warning(res.message)
      }
      visibleData.value.isShow = false
    }
  }
}
</script>
<style lang="scss">
.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
