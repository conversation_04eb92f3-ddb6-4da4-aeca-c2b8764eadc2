import { request } from './request'

// === 用户管理接口 - 已全面迁移到新的 /XY/UserManager/ 路径 ===
// 所有新接口请使用 UserManagerNew.ts 文件

// 重新导出新接口以保持兼容性
export {
  GetList as GetUserList,
  UpdateAccountRole as UpdateInner,
  UpdateOuterAccount as UpdateExtra,
  UpdateOuterStatus as UpdateExtraStatus,
  GetOpLogInfos as Log,
  GetAllSubordinates,
  UpdateUserContacts,
  GetDepartmentContactTreeList,
  UpdateDepartmentContacts,
  ContactUserGetList,
  ContactUserGetTree,
  UpdateContactUser,
  DeleteContactUser,
  ContactUserGroupGetList,
  UpdateContactUserGroup,
  DeleteContactUserGroup,
} from './UserManagerNew'

// === 以下接口暂时保持旧路径，因为新API中没有对应接口 ===

// 注意：以下接口已废弃，请使用 UserManagerNew.ts 中的新接口
// SetUserDate -> UpdateAccountRole
// GetInnerList -> GetList
// GetExtraList -> GetList
// 新增用户
export const Add = (data) => {
  return request({ url: '/api/UserManager/Add', data })
}
// 修改密码
export const UpdatePwd = (data) => {
  return request({ url: '/api/UserManager/UpdatePwd', data })
}
// 批量停用/启用内部用户
export const UpdateInnerStatus = (data) => {
  return request({ url: '/api/UserManager/UpdateInnerStatus', data })
}
// 删除外部用户
export const Delete = (data) => {
  return request({ url: '/api/UserManager/Delete', data })
}
// 获取部门树状下拉框
export const GetDepartmentTreeList = (data?: any) => {
  return request({ url: '/api/UserManager/GetDepartmentTreeList', data })
}
// 重置密码
export const ResetPwd = (data) => {
  return request({ url: '/api/UserManager/ResetPwd', data })
}

// 获取供应商列表
export const GetSupplierList = (data) => {
  return request({ url: '/api/UserModule/GetSupplierList', data })
}

// 获取用户供应商列表
export const GetUserSupplierList = (data) => {
  return request({ url: '/api/UserModule/GetUserSupplierList', data })
}
// 获取外部用户对应的部门名称
export const GetUserDeptNameList = (data) => {
  return request({ url: '/api/UserModule/GetUserSupplierDepartmentList', data })
}

// 获取内部用户详情
export const DetailsInner = (data) => {
  return request({ url: '/api/UserManager/DetailsInner', data })
}
// 获取外部用户详情
export const DetailsExtra = (data) => {
  return request({ url: '/api/UserManager/DetailsExtra', data })
}

// 获取外部用户详情
export const DetailsExtraByEdit = (data) => {
  return request({ url: '/api/User/GetUserById', data }, 'GET')
}
// 获取客户下拉框
export const GetCustomerSelectOption = () => {
  return request({ url: '/api/UserModule/GetCustomerSelectOption' })
}
// 获取部门下拉框
export const GetDepartmentSelectOption = (data) => {
  return request({ url: '/api/UserManager/GetDepartmentTreeList', data })
}
// 获取角色下拉框
export const GetRoleSelectOption = (data) => {
  return request({ url: '/api/UserModule/GetRoleSelectOption', data })
}

// 获取内部用户详情
export const DetailsInnerByEdit = (data) => {
  return request({ url: '/api/UserModule/DetailsInnerByEdit', data })
}

// 获取用户信息
export const GetUserInfo = (data) => {
  return request({ url: '/api/User/GetUserById', data }, 'GET')
}

// 获取用户下拉列表
export const GetUserOptions = (data) => {
  return request({ url: '/api/UserManager/getUserScreen', data })
}

// === 注意：新接口已在文件开头导出，避免重复导出 ===
