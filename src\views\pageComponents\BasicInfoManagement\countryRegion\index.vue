<template>
  <div class="main">
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.Country_Region" @search="search" @setting="tableRef?.showTableSetting()"></SearchForm>

    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.Country_Region" :get-list="GetUserList" :isCheckbox="true">
      <template #right-btn>
        <a-button @click="tapSwitch(false, null)" v-if="btnPermission[51002]">批量停用</a-button>
        <a-button @click="tapSwitch(true, null)" v-if="btnPermission[51002]">批量启用</a-button>
      </template>
      <!-- 图片 -->
      <!-- <template #number="{}">
        <a-image :width="30" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" />
      </template> -->
      <!-- 状态 -->
      <template #status="{ row }">
        <a-switch class="btn" @click="tapSwitch($event, row)" :disabled="!btnPermission[51002]" v-model:checked="[false, true][row.status]">
          <template #checkedChildren><check-outlined /></template>
          <template #unCheckedChildren><close-outlined /></template>
        </a-switch>
      </template>
      <!-- 标签 -->
      <template #area_names="{ row }">
        <span class="p-12px bg-#dedede mr-5px rounded-15px" v-for="item in row.area_names" :key="item">{{ item }}</span>
      </template>
      <template #used_language_name="{ row }">
        <span class="p-12px bg-#dedede mr-5px rounded-15px" v-for="item in row.used_language_name" :key="item">{{ item.language_name }}</span>
      </template>
      <template #operate="{ row }">
        <a-button type="text" @click="openDawer(row)" v-if="btnPermission[51001]">查看</a-button>
      </template>
    </BaseTable>
    <!-- 停用启用 -->
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <infoModel ref="InfoModelRef"></infoModel>
  </div>
</template>
<script lang="ts" setup>
import { PageType } from '@/common/enum'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { GetCountryRegionList, GetCountryRegionDetail, GetCountryRegionUpdateStatus, GetDropdownItems } from '@/servers/BasicInfoManagement'
import { message } from 'ant-design-vue'
import infoModel from '../components/infoModel.vue'

const InfoModelRef = ref()
const tableRef = ref()
const formRef = ref()
const formArr = ref([
  {
    label: '国家/地区编码',
    value: '',
    type: 'batch-input',
    key: 'codes',
  },
  {
    label: '国家/地区名称',
    value: null,
    type: 'input',
    key: 'country_region_name',
  },
  {
    label: '区域名称',
    value: null,
    type: 'input',
    key: 'area_name',
  },
  {
    label: '使用语言',
    value: [],
    type: 'select',
    key: 'language_id',
    mode: 'multiple',
    options: [],
  },
  {
    label: '状态',
    type: 'select_one',
    value: null,
    key: 'status',
    options: [],
    width: 200,
  },
  {
    label: '创建日期',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['creation_time_start', 'creation_time_end'],
    placeholder: ['创建开始日期', '结束日期'],
  },
  {
    label: '修改日期',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['modification_time_start', 'modification_time_end'],
    placeholder: ['最后修改日期', '结束日期'],
  },
])
const { btnPermission } = usePermission()
const visibleData = ref({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {},
})
const Infotable = ref([
  {
    title: '基本信息',
    tableItem: [
      {
        width: 100,
        labelwidth: 150,
        label: '国家/地区编码',
        key: 'code',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '国家/地区名称',
        key: 'country_region_name',
        type: 'text',
      },
      // {
      //   width: 100,
      //   labelwidth: 150,
      //   label: '标志',
      //   key: 'supplier_name',
      //   type: 'imgbox',
      // },
      {
        width: 100,
        labelwidth: 150,
        label: '状态',
        key: 'status',
        type: 'textoptions',
        options: [],
      },
    ],
  },
  {
    title: '区域',
    subtitle: '配置国家下的区域信息',
    tableItem: [
      {
        width: 100,
        key: 'areas',
        type: 'table',
        editwidth: 100,
        tableInfo: [
          {
            fieldkey: '',
            title: '序号',
            type: 'ID',
            width: 60,
          },
          {
            fieldkey: 'area_name',
            title: '区域名称',
            type: 'text',
          },
        ],
      },
    ],
  },
  {
    title: '使用语言',
    subtitle: '选择该国家/地区上架产品所需支持语言',
    tableItem: [
      {
        width: 100,
        key: 'used_language_name',
        type: 'table',
        editwidth: 100,
        tableInfo: [
          // {
          //   title: '标志',
          //   fieldkey: 'supplier_name',
          //   type: 'imgbox',
          // },
          // {
          //   fieldkey: 'is_default',
          //   title: '语言分组',
          //   type: 'textoptions',
          //   options: [
          //     {
          //       value: 1,
          //       label: '1111',
          //     },
          //     {
          //       value: 2,
          //       label: '222',
          //     },
          //   ],
          // },
          {
            fieldkey: 'code',
            title: '语言编码',
            type: 'text',
          },
          {
            fieldkey: 'language_name',
            title: '语言名称',
            type: 'text',
          },
          // {
          //   fieldkey: 'is_default',
          //   title: '默认语言',
          //   type: 'textoptions',
          //   options: [
          //     {
          //       value: 1,
          //       label: '是',
          //     },
          //     {
          //       value: 2,
          //       label: '否',
          //     },
          //   ],
          // },
        ],
      },
    ],
  },
  {
    title: '其他信息',
    tableItem: [
      {
        width: 50,
        labelwidth: 150,
        label: '创建时间',
        key: 'create_at',
        type: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '创建人',
        key: 'creator',
        key2: 'job_of_creator',
        key3: 'depart_of_creator',
        type: 'namanddepartmenttext',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '最后修改时间',
        key: 'modified_at',
        type: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '最后修改人',
        key: 'modifier',
        key2: 'job_of_modifier',
        key3: 'depart_of_modifier',
        type: 'namanddepartmenttext',
      },
    ],
  },
])

onMounted(() => {
  GetDropdownItemsdata()
})
const GetDropdownItemsdata = async () => {
  const res = await GetDropdownItems()
  if (res.success == true) {
    res.data.language_list.forEach((item) => {
      item.value = parseInt(item.value)
    })
    res.data.status_list.forEach((item) => {
      item.value = parseInt(item.value)
    })
    formArr.value.forEach((item) => {
      if (item.key == 'language_id') {
        item.options = res.data.language_list
      }
      if (item.key == 'status') {
        item.options = res.data.status_list
      }
    })
    Infotable.value.forEach((item) => {
      item.tableItem.forEach((titem) => {
        if (titem.key == 'status') {
          titem.options = res.data.status_list
        }
      })
    })
    console.log('formArr.value', formArr.value)
  } else {
    // message.warning(res.message)
  }
}
console.log('btnPermission', btnPermission)
const openDawer = async (item) => {
  console.log('item', item)
  const res = await GetCountryRegionDetail({ id: item.id })
  console.log('item', item)
  if (res.success == true) {
    const data = {
      ...res.data,
      ...res.data.audit_info,
    }
    InfoModelRef.value.init(Infotable.value, data, '查看国家/地区', 'Country_Region')
  } else {
    // message.warning(res.message)
  }
}
const search = () => tableRef.value.search()
const GetUserList = async (params: any) => {
  params.is_get_total = true
  const res = await GetCountryRegionList(params)
  return {
    data: {
      list: res.data.list,
      total: res.data.total,
    },
  }
}
const tapSwitch = (value, titem) => {
  console.log('tapSwitch', value, titem, tableRef.value.checkItemsArr)
  const ids: any = []
  if (titem == null) {
    tableRef.value.checkItemsArr.forEach((item) => {
      ids.push((item as any).id)
    })
  } else {
    ids.push(titem.id)
  }
  console.log('ids', ids)
  if (ids.length == 0) {
    message.warning('请选择操作数据')
    return
  }
  if (value) {
    // 启用
    visibleData.value.isShow = true
    visibleData.value.title = '启用国家/地区'
    visibleData.value.content = `即将启用该国家/地区，启用后：
    - 新的内容将可以使用该国家/地区。
    - 现有内容中已保留的该国家/地区相关数据也将继续可用。
  确定要启用该国家/地区吗？`
    visibleData.value.confirmBtnText = '启用'
    visibleData.value.isCancelBtn = true
    visibleData.value.okType = 'primary'
    visibleData.value.okFn = async () => {
      const params = {
        ids,
        status: '启用',
      }
      const res = await GetCountryRegionUpdateStatus(params)
      if (res.success == true) {
        search()
        message.success('操作成功')
      } else {
        // message.warning(res.message)
      }
      visibleData.value.isShow = false
    }
  }
  if (!value) {
    // 停用
    visibleData.value.isShow = true
    visibleData.value.title = '停用国家/地区'
    visibleData.value.content = `即将停用该国家/地区，停用后：
    - 新的内容将无法使用该国家/地区。
    - 现有内容中仍会保留该国家/地区的相关数据。
  确定要停用该国家/地区吗？`
    visibleData.value.confirmBtnText = '停用'
    visibleData.value.isCancelBtn = true
    visibleData.value.okType = 'primary'
    visibleData.value.okFn = async () => {
      const params = {
        ids,
        status: '停用',
      }
      const res = await GetCountryRegionUpdateStatus(params)
      if (res.success == true) {
        search()
        message.success('操作成功')
      } else {
        // message.warning(res.message)
      }
      visibleData.value.isShow = false
    }
  }
}
</script>
<style lang="scss">
.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
