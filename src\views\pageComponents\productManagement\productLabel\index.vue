<template>
  <div class="main">
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.ProductLabel" @search="search" @setting="tableRef?.showTableSetting()"></SearchForm>

    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.ProductLabel" :get-list="GetUserList" :isCheckbox="true">
      <!-- 标签 -->
      <template #area_names="{ row }">
        <span class="p-12px bg-#dedede mr-5px rounded-15px" v-for="item in row.area_names" :key="item">{{ item }}</span>
      </template>
    </BaseTable>
  </div>
</template>
<script lang="ts" setup>
import { PageType } from '@/common/enum'
import { GetProductLabelList } from '@/servers/productLabel'
import SearchForm from '@/components/SearchForm/index.vue'
// import { message } from 'ant-design-vue'

const tableRef = ref()
const formRef = ref()

const formArr = ref<any[]>([
  {
    label: '商品标签名称',
    value: null,
    type: 'input',
    key: 'name',
  },
])

const search = () => tableRef.value.search()
const GetUserList = async (params: any) => {
  params.sort_field = 'name'
  params.sort_asc = true
  params.status = '启用'
  const res = await GetProductLabelList(params)
  return {
    data: {
      list: res.data.list,
      total: res.data.total,
    },
  }
}
</script>
<style lang="scss">
.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
