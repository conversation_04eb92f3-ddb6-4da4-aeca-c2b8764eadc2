<template>
  <div>
    <a-radio-group v-model:value="showMode" class="mb-16" v-if="!disabled">
      <a-radio-button :value="1"><appstore-outlined /></a-radio-button>
      <a-radio-button :value="2"><BarsOutlined /></a-radio-button>
    </a-radio-group>
    <a-upload-dragger
      ref="uploadDraggerRef"
      class="draggerUploader"
      v-model:fileList="fileList"
      name="file"
      :multiple="true"
      :customRequest="null"
      action="#"
      :showUploadList="false"
      :disabled="loading || disabled"
      v-bind="$attrs"
      :before-upload="beforeUploadFn"
    >
      <div v-show="showMode === 1" class="upload-mode-box" id="dragger" ref="cardListRef">
        <FileCard v-for="item in fileList" :key="item.id" :disabled="disabled" :file="item" @delete="handleDelete(item)" @preview="handlePreview(item)" class="file-card-draggable" />
        <div v-show="fileList.length === 0 && !disabled" class="upload-tip">
          <span class="c-primary mr-8">选择</span>
          <span>拖拽或粘贴文件至此即可添加</span>
        </div>
      </div>
      <div v-show="showMode === 2" class="upload-mode-box">
        <FileItem v-for="item in fileList" :key="item.id" :disabled="disabled" :file="item" @delete="handleDelete(item)" @preview="handlePreview(item)" />
        <div v-show="fileList.length === 0 && !disabled" class="upload-tip">
          <span class="c-primary mr-8">选择</span>
          <span>拖拽或粘贴文件至此即可添加</span>
        </div>
      </div>
    </a-upload-dragger>
    <a-image-preview-group
      :preview="{
        visible: pdfPreviewerVisible,
        onVisibleChange: (value) => {
          pdfPreviewerVisible = value
        },
      }"
    >
      <a-image v-for="(item, index) in pdfPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
    </a-image-preview-group>
    <a-image-preview-group
      :preview="{
        current: imgPreviewerCurrent,
        visible: imgPreviewerVisible,
        onVisibleChange: (value) => {
          imgPreviewerVisible = value
        },
      }"
    >
      <a-image v-for="(item, index) in imgPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
    </a-image-preview-group>
  </div>
</template>

<script setup lang="ts">
import { AppstoreOutlined, BarsOutlined } from '@ant-design/icons-vue'
import * as pdfjsLib from 'pdfjs-dist/build/pdf'
import Sortable from 'sortablejs'
import { ref, onMounted, watch, nextTick } from 'vue'
import FileItem from './components/FileItem.vue'
import FileCard from './components/FileCard.vue'

// 配置 PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
const props = defineProps<{
  // eslint-disable-next-line no-unused-vars
  beforeUpload?: (file: any) => Promise<any>
  disabled?: boolean
}>()

const uploadDraggerRef = ref<any>()
const cardListRef = ref<HTMLElement | null>(null)
const sortableMap = new WeakMap<HTMLElement, Sortable>()

const emit = defineEmits(['delete', 'sortable'])

const fileList = defineModel<any[]>('fileList', { required: true })

const loading = ref(false)

const showMode = ref(1)

const pdfPreviewerVisible = ref(false)
const pdfPreviewerArray = ref<any[]>([])
const imgPreviewerVisible = ref(false)
const imgPreviewerArray = ref<any[]>([])
const imgPreviewerCurrent = ref(0)

const getBase64 = (file) => {
  return new Promise((resolve) => {
    const fileReader = new FileReader()
    fileReader.readAsDataURL(file)
    fileReader.onload = () => {
      resolve(fileReader.result)
    }
  })
}

const beforeUploadFn = async (file: any) => {
  const url = await getBase64(file)
  if (file.type === 'application/pdf') {
    const res = await getPdfMinPreviewUrl(url)
    file.pdfUrl = url
    file.url = res
    file.status = 'uploading'
  } else if (file.type === 'text/plain') {
    const res = await getTxtAsImage(file)
    file.url = res
    file.status = 'uploading'
  } else {
    file.url = url
    file.status = 'uploading'
  }

  await props.beforeUpload?.(file)
  setTimeout(() => {
    fileList.value = fileList.value.filter((i) => i.status !== 'uploading')
  }, 10)

  return false
}
const getPdfMinPreviewUrl = async (fileUrl) => {
  return new Promise((resolve) => {
    ;(async () => {
      const loadingTask = pdfjsLib.getDocument(fileUrl)
      const pdf = await loadingTask.promise
      const page = await pdf.getPage(1)
      const viewport = page.getViewport({ scale: 0.2 })
      const canvas = document.createElement('canvas')
      canvas.height = viewport.height
      canvas.width = viewport.width
      const context = canvas.getContext('2d')
      const renderContext = {
        canvasContext: context,
        viewport,
      }
      await page.render(renderContext).promise
      resolve(canvas.toDataURL('image/png'))
    })()
  })
}
const getTxtAsImage = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsText(file)
    reader.onload = () => {
      const textContent = reader.result as string
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      if (!context) {
        reject('无法创建2D上下文')
        return
      }
      const fontSize = 14
      const lineHeight = 24
      const padding = 20
      const lines = textContent.split('\n')
      canvas.width = 800
      canvas.height = 500
      context.fillStyle = '#fff'
      context.fillRect(0, 0, canvas.width, canvas.height)
      context.font = `${fontSize}px Arial`
      context.fillStyle = '#000'
      context.textBaseline = 'top'
      lines.forEach((line, index) => {
        context.fillText(line, padding, padding + index * lineHeight)
      })
      const imageUrl = canvas.toDataURL('image/png')
      resolve(imageUrl)
    }
    reader.onerror = () => {
      reject('文件读取失败')
    }
  })
}

const handleDelete = (item: any) => {
  emit('delete', item)
}

const handlePreview = async (item: any) => {
  if (item.type.indexOf('pdf') != -1) {
    // if (item.windowOpten) {
    //   window.open(item.openUrl)
    //   return
    // }
    console.log('PDF预览', item)
    // openurl：重新上传时的url；blobUrl：上传时的url；url：缩略图url
    window.open(item.blobUrl || item.openUrl || item.url, '_blank')
    // previewPdf(item)
  } else if (item.type.indexOf('image') != -1) {
    previewImg(item)
  } else if (item.type.indexOf('text') != -1) {
    previewText(item)
  }
}

const previewImg = (item) => {
  const arr = fileList.value.filter((e) => e.type.indexOf('image') != -1).map((a) => a.url)
  imgPreviewerVisible.value = true
  imgPreviewerArray.value = [...arr]
  imgPreviewerCurrent.value = arr.findIndex((e) => e === item.url)
}

const previewText = (item) => {
  imgPreviewerVisible.value = true
  imgPreviewerArray.value = [item.url]
  imgPreviewerCurrent.value = 0
}

const previewPdf = async (item) => {
  pdfPreviewerArray.value = []
  pdfPreviewerVisible.value = true
  const loadingTask = pdfjsLib.getDocument(item.pdfUrl || item.url)
  const pdf = await loadingTask.promise
  for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
    const page = await pdf.getPage(pageNum)
    const viewport = page.getViewport({ scale: 1 }) // 可调整 scale
    const canvas = document.createElement('canvas')
    canvas.width = viewport.width
    canvas.height = viewport.height
    const context = canvas.getContext('2d')
    await page.render({ canvasContext: context, viewport }).promise
    pdfPreviewerArray.value.push(canvas.toDataURL('image/png'))
  }
}

onMounted(() => {
  watch(
    () => showMode.value,
    (val) => {
      if (val === 1 && !props.disabled) {
        nextTick(() => {
          initSortable()
        })
      }
    },
    { immediate: true },
  )
})

function initSortable() {
  if (!cardListRef.value) return
  // 防止重复初始化
  if (sortableMap.has(cardListRef.value)) return
  const sortable = Sortable.create(cardListRef.value, {
    animation: 150,
    handle: '.file-card-draggable',
    onEnd(evt) {
      if (evt.oldIndex === evt.newIndex) return
      const movedItem = fileList.value.splice(evt.oldIndex, 1)[0]
      fileList.value.splice(evt.newIndex, 0, movedItem)
      emit('sortable', fileList.value)
    },
  })
  sortableMap.set(cardListRef.value, sortable)
}
watch(fileList, (newVal) => {
  if (newVal.length > 0 && newVal.some((i) => i.type.indexOf('pdf') != -1)) {
    newVal.map(async (i) => {
      const res = await getPdfMinPreviewUrl(i.url)
      i.viewUrl = res
    })
  }
})
</script>

<style scoped lang="scss">
.upload-mode-box {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 100px;
  padding-inline: 16px;
}

.upload-tip {
  height: 100px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

:deep(.ant-upload-wrapper .ant-upload-disabled) {
  cursor: pointer !important;
  color: #666;
}
</style>
