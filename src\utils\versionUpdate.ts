const isNewVersion = () => {
  let baseUrl = `/version.json?t=${new Date().getTime()}`
  fetch(baseUrl).then((res) => {
    if (res.status === 200) {
      res.json().then((data: { version?: string }) => {
        let appVersion = data?.version //当前项目发布的版本
        let localWebappVersion = localStorage.getItem("appVersion") //缓存中的版本

        if (
          appVersion &&
          localWebappVersion &&
          localWebappVersion != appVersion
        ) {
          // 如果没有缓存版本 或者 缓存版本与当前项目发布版本不一致时 强行刷新
          localStorage.setItem("appVersion", appVersion)
          window.location.reload()
          return
        } else if (appVersion) {
          localStorage.setItem("appVersion", appVersion)
        }
      })
    }
  })
}

const checkVersion = () => {
  const timer = 2 * 60 * 1000
  isNewVersion()
  setInterval(() => {
    isNewVersion()
  }, timer)
}

export { checkVersion }
