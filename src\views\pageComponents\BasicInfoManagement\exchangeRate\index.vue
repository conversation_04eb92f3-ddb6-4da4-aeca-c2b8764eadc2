<template>
  <div class="main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageType.ExchangeRate" @search="search" @setting="tableRef?.showTableSetting()"></Form>

    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.ExchangeRate" :get-list="GetUserList" :isCheckbox="true">
      <template #right-btn>
        <!-- <a-button type="primary" :disabled="!btnPermission[21001]" @click="tapManipulate('add')">新建用户</a-button> -->
        <a-button>批量停用</a-button>
        <a-button>批量启用</a-button>
      </template>
      <!-- 状态 -->
      <template #status_string="{ row }">
        <a-switch class="btn" @click="tapSwitch($event, row)" v-model:checked="[false, true][row.status]" checked-children="启用" un-checked-children="停用" />
      </template>
      <!-- 标签 -->
      <!-- <template #mainCategorie="{ row }">
        <span class="p-12px bg-#dedede mr-5px" v-for="item in row.main_categories_strings" :key="item">{{ item }}</span>
      </template> -->
      <template #operate="{ row }">
        <a-button class="mr-10px" @click="openDawer(row)">查看</a-button>
      </template>
    </BaseTable>
    <!-- 停用启用 -->
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <infoModel ref="InfoModelRef"></infoModel>
  </div>
</template>
<script lang="ts" setup>
import { PageType } from '@/common/enum'
import { GetSupplierSettlementApprovalList } from '@/servers/supplierSettlementApproval'
import infoModel from '../components/infoModel.vue'

const InfoModelRef = ref()
const tableRef = ref()
const formRef = ref()
const formArr = ref([
  // {
  //   label: '付款单编号',
  //   value: '',
  //   type: 'inputDlg',
  //   key: 'number',
  // },
  {
    label: '汇率类型',
    value: null,
    type: 'select_one',
    key: 'main_categories',
    selectArr: [
      {
        label: 565,
        value: 1,
      },
      {
        label: 25,
        value: 2,
      },
      {
        label: 3,
        value: 3,
      },
    ],
    width: 200,
  },
  {
    label: '原币',
    type: 'select_one',
    value: null,
    key: 'main_categories',
    selectArr: [
      {
        label: 565,
        value: 1,
      },
      {
        label: 25,
        value: 2,
      },
      {
        label: 3,
        value: 3,
      },
    ],
    width: 200,
  },
  {
    label: '目标币',
    type: 'select_one',
    value: null,
    key: 'main_categories',
    selectArr: [
      {
        label: 565,
        value: 1,
      },
      {
        label: 25,
        value: 2,
      },
      {
        label: 3,
        value: 3,
      },
    ],
    width: 200,
  },
  {
    label: '生效日期',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['min_join_time', 'max_join_time'],
    placeholder: ['生效开始日期', '结束日期'],
  },
  {
    label: '状态',
    type: 'select_one',
    value: null,
    key: 'main_categories',
    selectArr: [
      {
        label: 565,
        value: 1,
      },
      {
        label: 25,
        value: 2,
      },
      {
        label: 3,
        value: 3,
      },
    ],
    width: 200,
  },
  {
    label: '创建日期',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['min_join_time', 'max_join_time'],
    placeholder: ['创建开始日期', '结束日期'],
  },
  {
    label: '修改日期',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['min_join_time', 'max_join_time'],
    placeholder: ['最后修改日期', '结束日期'],
  },
])
const { btnPermission } = usePermission()
const visibleData = ref({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {},
})
const Infotable = ref([
  {
    title: '基本信息',
    tableItem: [
      {
        width: 100,
        labelwidth: 150,
        label: '汇率类型',
        key: 'number',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '原币',
        key: 'number',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '目标币',
        key: 'supplier_name',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '直接汇率',
        key: 'supplier_name',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '间接汇率',
        key: 'supplier_name',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '生效日期',
        key: 'supplier_name',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '失效日期',
        key: 'supplier_name',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '状态',
        key: 'status',
        type: 'textoptions',
        options: [
          {
            label: 565,
            value: 1,
          },
          {
            label: 25,
            value: 2,
          },
          {
            label: 3,
            value: 3,
          },
        ],
      },
    ],
  },

  {
    title: '其他信息',
    tableItem: [
      {
        width: 50,
        labelwidth: 150,
        label: '创建时间',
        key: 'number',
        type: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '创建人',
        key: 'number',
        type: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '最后修改时间',
        key: 'number',
        type: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '最后修改人',
        key: 'number',
        type: 'text',
      },
    ],
  },
])

console.log('btnPermission', btnPermission)
const openDawer = (item) => {
  console.log('item', item)
  item = {
    id: 9,
    number: 'SUP25062500006',
    supplier_name: '测试西月供应商',
    business_scale: 1,
    business_scale_string: '11~30人',
    main_categories: [100131, 100397],
    main_categories_strings: ['节日气氛', '放青松'],
    company_type: 1,
    company_type_string: '成品供应商',
    audit_opinion: '4',
    creator_id: 7343700607921820000,
    created_at: '0001-01-01 00:00:00',
    applicant: '林树立',
    auditor_id: 7343700607921820000,
    auditor_name: '林树立',
    audit_status: 90,
    audit_status_string: '审核通过',
    audit_type: 2,
    audit_time: '2025-07-02 11:16:49',
    audit_type_string: '资料修改审核',
    establishment_date: '2025-06-25 01:14:00',
    status: 1,
    status_string: '启用',
    modifier_id: 7343700607921820000,
    modifier_user: '林树立',
    modified_at: '2025-07-02 11:16:25',
    join_at: '2025-06-30 11:16:39',
    application_at: '2025-07-02 11:16:25',
    application_id: 7343700607921820000,
    srs_supplier_finance_infos: [
      {
        account_name: '5555',
        is_default: 1,
      },
    ],
  }
  InfoModelRef.value.init(Infotable.value, item, '编辑公司信息', 'ExchangeRate')
}
const search = () => tableRef.value.search()
const GetUserList = async (params: any) => {
  if (params.main_categories) {
    params.main_categories = [params.main_categories]
  }
  params.list_type = 1
  const res = await GetSupplierSettlementApprovalList(params)
  return {
    data: {
      list: res.data,
      total: res.data.length,
    },
  }
}
const tapSwitch = (value, titem) => {
  console.log('tapSwitch', value, titem)
  if (value) {
    // 启用
    visibleData.value.isShow = true
    visibleData.value.title = '启用汇率'
    visibleData.value.content = `即将启用该汇率，启用后：
    - 启用后，该汇率方可在系统中生效并用于币种间换算。
    - 历史汇率数据将继续保留，可用于已有业务。
  确定要启用该汇率吗？`
    visibleData.value.confirmBtnText = '启用'
    visibleData.value.isCancelBtn = true
    visibleData.value.okType = 'primary'
    visibleData.value.okFn = () => {
      visibleData.value.isShow = false
      titem.status = 1
    }
  }
  if (!value) {
    // 停用
    visibleData.value.isShow = true
    visibleData.value.title = '停用汇率'
    visibleData.value.content = `即将停用该汇率，停用后：
    - 历史汇率数据和已生效业务仍可继续使用，不受影响。
    - 历史汇率数据和已生效业务仍可继续使用，不受影响。
  确定要停用该汇率吗？`
    visibleData.value.confirmBtnText = '停用'
    visibleData.value.isCancelBtn = true
    visibleData.value.okType = 'primary'
    visibleData.value.okFn = () => {
      visibleData.value.isShow = false
      titem.status = 0
    }
  }
}
</script>
<style lang="scss">
.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
