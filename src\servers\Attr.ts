import { request } from './request'

// 获取属性列表
export const GetAttrList = (data) => request({ url: '/plm/Attr/GetAttrList', data })

// 获取属性详情
export const GetAttrDetail = (data) => request({ url: '/plm/Attr/GetAttrDetail', data })

// 获取属性详情（编辑用）
export const GetAttrDetailByEdit = (data) => request({ url: '/plm/Attr/GetAttrDetailByEdit', data })

// 获取属性分组列表
export const GetAttrGroupList = (data) => request({ url: '/plm/Attr/GetAttrGroupList', data })

// 获取属性分组详情
export const GetAttrGroupDetail = (data) => request({ url: '/plm/Attr/GetAttrGroupDetail', data })

// 获取属性分组详情（编辑用）
export const GetAttrGroupDetailByEdit = (data) => request({ url: '/plm/Attr/GetAttrGroupDetailByEdit', data })

// 获取属性分组下拉框
export const GetAttrGroupSelectOption = (data) => request({ url: '/plm/Attr/GetAttrGroupSelectOption', data })

// 获取文件类型下拉框
export const GetFileTypeSelectOption = () => request({ url: '/plm/Attr/GetFileTypeSelectOption' })

// 获取语言下拉框
export const GetLanguageSelectOption = () => request({ url: '/plm/Attr/GetLanguageSelectOption' })

// 获取计量单位下拉框
export const GetMeteringUnitSelectOption = () => request({ url: '/plm/Attr/GetMeteringUnitSelectOption' })

// 获取复合属性下拉框
export const GetMultiAttrSelectOptionSimple = () => request({ url: '/plm/Attr/GetMultiAttrSelectOptionSimple' })

// 校验属性名称唯一性
export const CheckAttrLanguageNameUnique = (data) => request({ url: '/plm/Attr/CheckAttrLanguageNameUnique', data })

// 校验属性分组名称唯一性
export const CheckAttrGroupLanguageNameUnique = (data) => request({ url: '/plm/Attr/CheckAttrGroupLanguageNameUnique', data })

// 获取加工方式（提供给MRP，SRM读取）
export const GetProcessingMethodData = (data, params) => request({ url: '/plm/Attr/GetProcessingMethodData', data, params })
