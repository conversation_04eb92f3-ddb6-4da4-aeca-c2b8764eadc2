// 系统通知管理接口 - 新的 /XY/Notify/ 路径
import { requestXY } from './request'

// 字段映射函数：将旧字段名转换为新字段名
const mapRequestFields = (data) => {
  if (!data || typeof data !== 'object') return data

  const params = { ...data }

  // 请求参数字段映射：旧字段名 -> 新字段名
  if (params.notice_type !== undefined) {
    params.type = params.notice_type
    delete params.notice_type
  }
  if (params.scheduled_publish_at !== undefined) {
    params.plan_publish_time = params.scheduled_publish_at
    delete params.scheduled_publish_at
  }
  if (params.expired_at !== undefined) {
    params.exp_time = params.expired_at
    delete params.expired_at
  }
  if (params.notice_ids !== undefined) {
    params.ids = params.notice_ids
    delete params.notice_ids
  }

  // 列表查询参数映射
  if (params.publish_start_at !== undefined) {
    params.published_start = params.publish_start_at
    delete params.publish_start_at
  }
  if (params.publish_end_at !== undefined) {
    params.published_end = params.publish_end_at
    delete params.publish_end_at
  }
  if (params.create_start_at !== undefined) {
    params.created_start = params.create_start_at
    delete params.create_start_at
  }
  if (params.create_end_at !== undefined) {
    params.created_end = params.create_end_at
    delete params.create_end_at
  }
  if (params.update_start_at !== undefined) {
    params.modified_start = params.update_start_at
    delete params.update_start_at
  }
  if (params.update_end_at !== undefined) {
    params.modified_end = params.update_end_at
    delete params.update_end_at
  }

  // 状态字段映射处理
  if (params.notice_status_list !== undefined && params.notice_status_list !== null && params.notice_status_list !== '') {
    if (Array.isArray(params.notice_status_list)) {
      // 如果是数组（多选），取第一个值
      params.status = params.notice_status_list.length > 0 ? params.notice_status_list[0] : undefined
    } else {
      // 如果是单个值，直接使用
      params.status = params.notice_status_list
    }
    delete params.notice_status_list
  }

  return params
}

// 响应数据字段映射：将新字段名转换为旧字段名以保持兼容性
const mapResponseFields = (response) => {
  if (!response || !response.data) return response

  const mapSingleItem = (item) => {
    if (!item || typeof item !== 'object') return item

    const mappedItem = { ...item }

    // 响应字段映射：新字段名 -> 旧字段名
    if (mappedItem.type !== undefined) {
      mappedItem.notice_type = mappedItem.type
    }
    if (mappedItem.plan_publish_time !== undefined) {
      mappedItem.scheduled_publish_at = mappedItem.plan_publish_time
    }
    if (mappedItem.exp_time !== undefined) {
      mappedItem.expired_at = mappedItem.exp_time
    }
    if (mappedItem.publish_time !== undefined) {
      mappedItem.publish_at = mappedItem.publish_time
    }
    if (mappedItem.created !== undefined) {
      mappedItem.create_at = mappedItem.created
    }
    if (mappedItem.modified !== undefined) {
      mappedItem.update_at = mappedItem.modified
    }
    if (mappedItem.status !== undefined) {
      mappedItem.notice_status = mappedItem.status
    }
    if (mappedItem.no !== undefined) {
      mappedItem.code = mappedItem.no
    }

    return mappedItem
  }

  // 处理列表数据
  if (Array.isArray(response.data)) {
    response.data = response.data.map(mapSingleItem)
  } else if (response.data.list && Array.isArray(response.data.list)) {
    response.data.list = response.data.list.map(mapSingleItem)
  } else {
    // 处理单个对象
    response.data = mapSingleItem(response.data)
  }

  return response
}

// 获取通知相关下拉框
export const GetDropsSource = () => {
  return requestXY({ url: '/Notify/GetDropsSource' }, 'GET')
}

// 添加系统通知草稿
export const Add = (data) => {
  const params = mapRequestFields(data)
  return requestXY({ url: '/Notify/Add', data: params }, 'POST').then(mapResponseFields)
}

// 删除系统通知
export const Delete = (data) => {
  const params = mapRequestFields(data)
  return requestXY({ url: '/Notify/Delete', data: params }, 'POST')
}

// 编辑系统通知草稿
export const Update = (data) => {
  const params = mapRequestFields(data)
  return requestXY({ url: '/Notify/Update', data: params }, 'POST').then(mapResponseFields)
}

// 获取系统通知列表
export const GetList = (data) => {
  const params = mapRequestFields(data)
  return requestXY({ url: '/Notify/GetList', data: params }, 'POST').then(mapResponseFields)
}

// 保存并发布系统通知
export const SaveAndPublish = (data) => {
  const params = mapRequestFields(data)
  // 如果没有id，设置为0
  if (!params.id) {
    params.id = 0
  }
  return requestXY({ url: '/Notify/SaveAndPublish', data: params }, 'POST').then(mapResponseFields)
}

// 发布系统通知
export const Publish = (data) => {
  const params = mapRequestFields(data)
  return requestXY({ url: '/Notify/Publish', data: params }, 'POST')
}

// 批量发布系统通知
export const BatchPublishs = (data) => {
  // 兼容旧的参数格式：将 notice_ids 转换为 ids
  const params = mapRequestFields(data)
  return requestXY({ url: '/Notify/BatchPublishs', data: params }, 'POST')
}

// 撤回通知
export const Revocation = (data) => {
  const params = mapRequestFields(data)
  return requestXY({ url: '/Notify/Revocation', data: params }, 'POST')
}

// 获取通知详情
export const GetNotifyDetails = (params) => {
  // 修复参数传递问题：支持对象和简单值两种格式
  const id = typeof params === 'object' && params !== null ? params.id : params
  return requestXY({ url: `/Notify/GetNotifyDetails?id=${id}` }, 'post').then(mapResponseFields)
}

// 获取最新通知内容
export const GetNewNotify = () => {
  return requestXY({ url: '/Notify/GetNewNotify' }, 'GET').then((response) => {
    // 修复响应格式：新接口返回字符串，前端期望对象格式
    if (response && typeof response.data === 'string') {
      response.data = {
        content: response.data,
        id: null,
      }
    }
    return response
  })
}

// === 以下是为了兼容旧接口而添加的别名接口 ===

// 批量发布系统通知 - 兼容旧接口名
export const BatchPublishNotify = BatchPublishs

// 获取通知列表 - 兼容旧接口名
export const GetnoticeList = GetList

// 新增保存系统通知 - 兼容旧接口名
export const AddNotification = Add

// 编辑保存系统通知 - 兼容旧接口名
export const UpdateNotification = Update

// 撤回系统通知 - 兼容旧接口名
export const revocation = Revocation

// 删除系统通知 - 兼容旧接口名
export const deleteNotification = Delete

// 获取系统通知详情 - 兼容旧接口名
export const DetailByEdit = GetNotifyDetails
