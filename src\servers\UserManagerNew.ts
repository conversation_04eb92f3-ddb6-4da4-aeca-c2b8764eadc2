// 用户管理接口 - 新的 /XY/UserManager/ 路径
import { requestXY } from './request'

// 获取用户列表
export const GetList = (data) => {
  return requestXY({ url: '/UserManager/GetList', data }, 'POST')
}

// 更新账号角色（内部账号）
export const UpdateAccountRole = (data) => {
  return requestXY({ url: '/UserManager/UpdateAccountRole', data }, 'POST')
}

// 更新外部账号
export const UpdateOuterAccount = (data) => {
  return requestXY({ url: '/UserManager/UpdateOuterAccount', data }, 'POST')
}

// 批量停用/启用外部用户
export const UpdateOuterStatus = (data) => {
  return requestXY({ url: '/UserManager/UpdateOuterStatus', data }, 'POST')
}

// 获取日志 - 使用通用日志接口
export const GetOpLogInfos = (data) => {
  return requestXY({ url: '/Common/GetOpLogInfos', data: { ...data, page: 3 } }, 'POST')
}

// 获取我的下属列表
export const GetAllSubordinates = (data) => {
  return requestXY({ url: '/UserManager/GetAllSubordinates', data }, 'POST')
}

// 批量设置用户的关联关系单位
export const UpdateUserContacts = (data) => {
  return requestXY({ url: '/UserManager/UpdateUserContacts', data }, 'POST')
}

// 获取部门树状下拉框, 带有是否设置关联关系标识
export const GetDepartmentContactTreeList = (data) => {
  return requestXY({ url: '/UserManager/GetDepartmentContactTreeList', data }, 'POST')
}

// 批量设置组织架构关联管理单位
export const UpdateDepartmentContacts = (data) => {
  return requestXY({ url: '/UserManager/UpdateDepartmentContacts', data }, 'POST')
}

// 获取[关联常用用户]列表
export const ContactUserGetList = (data) => {
  return requestXY({ url: '/UserManager/ContactUserGetList', data }, 'POST')
}

// 获取[关联常用用户分组及用户]树形列表
export const ContactUserGetTree = (data) => {
  return requestXY({ url: '/UserManager/ContactUserGetTree', data }, 'POST')
}

// 更新[关联常用用户]
export const UpdateContactUser = (data) => {
  return requestXY({ url: '/UserManager/UpdateContactUser', data }, 'POST')
}

// 删除[关联常用用户]
export const DeleteContactUser = (data) => {
  return requestXY({ url: '/UserManager/DeleteContactUser', data }, 'POST')
}

// 获取[关联常用用户分组]列表
export const ContactUserGroupGetList = (data) => {
  return requestXY({ url: '/UserManager/ContactUserGroupGetList', data }, 'POST')
}

// 更新[关联常用用户分组]
export const UpdateContactUserGroup = (data) => {
  return requestXY({ url: '/UserManager/UpdateContactUserGroup', data }, 'POST')
}

// 删除[关联常用用户分组]
export const DeleteContactUserGroup = (data) => {
  return requestXY({ url: '/UserManager/DeleteContactUserGroup', data }, 'POST')
}

// 新增外部用户账号
export const AddUserAccount = (data) => {
  return requestXY({ url: '/UserManager/AddUserAccount', data }, 'POST')
}

// 获取当前用户信息
export const GetUserInfo = (data) => {
  return requestXY({ url: '/User/GetUserInfo', data }, 'GET')
}

// 获取账号下拉选项
export const GetAccountSelectOption = (data) => {
  return requestXY({ url: '/UserManager/GetAccountSelectOption', data }, 'POST')
}
// 获取UMC账号日志
export const GetUmcOpLogInfos = (data: { page: number; pageSize: number; sortField?: string; sortType?: string; id?: string; user_id?: any; account_id?: any }) => {
  return requestXY({ url: '/UserManager/GetUmcOpLogInfos', data }, 'POST')
}

// 同步公司信息
export const SyncCompanyInfo = () => {
  return requestXY({ url: '/User/SyncCompanyInfo' }, 'GET')
}

// === 兼容性别名接口 ===

// 兼容旧接口名
export const GetUserList = GetList
export const Log = GetOpLogInfos
export const UpdateExtra = UpdateOuterAccount // 更新为新的外部账号接口
export const UpdateExtraStatus = UpdateOuterStatus
export const UpdateInner = UpdateAccountRole // 内部用户更新使用账号角色接口
