// 公共接口
import { request, requestXY } from './request'
// 获取列表动态字段（列名、字段、排序、固定类型）
export const GetTableConfig = (data) => {
  return requestXY({ url: '/Common/GetViewFiledMapsV2', data }, 'POST')
}
export const SetTableConfig = (data) => {
  return requestXY({ url: '/Common/SaveViewFiledMapsV2', data }, 'POST')
}
// 获取所有枚举
export const GetEnum = () => {
  return requestXY({ url: '/Common/GetEnum' }, 'POST')
}

// 获取PLM所有枚举
export const GetPlmEnum = () => {
  return request({ url: '/plm/Common/GetEnum' }, 'POST')
}
// 获取快捷查询信息
export const GetQuickQuery = (data) => {
  return requestXY({ url: '/Common/GetViewQuickQuery', data }, 'POST')
}

// 保存快捷查询信息
export const SaveQuickQuery = (data) => {
  return requestXY({ url: '/Common/SaveViewQuickQuery', data }, 'POST')
}
// 上传文件
export const FileUpload = (data) => {
  return request({ url: '/api/File/Upload', data, isFormData: true })
}
// 获取文件预览
export const FilePreview = (data) => {
  return request({ url: '/api/File/Preview', data }, 'GET')
}

export const GetFileUrl = (data) => {
  return request({ url: '/api/File/GetFileUrl', data }, 'GET')
}

// 获取系统信息 - 注意：原接口已失效，暂时返回空数据避免404错误
export const Info = () => {
  // console.warn('Info接口已失效，请联系后端提供新的系统信息接口')
  return request({ url: '/api/Info/Sys' }, 'GET')
  // return Promise.resolve({ data: { version: '1.6.0' } })
}


// 关闭系统通知 (从 Notice.ts 迁移)
export const ReadNotice = (data) => {
  return requestXY({ url: '/Common/ReadNotice', data }, 'POST')
}

// 获取公用下拉选择数据
export const GetCommon = () => {
  return request({ url: '/api/Common/GetDropdownItems' }, 'GET')
}
export const GetCommonOption = (data) => {
  return request({ url: '/api/Common/GetDropdownData', data }, 'POST')
}
// 获取文件流
export const ViewByFileId = (data) => {
  return request({ url: '/api/Files/ViewByFileId', data, responseType: 'blob' }, 'GET')
}

// 主营区域/api/Common/GetDeliveryRegionList
export const GetDeliveryRegionList = (data) => {
  return request({ url: '/api/Common/GetDeliveryRegionList', data }, 'POST')
}
// 主营类目/api/Common/GetProductCategoryList
export const GetProductCategoryList = (data) => {
  return request({ url: '/api/Common/GetProductCategoryList', data }, 'POST')
}

// 获取文件流
export const ViewByFileIdCommon = (fileId) => {
  return request({ url: `/api/Files/ViewByFileId?fileId=${fileId}`, responseType: 'blob' }, 'GET')
}

// 上传文件
export const UploadCommonFile = (fileModule: string, data: any) => {
  return request({ url: `/api/Files/UploadFile?fileModule=${fileModule}`, data, isFormData: true })
}

// 上传视频
export const UploadVideoFile = (fileModule: string, data: any) => {
  return request({ url: `api/Files/UploadVideoFile?fileModule=${fileModule}`, data, isFormData: true })
}

// 预览图片
export const GetProductPreviewUrl = (data) => request({ url: '/api/Files/PreViewUrlByFileId', data }, 'GET')

// 获取下载通知 - 兼容旧接口
export const GetDownloadNotice = () => {
  // 这个接口可能需要根据实际后端实现来调整
  // 暂时返回一个模拟的响应，如果后端有对应接口可以替换
  return Promise.resolve({
    success: true,
    data: {
      undownload_count: 0,
      notice_list: [],
    },
  })
}

export const GetRedPoint = () => {
  return request({ url: '/api/Common/GetLabelStatusCountNotAuth' }, 'GET')
}
