import { request } from './request'
// 获取类目列表
export const GetCategoryList = (data: any) => request({ url: '/plm/Category/GetCategoryList', data })

// 获取类目详情
export const GetCategoryDetail = (data: any) => request({ url: '/plm/Category/GetCategoryDetail', data })

// 获取类目详情（编辑用）
export const GetCategoryDetailByEdit = (data: any) => request({ url: '/plm/Category/GetCategoryDetailByEdit', data })

// 获取类目下拉框
export const GetCategorySelectOption = (data: any) => request({ url: '/plm/Category/GetCategorySelectOption', data })

// 获取类目模板下拉框
export const GetCategoryTemplateSelectOption = () => request({ url: '/plm/Category/GetCategoryTemplateSelectOption' })

// 获取语言下拉框
export const GetLanguageSelectOption = () => request({ url: '/plm/Category/GetLanguageSelectOption' })
