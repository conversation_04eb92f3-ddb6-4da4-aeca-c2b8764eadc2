<template>
  <div class="main">
    <a-steps :current="step" :items="stepList" labelPlacement="vertical" status="process"></a-steps>
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :colon="false">
      <section v-show="step == 0">
        <div class="title">公司基本信息</div>
        <a-form-item label="营业执照" name="fileList">
          <a-upload
            v-model:file-list="fileList"
            list-type="picture-card"
            :beforeUpload="(file, uploadFileList) => beforeUpload(file, uploadFileList, fileList)"
            @preview="handlePreview"
            :max-count="1"
          >
            <div v-if="fileList.length == 0">
              <PlusOutlined />
            </div>
          </a-upload>
        </a-form-item>
        <a-form-item label="统一社会信用代码" name="credit_code">
          <a-input v-model:value="form.credit_code" class="input-width" placeholder="请输入统一社会信用代码"></a-input>
          （可在企信网上查询得到，去查询）
        </a-form-item>
        <a-form-item label="公司名称" name="supplier_name">
          <a-input v-model:value="form.supplier_name" class="input-width" placeholder="请输入公司名称" :disabled="userData.company && !isOriFormSupplierName"></a-input>
          <a-button type="link" @click="onClickCreateCompany" style="padding: 8px">新增入驻企业</a-button>
          <a-tooltip placement="rightTop">
            <template #title>
              <span>增加一个新增账号数据，审核通过后，该账号与入驻企业直接绑定。</span>
            </template>
            <QuestionCircleFilled style="font-size: 18px; color: gray" />
          </a-tooltip>
        </a-form-item>
        <a-form-item label="公司类型" name="company_type">
          <a-select
            class="input-width"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="form.company_type"
            placeholder="请选择公司类型"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="(item, i) in companyTypeOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="公司规模" name="business_scale">
          <a-select
            class="input-width"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="form.business_scale"
            placeholder="请选择公司规模"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="(item, i) in companyModeOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="成立日期" name="establishment_date">
          <a-date-picker v-model:value="form.establishment_date" valueFormat="YYYY-MM-DD" class="input-width" />
        </a-form-item>
        <a-form-item label="营业执照有效期" name="business_license_validity">
          <a-space>
            <a-date-picker v-model:value="form.business_license_validity" valueFormat="YYYY-MM-DD" class="input-width" />
            <a-checkbox v-model:checked="form.is_long">长期</a-checkbox>
          </a-space>
        </a-form-item>
        <a-form-item label="营业执照地址" name="business_license_address">
          <a-space>
            <a-cascader class="short" v-model:value="form.bussinessAddress" :options="areaOptions" placeholder="请选择省市区" @change="changeAddress" />
            <a-input v-model:value="form.business_license_address" class="input-width" placeholder="详细地址" :maxlength="200"></a-input>
          </a-space>
        </a-form-item>
        <a-form-item label="办公地址" name="office_address_detail">
          <a-space>
            <a-cascader class="short" v-model:value="form.officeAddress" :options="areaOptions" placeholder="请选择省市区" @change="changeAddress" />
            <a-input v-model:value="form.office_address_detail" class="input-width" placeholder="详细地址" :maxlength="200"></a-input>
          </a-space>
        </a-form-item>
        <div class="title">法定代表人信息</div>
        <a-form-item label="法人姓名" name="legal_person_name">
          <a-input v-model:value="form.legal_person_name" class="input-width" placeholder="请输入法人姓名"></a-input>
        </a-form-item>
        <a-form-item label="证件类型" name="certificate_type">
          <a-select
            class="input-width"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="form.certificate_type"
            placeholder="请选择证件类型"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="(item, i) in documentTypeOptions" :key="i" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="证件号" name="certificate_number">
          <a-input v-model:value="form.certificate_number" class="input-width" placeholder="请输入证件号" :controls="false"></a-input>
        </a-form-item>
        <a-form-item label="证件照" name="photoList">
          <a-space>
            <a-upload
              v-model:file-list="photoList"
              list-type="picture-card"
              :beforeUpload="(file, uploadFileList) => beforeUpload(file, uploadFileList, photoList)"
              @preview="handlePreview"
              :max-count="1"
            >
              <div v-if="photoList.length == 0">
                <PlusOutlined />
                <div>正面</div>
              </div>
            </a-upload>
            <a-upload
              v-model:file-list="backPhotoList"
              list-type="picture-card"
              :beforeUpload="(file, uploadFileList) => beforeUpload(file, uploadFileList, backPhotoList)"
              @preview="handlePreview"
              :max-count="1"
            >
              <div v-if="backPhotoList.length == 0">
                <PlusOutlined />
                <div>反面</div>
              </div>
            </a-upload>
          </a-space>
        </a-form-item>
      </section>
      <section v-show="step == 1">
        <div class="title">公司扩展信息</div>
        <a-form-item label="自营/合作工厂规模" name="factory_scale">
          <a-radio-group v-model:value="form.factory_scale" name="radioGroup">
            <a-radio v-for="(item, i) in factoryscaleOptions" :key="i" :value="item.value">{{ item.label }}</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="主营类目（多选）" name="main_categories">
          <a-checkbox-group v-model:value="form.main_categories" name="majorTypegroup" :options="majorTypeOptions" />
        </a-form-item>
        <a-form-item label="主营区域（多选）" name="main_regions">
          <a-checkbox-group v-model:value="form.main_regions" name="majorAreagroup" :options="majorAreaOptions" />
        </a-form-item>
        <a-form-item label="工厂人员规模">
          <a-input-number v-model:value="form.factory_employee_count" addon-after="人" :precision="0" :max="99999"></a-input-number>
        </a-form-item>
        <a-form-item label="SKU数量">
          <a-input-number v-model:value="form.sku_count" addon-after="个" :precision="0" :max="999999"></a-input-number>
        </a-form-item>
        <a-form-item label="主营商品" name="main_products">
          <a-input v-model:value="form.main_products" class="input-width" placeholder="请输入贵司主营商品，存在多个用逗号隔开"></a-input>
        </a-form-item>
        <a-form-item label="主营商品价格区间" name="main_products_min_price">
          <a-input-number class="price-before" v-model:value="form.main_products_min_price" addon-after="-" :precision="2"></a-input-number>
          <a-input-number class="price-affter" v-model:value="form.main_products_max_price" addon-after="元" :precision="2"></a-input-number>
        </a-form-item>
        <a-form-item label="公司年销售额">
          <a-input-number class="input-width" v-model:value="form.annual_sales" addon-after="万元" :precision="0"></a-input-number>
        </a-form-item>
      </section>
      <section v-show="step == 2">
        <div class="title">
          联系人信息
          <span class="grey">（最多支持添加5个）</span>
        </div>
        <div class="vxe-table-box">
          <vxe-table class="rule-table" ref="contactsTableRef" keep-source border show-overflow height="288" :data="form.srs_supplier_contact_infos">
            <vxe-column field="name" title="联系人姓名">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item :name="['srs_supplier_contact_infos', 'name']" :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 20 }), trigger: ['change', 'blur'] }]">
                  <a-input v-model:value="row.name"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="job" title="职务">
              <template #default="{ row, column }">
                <a-form-item :name="['srs_supplier_contact_infos', 'job']" :rules="[{ validator: () => validators({ row, column, length: 20 }), trigger: ['change', 'blur'] }]">
                  <a-input v-model:value="row.job"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="mobile_phone_number" title="手机号">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_contact_infos', 'mobile_phone_number']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 13 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input-number class="w100" v-model:value="row.mobile_phone_number"></a-input-number>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="email" title="邮箱">
              <template #default="{ row, column }">
                <a-form-item :name="['srs_supplier_contact_infos', 'email']" :rules="[{ validator: () => validators({ row, column, length: 20 }), trigger: ['change', 'blur'] }]">
                  <a-input v-model:value="row.email"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="weixin_number" title="微信">
              <template #default="{ row, column }">
                <a-form-item :name="['srs_supplier_contact_infos', 'weixin_number']" :rules="[{ validator: () => validators({ row, column, length: 20 }), trigger: ['change', 'blur'] }]">
                  <a-input v-model:value="row.weixin_number"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="is_default" title="默认联系人">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column, rowIndex }">
                <a-form-item
                  :name="['srs_supplier_contact_infos', 'is_default']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, type: 'select' }), trigger: ['change', 'blur'] }]"
                >
                  <a-select
                    class="w100"
                    v-model:value="row.is_default"
                    :options="trueOrFalseOptions"
                    :filter-option="filterOption"
                    @change="changeContactDefault($event, rowIndex, 'srs_supplier_contact_infos')"
                  />
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="operate" title="操作">
              <template #default="{ rowIndex }">
                <a-space>
                  <MinusOutlined style="color: red" @click="onDelContact(rowIndex, 'srs_supplier_contact_infos')" />
                  <PlusOutlined v-if="rowIndex >= 1 || rowIndex == form.srs_supplier_contact_infos.length - 1" @click="onAddTableItem('srs_supplier_contact_infos')" />
                </a-space>
              </template>
            </vxe-column>
          </vxe-table>
        </div>

        <div class="title">
          财务信息
          <span class="grey">（最多支持添加5个）</span>
        </div>
        <a-row>
          <a-col :span="8">
            <a-form-item label="结算方式" name="settlement_method">
              <a-select v-model:value="form.settlement_method" :options="settlementMethodOptions" :filter-option="filterOption"></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="发票类型" name="invoice_type">
              <a-select v-model:value="form.invoice_type" :options="invoiceTypeOptions" :filter-option="filterOption"></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="默认税率" name="default_tax_rate">
              <a-select v-model:value="form.default_tax_rate" :options="defaultTaxRateOptions" :filter-option="filterOption"></a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <div class="vxe-table-box">
          <vxe-table class="rule-table" ref="financeTableRef" keep-source border show-overflow height="288" :data="form.srs_supplier_finance_infos">
            <vxe-column field="account_name" title="账户名称">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'account_name']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 20 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input v-model:value="row.account_name"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="account_type" title="账户类型" :edit-render="{ name: 'VxeSelect' }">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'account_type']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, type: 'select' }), trigger: ['change', 'blur'] }]"
                >
                  <a-select class="w100" v-model:value="row.account_type" :options="accountTypeOptions" :filter-option="filterOption" />
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="collection_card_number" title="银行卡卡号">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'collection_card_number']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input v-model:value="row.collection_card_number"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="collection_bank" title="开户行">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'collection_bank']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input v-model:value="row.collection_bank"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="collection_bank_branch" title="所属支行">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'collection_bank_branch']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, length: 30 }), trigger: ['change', 'blur'] }]"
                >
                  <a-input v-model:value="row.collection_bank_branch"></a-input>
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="is_default" title="默认联系人" :edit-render="{ name: 'VxeSelect' }">
              <template #header="{ column }">
                <span class="red">*</span>
                {{ column.title }}
              </template>
              <template #default="{ row, column, rowIndex }">
                <a-form-item
                  :name="['srs_supplier_finance_infos', 'is_default']"
                  :rules="[{ validator: () => validators({ row, column, isRequired: true, type: 'select' }), trigger: ['change', 'blur'] }]"
                >
                  <a-select
                    class="w100"
                    v-model:value="row.is_default"
                    :options="trueOrFalseOptions"
                    :filter-option="filterOption"
                    @change="changeContactDefault($event, rowIndex, 'srs_supplier_finance_infos')"
                  />
                </a-form-item>
              </template>
            </vxe-column>
            <vxe-column field="operate" title="操作">
              <template #default="{ rowIndex }">
                <a-space>
                  <MinusOutlined style="color: red" @click="onDelContact(rowIndex, 'srs_supplier_finance_infos')" />
                  <PlusOutlined v-if="rowIndex >= 1 || rowIndex == form.srs_supplier_finance_infos.length - 1" @click="onAddTableItem('srs_supplier_finance_infos')" />
                </a-space>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </section>
      <section v-show="step == 3">
        <div class="title">上传文件</div>
        <a-form-item name="uploadFile" label="上传文件">
          <a-upload v-model:file-list="uploadFileList" :beforeUpload="(file, uploadFileList) => beforeUpload(file, uploadFileList, certificateFileList)" :max-count="50" :showUploadList="false">
            <a-button>上传文件</a-button>
          </a-upload>
          <a-table :columns="columns" :data-source="certificateFileList" class="w-90% mt20 mb20" bordered size="small" :pagination="false">
            <template #bodyCell="{ column, rowIndex, record }">
              <template v-if="column.dataIndex === 'operation'">
                <a-space>
                  <a @click="ToPagePreviewbyId(record.id)">预览</a>
                  <a @click="onDelFile(rowIndex)">删除</a>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-form-item>
      </section>
      <section v-show="step == 4" class="pd20">
        <div v-if="1">
          <p class="strong font-xl text-center color333">入驻申请已完成，请等待审核</p>
          <CheckCircleFilled class="success-icon center mb30" />
          <p class="strong text-center font-l">提交成功！</p>
          <p class="text-center">感谢您提交入驻申请，我们将尽快完成审核流程。</p>
        </div>
        <div v-else>
          <CloseCircleFilled class="fail-icon center mb30 red" />
          <p class="strong text-center font-l">审核不通过</p>
          <p class="red">失败原因：供应商数据不齐全，请不全后重新提交</p>
        </div>
        <div class="apply-info center">
          <div class="w500">
            <p class="font-l mt20">申请信息摘要</p>
            <a-row>
              <a-col :span="12">公司名称 广东省汕头市xxx公司</a-col>
              <a-col :span="12">公司类型 大型公司</a-col>
            </a-row>
            <a-row>
              <a-col :span="12">联系人 陈小姐</a-col>
              <a-col :span="12">联系方式 18811111111</a-col>
            </a-row>
            <a-row>
              <a-col :span="12">申请时间 2018-12-15</a-col>
            </a-row>
          </div>
        </div>
      </section>

      <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
        <a-space>
          <a-button v-if="step > 0 && step < 4" type="primary" @click="onClickBack">上一步</a-button>
          <a-button v-if="step < 3" type="primary" @click="onSubmit" :loading="loading">下一步</a-button>
          <a-button v-if="step == 3" type="primary" @click="onSubmit" :loading="loading">提交</a-button>
        </a-space>
      </a-form-item>
    </a-form>
    <a-modal :open="previewVisible" :title="previewTitle" :footer="null" @cancel="handleCancel">
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { PlusOutlined, QuestionCircleFilled, MinusOutlined, CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { message, type UploadProps } from 'ant-design-vue'
import { areaCity } from '@/utils/address'
import { validateStr, filterOption, getNowDateTime } from '@/utils/index'
import { VxeTableInstance } from 'vxe-table'
import { GetSupplierInfo, saveSupplierSettlement, saveSupplierExpand, saveSupplierFollow, updateSupplierLicenseFileIds } from '@/servers/Supplier'
import { GetCommon, GetDeliveryRegionList, GetProductCategoryList } from '@/servers/Common'

const VITE_APP_ENV = import.meta.env.VITE_APP_ENV

let uploadUrl = '/api/Files/UploadFile'
let previewUrl = '/api/Files/ViewByFileId'
if (VITE_APP_ENV === 'development') {
  uploadUrl = `/api/api/Files/UploadFile`
  previewUrl = '/api/api/Files/ViewByFileId'
}

const stepList = [
  {
    title: '基本信息',
    icon: 1,
  },
  {
    title: '扩展信息',
    icon: 2,
  },
  {
    title: '跟进信息',
    icon: 3,
  },
  {
    title: '证书上传',
    icon: 4,
  },
  {
    title: '审核结果',
    icon: 5,
  },
]

const contactsTableRef = ref<VxeTableInstance>()
const financeTableRef = ref<VxeTableInstance>()

const userData = JSON.parse(localStorage.getItem('userData') || '{}')

const step = ref(0)
const loading = ref(false)
const isOriFormSupplierName = ref(false) // 接口获取出来的数据中是否有公司名称
const formRef = ref()
const form = ref<any>({
  srs_supplier_finance_infos: [
    {
      id: 0,
      account_name: '',
      account_type: null,
      collection_bank: '',
      collection_bank_branch: '',
      collection_card_number: '',
      is_default: null,
    },
  ],
  srs_supplier_contact_infos: [
    {
      id: 0,
      name: '',
      job: '',
      mobile_phone_number: '',
      email: '',
      is_default: null,
    },
  ],
})
const certificateFileList = ref<any>([])
const rules = ref({}) as any
const allRules: Record<string, Rule[]> = {
  fileList: [
    {
      required: true,
      message: '请上传营业执照',
      trigger: 'change',
      validator: () => {
        if (fileList.value.length === 0) {
          return Promise.reject('请上传营业执照')
        }
        return Promise.resolve()
      },
    },
  ],
  credit_code: [
    {
      required: true,
      message: '请输入统一社会信用代码',
    },
  ],
  company_type: [
    {
      required: true,
      message: '请选择公司类型',
    },
  ],
  supplier_name: [
    {
      required: true,
      message: '请输入公司名称',
    },
  ],
  business_scale: [
    {
      required: true,
      message: '请选择公司规模',
    },
  ],
  establishment_date: [
    {
      required: true,
      message: '请选择成立日期',
    },
  ],
  business_license_address: [
    {
      required: true,
      message: '请输入营业执照地址',
      validator: () => {
        if (!form.value.bussinessAddress) {
          return Promise.reject('请输入营业执照地址')
        }
        return Promise.resolve()
      },
    },
  ],
  office_address_detail: [
    {
      required: true,
      message: '请输入办公地址',
      validator: () => {
        if (!form.value.officeAddress) {
          return Promise.reject('请输入办公地址')
        }
        return Promise.resolve()
      },
    },
  ],
  legal_person_name: [
    {
      required: true,
      message: '请输入法人姓名',
    },
  ],
  certificate_type: [
    {
      required: true,
      message: '请选择证件类型',
    },
  ],
  certificate_number: [
    {
      required: true,
      message: '请输入证件号',
    },
    {
      validator: (_rule, value) => validateStr(_rule, value, 30),
      message: '输入内容不可超过30字符',
    },
  ],
  photoList: [
    {
      required: true,
      message: '请上传法人身份证',
      trigger: 'change',
      validator: () => {
        if (photoList.value.length === 0) {
          return Promise.reject('请上传法人身份证正面')
        }
        if (backPhotoList.value.length === 0) {
          return Promise.reject('请上传法人身份证反面')
        }
        return Promise.resolve()
      },
    },
  ],
  factory_scale: [
    {
      required: true,
      message: '请选择自营/合作工厂规模',
    },
  ],
  main_categories: [
    {
      required: true,
      message: '请选择主营类目',
    },
  ],
  main_regions: [
    {
      required: true,
      message: '请选择主营区域',
    },
  ],
  main_products: [
    {
      required: true,
      message: '请输入主营商品',
    },
  ],
  main_products_min_price: [
    {
      required: true,
      message: '',
    },
    {
      trigger: ['change', 'blur'],
      validator: () => {
        if ((!form.value.main_products_min_price && form.value.main_products_min_price !== 0) || (!form.value.main_products_max_price && form.value.main_products_max_price !== 0)) {
          return Promise.reject('请输入主营商品价格区间')
        }
        if (form.value.main_products_min_price > form.value.main_products_max_price) {
          return Promise.reject('请输入正确的主营商品价格区间')
        }
        return Promise.resolve()
      },
    },
  ],
  settlement_method: [
    {
      required: true,
      message: '请选择结算方式',
    },
  ],
  invoice_type: [
    {
      required: true,
      message: '请选择发票类型',
    },
  ],
  default_tax_rate: [
    {
      required: true,
      message: '请选择默认税率',
    },
  ],
  uploadFileList: [
    {
      required: true,
      message: '请上传文件',
      trigger: 'change',
      validator: () => {
        if (uploadFileList.value.length === 0) {
          return Promise.reject('请上传文件')
        }
        return Promise.resolve()
      },
    },
  ],
}
const formRulesKey = [
  [
    'fileList',
    'credit_code',
    'company_type',
    'supplier_name',
    'business_scale',
    'establishment_date',
    'business_license_address',
    'office_address_detail',
    'legal_person_name',
    'certificate_type',
    'certificate_number',
    'photoList',
  ],
  ['factory_scale', 'main_categories', 'main_regions', 'main_products', 'main_products_min_price'],
  ['settlement_method', 'invoice_type', 'default_tax_rate'],
  ['uploadFileList'],
]

const columns = [
  {
    title: '文件',
    dataIndex: 'original_name',
    width: '40%',
  },
  {
    title: '上传人',
    dataIndex: 'account_name',
    width: '20%',
  },
  {
    title: '上传时间',
    dataIndex: 'create_at',
    width: '20%',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '20%',
  },
]
const fileList = ref<any>([])
const photoList = ref<any>([])
const backPhotoList = ref<any>([])
const uploadFileList = ref<any>([])
const previewVisible = ref(false)
const previewImage = ref('')
const previewTitle = ref('')
const documentTypeOptions = ref<any>([])
const factoryscaleOptions = ref<any>([]) // 自营/合作工厂规模
const majorTypeOptions = ref<any>([])
const majorAreaOptions = ref<any>([])
// 是否默认
const trueOrFalseOptions = [
  {
    value: true,
    label: '是',
  },
  {
    value: false,
    label: '否',
  },
]

// 账户类型
const accountTypeOptions = ref<any>([])

const companyTypeOptions = ref<any>([]) // 公司类型
const companyModeOptions = ref<any>([]) // 公司规模

const settlementMethodOptions = ref<any>([]) // 结算方式
const invoiceTypeOptions = ref<any>([]) // 发票类型
const defaultTaxRateOptions = ref<any>([]) // 默认税率

// 将省市区数据转换为 a-cascader 所需的格式
const areaOptions = ref(
  areaCity.map((province) => ({
    value: province.name,
    label: province.name,
    children: province.children?.map((city) => ({
      value: city.name,
      label: city.name,
      children: city.children?.map((district) => ({
        value: district.name,
        label: district.name,
      })),
    })),
  })),
)

const validators = ({ row, isRequired, length, column, type }: any) => {
  const value = row[column.field]
  const tipsBefore = type == 'select' ? '请选择' : '请输入'
  if (typeof value != 'number' && typeof value != 'boolean' && isRequired && (!value || value.trim() === '')) {
    return Promise.reject(tipsBefore + column.title)
  }
  if (length && String(value).length > length) {
    return Promise.reject(`不可超过${length}字符`)
  }
  return Promise.resolve()
}

// 上传
// const beforeUpload = (file, uploadFileList) => {
//   const promise = new Promise((resolve, reject) => {
//     // 生成临时文件对象
//     const reader = new FileReader()
//     reader.readAsDataURL(file)
//     reader.onload = () => {
//       const tempFile = {
//         name: file.name,
//         uid: Date.now().toString(),
//         status: 'done',
//         url: reader.result as string,
//         uploadTime: getNowDateTime().formattedDateTime,
//       }
//       certificateFileList.value.push(tempFile)
//       console.log('certificateFileList', certificateFileList)
//     }
//     console.log(resolve)
//     return reject()
//   })
//   return promise
// }

const readFile = async (id) => {
  const url = `${previewUrl}?fileId=${id}`
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.login_token,
      },
    })
    console.log('response', response)
    if (!response.ok) {
      message.warning('获取失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    /*     const contentDisposition = response.headers.get('Content-Disposition')
    let fileName = 'unknown_file'
    if (contentDisposition) {
      const match = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (match != null && match[1]) {
        fileName = match[1].replace(/['"]/g, '')
      }
    }
    console.log('解析出文件名称', fileName) */

    // 可选：一段时间后释放内存
    setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    return { url: previewUrl /* , name: fileName */ }
  } catch (error) {
    console.error('转换失败:', error)
    throw error
  }
}

const beforeUpload = (file, uploadFileList, list) => {
  console.log('beforeUpload=============', file, uploadFileList, '=========')
  let check = true
  // eslint-disable-next-line no-async-promise-executor
  const promise = new Promise(async (resolve, reject) => {
    const arr = ['image/png', 'image/jpeg', 'application/pdf']
    if (arr.indexOf(file.type) == -1) {
      message.error('支持文件格式：PNG，JPEG，JPG，PDF')
      check = false
    }
    if (file.size / 1024 / 1024 > 10) {
      message.error('文件大小不得大于10M')
      check = false
    }
    if (check) {
      const formData = new FormData()
      formData.append('files', file) // 'file' 是后端接收文件的字段名
      formData.append('fileModule', 'Supplier')
      formData.append('account_id', userData.id)
      formData.append('company_id', userData.company_id)
      const response = await fetch(`${uploadUrl}`, {
        method: 'POST',
        body: formData,
        headers: {
          logintoken: userData.login_token,
        },
      })
      if (!response.ok) {
        throw new Error(`上传失败: ${response.status}`)
      }
      const res = await response.json()
      if (res.success == true) {
        file.id = res.data[0].id
        // const obj = {
        //   id: res.data[0].id,
        //   original_name: res.data[0].name,
        //   account_id: userData.id,
        //   account_name: userData.real_name,
        //   create_at: getNowDateTime().formattedDateTime,
        // }

        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          const tempFile = {
            original_name: file.name,
            uid: Date.now().toString(),
            status: 'done',
            url: reader.result as string,
            uploadTime: getNowDateTime().formattedDateTime,
            id: res.data[0].id,
          }
          list.push(tempFile)
          console.log('list', list)
        }

        console.log('上传成功:', res)
      }
      // eslint-disable-next-line no-promise-executor-return
      return reject()
    }
  })

  // console.log('file', tableobj.value.fileList)

  return promise
}

const ToPagePreviewbyId = async (id) => {
  const url = `${previewUrl}?fileId=${id}`
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.login_token,
      },
    })
    if (!response.ok) {
      message.warning('获取失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)
    // 在新窗口打开预览
    window.open(previewUrl, '_blank')
    // 可选：一段时间后释放内存
    setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    return previewUrl
  } catch (error) {
    console.error('转换失败:', error)
    throw error
  }
}

const handleCancel = () => {
  previewVisible.value = false
  previewTitle.value = ''
}

const handlePreview = async (file: UploadProps['fileList'][number]) => {
  // console.log('handlePreview-file', file)
  // await ToPagebyId(file.id)

  console.log('handlePreview-file', file)

  if (!file.url && !file.preview) {
    file.preview = (await getBase64(file.originFileObj)) as string
  }
  previewImage.value = file.url || file.preview
  previewVisible.value = true
  previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1)
}

function getBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })
}

const changeAddress = (val) => {
  console.log(val)
}

const onClickCreateCompany = () => {}

const onClickBack = () => {
  step.value--
}

const handleFormData = () => {
  if (step.value === 0) {
    if (form.value.bussinessAddress) {
      form.value.business_license_province = form.value.bussinessAddress[0]
      form.value.business_license_city = form.value.bussinessAddress[1]
      form.value.business_license_area = form.value.bussinessAddress[2]
    }
    if (form.value.officeAddress) {
      form.value.office_address_province = form.value.officeAddress[0]
      form.value.office_address_city = form.value.officeAddress[1]
      form.value.office_address_area = form.value.officeAddress[2]
    }
    form.value.business_license_file_id = fileList.value[fileList.value.length - 1]?.id
    form.value.id_card_front_file_id = photoList.value[photoList.value.length - 1]?.id
    form.value.id_card_back_file_id = backPhotoList.value[backPhotoList.value.length - 1]?.id
  }
  form.value.audit_main_categories = []
}

const onSubmit = async () => {
  console.log('form', form.value)
  try {
    await formRef.value.validateFields()
    console.log('表单校验器过了')
    handleFormData()
    const api = [saveSupplierSettlement, saveSupplierExpand, saveSupplierFollow, updateSupplierLicenseFileIds]
    try {
      if (loading.value) {
        return
      }
      loading.value = true
      let params = {}
      if (step.value === 3) {
        params = {
          id: form.value.id,
          license_file_ids: certificateFileList.value.map((n) => {
            return n.id
          }),
          is_audit: true,
          audit_type: '入驻审核',
        }
      } else {
        params = form.value
      }
      const res = await api[step.value](params)
      console.log('提交返回', res)
      step.value++
      loading.value = false
    } catch (err) {
      loading.value = false
    }
  } catch (error: any) {
    console.log('error', error)

    const firstErrorField = error.errorFields[0]?.name[0]
    if (firstErrorField) {
      const inputElement = document.getElementById(`form_item_${firstErrorField}`) as HTMLInputElement
      if (inputElement) {
        inputElement.focus()
      }
    }
  }
}

const changeContactDefault = (val, index, tableData) => {
  if (val) {
    form.value[tableData].forEach((item, i) => {
      console.log('index', index)
      if (index !== i) {
        item.is_default = false
      }
    })
  }
}

const onAddTableItem = (tableData) => {
  if (form.value[tableData].length < 5) form.value[tableData].push({})
}

const onDelContact = (index, tableData) => {
  form.value[tableData].splice(index, 1)
}

const onDelFile = (index) => {
  certificateFileList.value.splice(index, 1)
}

// const downloadLocalFile = (file) => {
//   console.log('row file', file)

//   const link = document.createElement('a')
//   // 如果是本地文件，使用 file.originFileObj 获取原始文件
//   if (file.originFileObj) {
//     link.href = URL.createObjectURL(file.originFileObj)
//     link.download = file.name
//   } else if (file.url) {
//     // 如果是 data URL 格式
//     link.href = file.url
//     link.download = file.name || 'image'
//   }
//   link.click()
//   // 释放 object URL
//   if (file.originFileObj) {
//     URL.revokeObjectURL(link.href)
//   }
// }

const getOptions = async () => {
  const res = await GetCommon()
  console.log('获取下拉列表', res)
  if (res.success == true) {
    for (const key in res.data) {
      res.data[key].forEach((item) => {
        item.value = parseInt(item.value.toString())
      })
    }
    companyTypeOptions.value = res.data.companytype_list
    companyModeOptions.value = res.data.businessscale_list
    documentTypeOptions.value = res.data.certificatetype_list
    settlementMethodOptions.value = res.data.settlementmethod_list
    invoiceTypeOptions.value = res.data.invoicetype_list
    defaultTaxRateOptions.value = res.data.defaulttaxrate_list
    accountTypeOptions.value = res.data.publicaccounttype_list
    factoryscaleOptions.value = res.data.factoryscale_list
  }

  GetProductCategoryList({ page: 1, pageSize: 9999 }).then((res) => {
    majorTypeOptions.value = res.data?.list.map((n) => {
      return {
        value: n.id,
        label: n.name,
      }
    })
  })

  GetDeliveryRegionList({ page: 1, pageSize: 9999 }).then((res) => {
    majorAreaOptions.value = res.data?.list.map((n) => {
      return {
        value: n.id,
        label: n.name,
      }
    })
  })
}

const getSupplierInfo = async () => {
  try {
    const res = await GetSupplierInfo()
    console.log('获取供应商信息', res?.data)
    form.value = res.data
    isOriFormSupplierName.value = !!form.value.supplier_name
    form.value.supplier_name = form.value.supplier_name || userData?.company || ''
    form.value.bussinessAddress = [form.value.business_license_province, form.value.business_license_city, form.value.business_license_area]
    form.value.officeAddress = [form.value.office_address_province, form.value.office_address_city, form.value.office_address_area]
    certificateFileList.value = form.value.license_files
    if (form.value.business_license_file_id) {
      const fileInfo = await readFile(form.value.business_license_file_id)
      fileList.value = [
        {
          uid: 1,
          url: fileInfo.url,
          // name: fileInfo.name,
          status: 'done',
          id: form.value.business_license_file_id,
        },
      ]
    }
    if (form.value.id_card_front_file_id) {
      const fileInfo = await readFile(form.value.id_card_front_file_id)
      photoList.value = [
        {
          uid: 2,
          url: fileInfo.url,
          // name: fileInfo.name,
          status: 'done',
          id: form.value.id_card_front_file_id,
        },
      ]
    }
    if (form.value.id_card_back_file_id) {
      const fileInfo = await readFile(form.value.id_card_back_file_id)
      backPhotoList.value = [
        {
          uid: 3,
          url: fileInfo.url,
          // name: fileInfo.name,
          status: 'done',
          id: form.value.id_card_back_file_id,
        },
      ]
    }
  } catch (err: any) {
    console.log('getSupplierInfo-err', err, userData?.company)
    if (err?.code === -2) {
      // 没有供应商信息,不需要显示报错
      form.value.supplier_name = userData?.company || ''
    }
  }
}

watch(
  () => form.value.srs_supplier_contact_infos,
  () => {
    if (form.value.srs_supplier_contact_infos.length == 1 || !form.value.srs_supplier_contact_infos.find((n) => n.is_default)) {
      form.value.srs_supplier_contact_infos[0].is_default = true
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(
  () => form.value.srs_supplier_finance_infos,
  () => {
    if (form.value.srs_supplier_finance_infos.length == 1 || !form.value.srs_supplier_finance_infos.find((n) => n.is_default)) {
      form.value.srs_supplier_finance_infos[0].is_default = true
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(
  () => step.value,
  () => {
    getSupplierInfo()
    formRulesKey[step.value].forEach((key) => {
      rules.value[key] = allRules[key]
    })
  },
  { immediate: true },
)

watch(
  () => form.value.is_long,
  (val) => {
    if (val) {
      delete rules.value.business_license_validity
    } else {
      rules.value.business_license_validity = allRules.business_license_validity
    }
  },
)

onMounted(() => {
  getSupplierInfo()
  getOptions()
})
</script>

<style lang="scss" scoped>
.main {
  height: calc(100% - 24px) !important;
  overflow-y: scroll;
  color: #333;
}

:deep(.ant-steps) {
  padding: 1rem 6.25rem !important;
}

:deep(.ant-steps-icon) {
  display: inline-block;
  width: 3rem !important;
  height: 3rem !important;
  padding: 0 !important;
  font-weight: bold;
  line-height: 3rem !important;
  color: white !important;
  background: orangered;
  border-radius: 100%;
}

:deep(.ant-steps-item-wait .ant-steps-icon) {
  background: #b9b9b9 !important;
}

.title {
  padding: 8px;
  margin: 20px !important;
  font-weight: bold;
  background-color: #e7f2ff !important;
}

.input-width {
  width: 31.25rem;
}

.short {
  width: 23rem;
}

.price-before :deep(.ant-input-number-group-addon) {
  background-color: #fff;
  border-radius: 0 !important;
}

.price-affter :deep(.ant-input-number) {
  border-left: none;
  border-radius: 0 !important;
}

.grey {
  color: grey;
}

.vxe-table-box {
  padding: 0 1.7rem;
  margin-bottom: 2rem;
}

.w100 {
  width: 100%;
}

.rule-table :deep(.ant-form-item) {
  margin: 0;
}

.red {
  color: red;
}

.strong {
  font-weight: bold;
}

.font-xl {
  font-size: 1.875rem;
}

.font-l {
  font-size: 1.5rem;
}

.pd20 {
  padding: 20px;
}

.color333 {
  color: #333;
}

.success-icon {
  font-size: 100px;
  color: orange;
}

.fail-icon {
  font-size: 100px;
}
</style>
