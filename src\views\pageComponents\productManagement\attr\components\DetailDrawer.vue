<template>
  <a-drawer :footerStyle="{ paddingLeft: '24px' }" :headerStyle="{ paddingTop: logVisble ? '12px' : '', paddingBottom: logVisble ? '12px' : '' }" v-model:open="detailVisible" width="890" title="查看属性" placement="right" :maskClosable="false">
    <template v-if="logVisble" #extra>
      <a-space>
        <a-button id="languageLog" @click="log">日志</a-button>
      </a-space>
    </template>
    <div class="detailBox">
      <a-spin v-show="detailloading" />
      <a-form :colon="false" :label-col="{ style: { width: '160px', marginRight: '20px' }}" v-if="!detailloading && target">
        <!-- <div class="detailFormTitle">属性编码：{{ target.code }}</div>
        <a-form-item label="多语言配置">
          <span class="detailValue">{{ target.language_config.map(e => e.language_name).join('，') }}</span>
        </a-form-item> -->
        <div class="detailFormTitle">基本信息</div>
         <a-form-item label="属性编码">
          <span class="detailValue">{{ target.code }}</span>
        </a-form-item>
        <div v-for="(item, index) in target.language_config" :key="index">
          <a-form-item>
            <template #label>
              <a-tooltip :mouseEnterDelay="`属性名称`.length <= 20 ? 10000 : 0.1">
                <template #title>
                  <span>{{ `属性名称` }}</span>
                </template>
                <span>{{ `属性名称`.length > 20 ? `属性名称`.slice(0,20) + '...' : `属性名称` }}</span>
              </a-tooltip>
            </template>
            <span class="detailValue">{{ item.attr_name }}</span>
          </a-form-item>
        </div>
        <!-- <a-form-item label="备注">
          <span class="detailValue">{{ target.remark ? target.remark : '--' }}</span>
        </a-form-item> -->
        <div class="detailFormTitle">数据类型</div>
        <a-form-item label="数据类型">
          <span class="detailValue">{{ typeOptions.find(e => e.value === target.type)?.label || '--' }}</span>
        </a-form-item>
        <div v-if="target.type === 1">
          <a-form-item label="字数限制">
            <span class="detailValue">{{ target.single_line_text_type.word_limit_count ? target.single_line_text_type.word_limit_count : '--' }}</span>
          </a-form-item>
        </div>
        <div v-if="target.type === 2">
          <a-form-item label="字数限制">
            <span class="detailValue">{{ target.multi_line_text_type.word_limit_count ? target.multi_line_text_type.word_limit_count : '--' }}</span>
          </a-form-item>
        </div>
        <div v-if="target.type === 3">
          <a-form-item label="精度">
            <span class="detailValue">{{ target.number_input_type.precision ? target.number_input_type.precision : '--' }}</span>
          </a-form-item>
          <a-form-item label="舍入类型">
            <span class="detailValue">{{ roundingTypeOptions.find(e => e.value == target.number_input_type.rounding_type)?.label || '--' }}</span>
          </a-form-item>
          <a-form-item label="数值范围">
            <span class="detailValue">{{ target.number_input_type.min_value }}</span>
            <span class="detailValue"> ~ </span>
            <span class="detailValue">{{ target.number_input_type.max_value }}</span>
          </a-form-item>
          <a-form-item label="显示格式">
            <div v-if="target.number_input_type.display_format && target.number_input_type.display_format.length > 0">
              <a-checkbox-group v-model:value="target.number_input_type.display_format">
                <a-checkbox disabled v-for="(item, index) in displayFormatOptions" :key="index" :value="item.value"><span style="color: rgba(0, 0, 0, 0.88)!important;">{{ item.label }}</span></a-checkbox>
              </a-checkbox-group>
            </div>
            <span v-else class="detailValue">--</span>
          </a-form-item>
        </div>
        <div v-if="target.type === 4">
          <a-form-item label="单位类型">
            <span class="detailValue">{{ [{label: '计量单位', value: 1},{label: '货币单位', value: 2}].find(e => e.value === target.unit_input_type.unit_type)?.label || '--' }}</span>
          </a-form-item>
          <a-form-item v-if="target.unit_input_type.unit_type == 1" label="计量单位">
            <span class="detailValue">{{ meteringUnitOptions.filter(e => target.unit_input_type.metering_unit_ids.indexOf(e.value) != -1).map(e => e.label).join('，') }}</span>
          </a-form-item>
          <a-form-item v-if="target.unit_input_type.unit_type == 2" label="货币符号">
            <span class="detailValue">{{ currencySymbolOptions.filter(e => target.unit_input_type.currency_unit_ids.indexOf(e.value) != -1).map(e => e.label).join('，') }}</span>
          </a-form-item>
          <a-form-item label="数值范围">
            <span class="detailValue">{{ target.unit_input_type.min_value }}</span>
            <span class="detailValue"> ~ </span>
            <span class="detailValue">{{ target.unit_input_type.max_value }}</span>
          </a-form-item>
          <a-form-item label="显示格式">
            <div >
              <a-checkbox-group v-model:value="target.unit_input_type.display_format">
                <a-checkbox disabled v-for="(item, index) in displayFormatOptions" :key="index" :value="item.value"><span style="color: rgba(0, 0, 0, 0.88)!important;">{{ item.label }}</span></a-checkbox>
              </a-checkbox-group>
            </div>
            <!-- <span v-else class="detailValue">--</span> -->
          </a-form-item>
        </div>
        <div v-if="target.type === 5">
          <a-form-item label="样式类型">
            <span class="detailValue">{{ timeStyleTypeOptions.find(e => e.value === target.date_time_picker_type.style_type)?.label || '--' }}</span>
          </a-form-item>
          <a-form-item v-if="!target.date_time_picker_type.default_value || !target.date_time_picker_type.default_value.length" label="时间限制" :name="['date_time_picker_type', 'time_limit']">
            <a-checkbox-group v-model:value="target.date_time_picker_type.time_limit">
              <a-checkbox disabled v-for="(item, index) in timeLimitOptions" :key="index" :value="item.value"><span style="color: rgba(0, 0, 0, 0.88)!important;">{{ item.label }}</span></a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          <a-form-item v-if="!target.date_time_picker_type.time_limit || !target.date_time_picker_type.time_limit.length" label="默认值" :name="['date_time_picker_type', 'default_value']">
            <a-checkbox-group v-model:value="target.date_time_picker_type.default_value">
              <a-checkbox disabled v-for="(item, index) in defaultValueOptions" :key="index" :value="item.value"><span style="color: rgba(0, 0, 0, 0.88)!important;">{{ item.label }}</span></a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </div>
        <div v-if="target.type === 6">
          <div class="detailFormTitle">选项值</div>
          <a-table style="margin-bottom: 12px;" v-if="radioTypeColumns.length != 0" size="small" :pagination="false" :columns="radioTypeColumns" :data-source="target.radio_type.option_list">
            <template #bodyCell="{ text, record, index, column }">
              <template v-if="column.key.indexOf('column_label_') != -1">
                <div>{{ record[column.key] }}</div>
              </template>
              <template v-if="column.key === 'is_def'">
                <a-checkbox disabled v-model:checked="record[column.key]"></a-checkbox>
              </template>
            </template>
          </a-table>
        </div>
        <div v-if="target.type === 7">
          <div class="detailFormTitle">选项值</div>
          <a-table style="margin-bottom: 12px;" v-if="radioTypeColumns.length != 0" size="small" :pagination="false" :columns="radioTypeColumns" :data-source="target.check_box_type.option_list">
            <template #bodyCell="{ text, record, index, column }">
              <template v-if="column.key.indexOf('column_label_') != -1">
                <div>{{ record[column.key] }}</div>
              </template>
              <template v-if="column.key === 'is_def'">
                <a-checkbox disabled v-model:checked="record[column.key]"></a-checkbox>
              </template>
            </template>
          </a-table>
        </div>
        <div v-if="target.type === 8">

        </div>
        <div v-if="target.type === 9">
          <a-form-item label="图片数量">
            <span class="detailValue">{{ target.image_type.image_count }}</span>
          </a-form-item>
          <a-form-item label="图片格式">
            <span class="detailValue">{{ imageTypeOptions.filter(e => target.image_type.image_types.indexOf(e.value) != -1 ).map(e => e.label).join('，') }}</span>
          </a-form-item>
          <a-form-item label="图片大小">
            <span class="detailValue">{{ target.image_type.image_size }}</span>
            <span class="detailValue">{{ fileSizeUnitOptions.find(e => e.value === target.image_type.image_size_unit)?.label || '--' }}</span>
          </a-form-item>
        </div>
        <div v-if="target.type === 10">
          <a-form-item label="视频数量">
            <span class="detailValue">{{ target.video_type.video_count }}</span>
          </a-form-item>
          <a-form-item label="视频格式">
            <span class="detailValue">{{ videoTypeOptions.filter(e => target.video_type.video_types.indexOf(e.value) != -1 ).map(e => e.label).join('，') }}</span>
          </a-form-item>
          <a-form-item label="视频大小">
            <span class="detailValue">{{ target.video_type.video_size }}</span>
            <span class="detailValue">{{ fileSizeUnitOptions.find(e => e.value === target.video_type.video_size_unit)?.label || '--' }}</span>
          </a-form-item>
        </div>
        <div v-if="target.type === 11">
          <a-form-item label="文件数量">
            <span class="detailValue">{{ target.file_type.file_count }}</span>
          </a-form-item>
          <a-form-item label="文件格式">
            <span class="detailValue">{{ fileTypeOptions.filter(e => target.file_type.file_type_ids.indexOf(e.value) != -1 ).map(e => e.label).join('，') }}</span>
          </a-form-item>
          <a-form-item label="文件大小">
            <span class="detailValue">{{ target.file_type.file_size }}</span>
            <span class="detailValue">{{ fileSizeUnitOptions.find(e => e.value === target.file_type.file_size_unit)?.label || '--' }}</span>
          </a-form-item>
        </div>
        <div v-if="target.type === 12">
          <a-form-item label="引用类型">
            <!-- <span class="detailValue">{{ multiAttrSelectOptions.find(e => e.value === target.reference_type.multi_attr_id).label }}</span> -->
             <span>{{ availableReferenceTypeOptions.length ? availableReferenceTypeOptions.find(e => e.value === target.reference_type.available_reference_type)?.label || '--' : '--' }}</span>
          </a-form-item>
          <a-form-item v-if="target.reference_type.available_reference_type === 1" label="复合属性">
            <span class="detailValue">{{ multiAttrSelectOptions.find(e => e.value === target.reference_type.multi_attr_id)?.label || '--' }}</span>
          </a-form-item>
          <a-form-item v-if="target.reference_type.available_reference_type === 2 || target.reference_type.available_reference_type === 3" label="数据源">
            <span>{{ dataSourceOptions.length ? (target.reference_type.available_reference_type === 2 ? dataSourceOptions.filter(e => e.value <= 1000) : dataSourceOptions.filter(e => e.value > 1000)).find(e => e.value === target.reference_type.data_source)?.label || '--' : '--' }}</span>
          </a-form-item>
          <a-form-item v-if="target.reference_type.available_reference_type" label="选项类型">
            <span>{{ [{ label: '单选', value: 1 }, { label: '多选', value: 2 }].find(e => e.value === target.reference_type.option_type)?.label || '--' }}</span>
          </a-form-item>
        </div>
        <div v-if="target.type === 1 || target.type === 2">
          <div class="detailFormTitle">默认值</div>
          <div v-for="(item, index) in target.language_config" :key="index">
            <a-form-item>
              <template #label>
                <a-tooltip :mouseEnterDelay="`默认值(${item.language_name})`.length <= 20 ? 10000 : 0.1">
                  <template #title>
                    <span>{{ `默认值(${item.language_name})` }}</span>
                  </template>
                  <span>{{ `默认值(${item.language_name})`.length > 20 ? `默认值(${item.language_name})`.slice(0,20) + '...' : `默认值(${item.language_name})` }}</span>
                </a-tooltip>
              </template>
              <span class="detailValue">{{ item.default_value ? item.default_value : '--' }}</span>
            </a-form-item>
          </div>
        </div>
        <div class="detailFormTitle">提示文本</div>
        <div v-for="(item, index) in target.language_config" :key="index">
          <a-form-item>
            <template #label>
              <a-tooltip :mouseEnterDelay="`提示文本`.length <= 20 ? 10000 : 0.1">
                <template #title>
                  <span>{{ `提示文本` }}</span>
                </template>
                <span>{{ `提示文本`.length > 20 ? `提示文本`.slice(0,20) + '...' : `提示文本` }}</span>
              </a-tooltip>
            </template>
            <span class="detailValue">{{ item.attr_tips ? item.attr_tips : '--' }}</span>
          </a-form-item>
        </div>
        <div class="detailFormTitle">相关信息</div>
        <a-form-item label="所属分组">
          <span class="detailValue">{{ target.attr_group_name ? target.attr_group_name : '' }}</span>
        </a-form-item>
        <a-form-item label="是否必填">
          <span class="detailValue">{{ target.is_must ? '是' : '否' }}</span>
        </a-form-item>
        <!-- <a-form-item label="状态">
          <span class="detailValue">{{ target.status == 0 ? '停用' : '启用' }}</span>
        </a-form-item> -->
        <div class="detailFormTitle">其他信息</div>
        <div style="display: flex;">
          <a-form-item label="创建时间">
            <div class="detailValue w200">{{ target.create_at }}</div>
          </a-form-item>
          <!-- <a-form-item label="创建人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.create_user_real_name }}</div>
                <div v-show="target.create_user_scope == 1">所属公司：{{ target.create_user_company_name ? target.create_user_company_name : '--' }}</div>
                <div v-show="target.create_user_scope != 1">所属客户：{{ target.create_user_customer_name ? target.create_user_customer_name : '--' }}</div>
                <div>所在部门：{{ target.create_user_department ? target.create_user_department : '--' }}</div>
                <div>岗<span style="visibility: hidden;">占位</span>位：{{ target.create_user_jobtitlename ? target.create_user_jobtitlename : '--' }}</div>
              </template>
              <div style="display: flex;align-items: center;">
                <span class="detailValue">{{ target.create_user_real_name ? target.create_user_real_name : '--' }}</span>
                <span v-if="target.create_user_department || target.create_user_jobtitlename" class="detailValueDescription">（
                  <span v-if="target.create_user_jobtitlename">{{ target.create_user_jobtitlename }}&nbsp;|&nbsp;</span>
                  <span v-if="target.create_user_department">{{ target.create_user_department.length > 10 ? target.create_user_department.slice(0, 10) + '...' : target.create_user_department }} </span>
                ）</span>
              </div>
            </a-tooltip>
          </a-form-item> -->
        </div>
        <div style="display: flex;">
          <a-form-item label="最后修改时间">
            <div class="detailValue w200">{{ target.update_at }}</div>
          </a-form-item>
          <!-- <a-form-item label="最后修改人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.update_user_real_name }}</div>
                <div v-show="target.update_user_scope == 1">所属公司：{{ target.update_user_company_name ? target.update_user_company_name : '--' }}</div>
                <div v-show="target.update_user_scope != 1">所属客户：{{ target.update_user_customer_name ? target.update_user_customer_name : '--' }}</div>
                <div>所在部门：{{ target.update_user_department ? target.update_user_department : '--' }}</div>
                <div>岗<span style="visibility: hidden;">占位</span>位：{{ target.update_user_jobtitlename ? target.update_user_jobtitlename : '--' }}</div>
              </template>
              <div style="display: flex;align-items: center;">
                <span class="detailValue">{{ target.update_user_real_name ? target.update_user_real_name : '--' }}</span>
                <span v-if="target.update_user_department || target.update_user_jobtitlename" class="detailValueDescription">（
                  <span v-if="target.update_user_jobtitlename">{{ target.update_user_jobtitlename }}&nbsp;|&nbsp;</span>
                  <span v-if="target.update_user_department">{{ target.update_user_department.length > 10 ? target.update_user_department.slice(0, 10) + '...' : target.update_user_department }} </span>
                ）</span>
              </div>
            </a-tooltip>
          </a-form-item> -->
        </div>
      </a-form>
    </div>
  </a-drawer>
  <!-- 日志 -->
  <log-drawer ref="logDrawerRef" />
</template>

    

<script lang="ts" setup>
  import { ref } from 'vue'
  import LogDrawer from '@/components/LogDrawer.vue';
  import { GetAttrDetail, GetMeteringUnitSelectOption, GetFileTypeSelectOption, GetLanguageSelectOption, GetMultiAttrSelectOptionSimple } from '@/servers/Attr';
  import useAppStore from '@/store/modules/app';
  import {GetPlmEnum  } from '@/servers/Common'


  const logDrawerRef = ref<any>(null)
  const detailVisible = ref(false)
  const detailloading = ref(false)
  const logVisble = ref(false)
  const target = ref<any>(null)
  const typeOptions = ref<any[]>([])
  const languagesOptions = ref<any[]>([])
  const meteringUnitOptions = ref<any[]>([])
  const currencySymbolOptions = ref<any[]>([])
  const displayFormatOptions = ref<any[]>([])
  const roundingTypeOptions = ref<any[]>([])
  const timeStyleTypeOptions = ref<any[]>([])
  const defaultValueOptions = ref<any[]>([])
  const timeLimitOptions = ref<any[]>([])
  const imageTypeOptions = ref<any[]>([])
  const videoTypeOptions = ref<any[]>([])
  const fileSizeUnitOptions = ref<any[]>([])
  const fileTypeOptions = ref<any[]>([])
  const checkboxDisplayTypeOptions = ref<any[]>([])
  const radioDisplayTypeOptions = ref<any[]>([])
  const radioTypeColumns = ref<any[]>([])
  const multiAttrSelectOptions = ref<any[]>([])
  const dataSourceOptions = ref<any[]>([])
  const availableReferenceTypeOptions = ref<any[]>([])

  const open = (id: string, boolean: boolean) => {
    radioTypeColumns.value = []
    target.value = null
    detailloading.value = true
    logVisble.value = boolean
    detailVisible.value = true

    // 并行加载枚举数据和属性详情
    getEnum()

    GetAttrDetail({ id}).then(res => {
      // 处理单位输入类型的显示格式
      if (res.data.unit_input_type) {
        if (res.data.unit_input_type.display_format) {
          res.data.unit_input_type.display_format = [res.data.unit_input_type.display_format]
        } else {
          res.data.unit_input_type.display_format = []
        }
      }
      // 处理数字输入类型的显示格式
      if (res.data.number_input_type) {
        if (res.data.number_input_type.display_format) {
          res.data.number_input_type.display_format = [res.data.number_input_type.display_format]
        } else {
          res.data.number_input_type.display_format = []
        }
      }
      // 处理日期时间选择器的时间限制
      if (res.data.date_time_picker_type) {
        if (res.data.date_time_picker_type.time_limit) {
          res.data.date_time_picker_type.time_limit = [res.data.date_time_picker_type.time_limit]
        } else {
          res.data.date_time_picker_type.time_limit = []
        }
        if (res.data.date_time_picker_type.default_value) {
          res.data.date_time_picker_type.default_value = [res.data.date_time_picker_type.default_value]
        } else {
          res.data.date_time_picker_type.default_value = []
        }
      }
      if (res.data.radio_type && res.data.radio_type.option_list && res.data.radio_type.option_list.length != 0) {
        var newArr: any[] = []
        res.data.radio_type.option_list.forEach((x: any) => {
          var obj: any = {
            is_def: x.is_def,
            value: x.value
          }
          x.option_label_list.forEach((y: any) => {
            obj[`column_label_${y.language_id}`] = y.label
          })
          newArr.push(obj)
        })
        res.data.radio_type.option_list = newArr
      }
      if (res.data.check_box_type && res.data.check_box_type.option_list && res.data.check_box_type.option_list.length != 0) {
        var newArr: any[] = []
        res.data.check_box_type.option_list.forEach((x: any) => {
          var obj: any = {
            is_def: x.is_def,
            value: x.value
          }
          x.option_label_list.forEach((y: any) => {
            obj[`column_label_${y.language_id}`] = y.label
          })
          newArr.push(obj)
        })
        res.data.check_box_type.option_list = newArr
      }
      // 单行文本
      if (res.data.type === 1) {
        res.data.single_line_text_type.default_value_config.forEach((x: any) => {
          res.data.language_config.find((e: any) => e.language_id === x.language_id).default_value = x.default_value
        })
      }
      // 多行文本
      if (res.data.type === 2) {
        res.data.multi_line_text_type.default_value_config.forEach((x: any) => {
          res.data.language_config.find((e: any) => e.language_id === x.language_id).default_value = x.default_value
        })
      }
      GetLanguageSelectOption().then(res1 => {
        res1.data.forEach((x: any) => {
          x.label = x.language_name
          x.value = x.id
          x.default = x.is_default_language
        })
        languagesOptions.value = res1.data
        languagesOptions.value.forEach((x: any) => {
          if (res.data.language_config.map((e: any) => e.language_id).indexOf(x.value) != -1) {
            radioTypeColumns.value.push({
              title: `选项值(${x.language_name})`,
              key: `column_label_${x.value}`
            })
          }
        })
        radioTypeColumns.value = radioTypeColumns.value.concat([{ title: '默认勾选', key: 'is_def', width: 70 }])
      })
      console.log('属性详情数据:', res.data)
      console.log('当前typeOptions:', typeOptions.value)
      target.value = res.data
      detailloading.value = false
    }).catch(() => {
      detailloading.value = false
    })
    GetMultiAttrSelectOptionSimple().then(res => {
    res.data.forEach((x: any) => {
      x.label = x.multi_attr_name
      x.value = x.id
    })
    multiAttrSelectOptions.value = res.data
  })
    GetMeteringUnitSelectOption().then(res => {
      res.data.forEach((x: any) => {
        x.label = x.metering_unit_name
        x.value = x.id
      })
      meteringUnitOptions.value = res.data
    })
    GetFileTypeSelectOption().then(res => {
      res.data.forEach((x: any) => {
        x.label = x.type_name
        x.value = x.id
      })
      fileTypeOptions.value = res.data
    })
  };
  const log = () => {
    logDrawerRef.value.open(target.value);
  }
  const getEnum = () => {
    return GetPlmEnum().then((res: any) => {
     
      timeStyleTypeOptions.value = res.data.attr.style_type || []
      roundingTypeOptions.value = res.data.common.rounding_type || []
      typeOptions.value = res.data.attr.type || []
      currencySymbolOptions.value = res.data.attr.currency_symbol_list || []
      displayFormatOptions.value = res.data.attr.display_format || []
      timeLimitOptions.value = res.data.attr.time_limit || []
      defaultValueOptions.value = res.data.attr.default_value || []
      fileSizeUnitOptions.value = res.data.attr.file_size_unit || []
      videoTypeOptions.value = res.data.attr.video_type || []
      imageTypeOptions.value = res.data.attr.image_type || []
      radioDisplayTypeOptions.value = res.data.attr.radio_display_type || []
      checkboxDisplayTypeOptions.value = res.data.attr.checkbox_display_type || []
      availableReferenceTypeOptions.value = res.data.attr.available_reference_type || []
      dataSourceOptions.value = res.data.attr.data_source || []
      console.log('typeOptions 设置为:', typeOptions.value)
      console.log('枚举数据',res);
      
    }).catch((error: any) => {
      console.error('枚举数据加载失败:', error)
    });
};
  // 暴露方法
  defineExpose({
    open
  });
</script>

<style lang="scss" scoped>
  .detailFormTitle {
    padding: 6px 20px;
    font-size: 14px;
    background: #F4F7FE;
    margin-bottom: 16px;
    color: #333;
    border-radius: 4px;
  }
  .w350 {
    width: 350px;
  }
  .w250 {
    width: 250px;
  }
  .w150 {
    width: 150px;
  }
  .w200 {
    width: 200px;
  }
  .description {
    color: rgba(0,0,0,0.5);
    font-size: 12px;
    padding-left: 20px;
    white-space: nowrap;
  }
  .detailValueDescription {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.5);
  }
  .detailValue {
    word-break: break-all;
  }
  ::v-deep(.ant-form-item-label) {
    overflow: visible;
    white-space: wrap;
  }
</style>
