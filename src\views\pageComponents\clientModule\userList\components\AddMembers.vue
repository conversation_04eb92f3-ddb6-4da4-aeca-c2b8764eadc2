<template>
  <a-drawer v-model:open="openDrawer" placement="right" :width="500" :maskClosable="false" title="新建用户" @after-open-change="afterOpenChange" @close="closeDrawer">
    <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
      <a-form-item label="用户名" name="user_name">
        <a-input placeholder="请输入用户名" v-model:value="formState.user_name" :maxlength="100" />
      </a-form-item>

      <a-form-item label="别名" name="account_name">
        <a-input placeholder="请输入别名" v-model:value="formState.account_name" :maxlength="100" />
      </a-form-item>

      <a-form-item label="真实姓名" name="real_name">
        <a-input placeholder="请输入真实姓名" v-model:value="formState.real_name" :maxlength="30" />
      </a-form-item>

      <a-form-item label="工号" name="job_id">
        <a-input v-if="showOaDepartment?.company_category_type == 1" placeholder="请输入工号" v-model:value="formState.job_id" :maxlength="6" v-numberEng />
        <a-input v-else placeholder="请输入工号" v-model:value="formState.job_id" :maxlength="30" v-un-chinese />
      </a-form-item>

      <a-form-item label="所属企业" name="company_id">
        <a-select showArrow placeholder="请选择所属企业" v-model:value="formState.company_id" :options="enterpriseOption" @change="handleCompanyChange">
          <template #suffixIcon>
            <down-outlined />
          </template>
        </a-select>
      </a-form-item>

      <a-form-item label="所属部门" name="department_ids">
        <a-select
          showArrow
          mode="multiple"
          placeholder="请选择所属部门"
          v-model:value="formState.department_ids"
          :options="departmentOption"
          @click.stop="openDepartmentSelect"
          @deselect="handleDepartmentDeselect"
          :open="false"
        >
          <template #tagRender="{ label, closable, onClose, option, value }">
            <a-tag class="dragTag" :closable="closable" style="display: flex; align-items: center; margin: 2px; font-size: 12px; cursor: pointer" :key="value" @close="onClose">
              <span>{{ label }}</span>
              <span class="ml-2" v-if="option?.index == 0">主部门</span>
            </a-tag>
          </template>
        </a-select>
      </a-form-item>

      <a-form-item label="所属岗位" name="position_id">
        <a-select showArrow showSearch optionFilterProp="label" placeholder="请选择所属岗位" v-model:value="formState.position_id" :options="jobOption" />
      </a-form-item>

      <a-form-item label="直接上级" name="leader_id">
        <a-select
          showArrow
          allowClear
          showSearch
          optionFilterProp="account_name"
          placeholder="请选择直接上级"
          v-model:value="formState.leader_id"
          :options="leaderOption"
          :field-names="{ label: 'account_name', value: 'id' }"
        />
      </a-form-item>

      <a-form-item label="关联OA成员" name="oa_id" v-if="showOaDepartment?.company_category_type == 1">
        <a-select showSearch v-model:value="formState.oa_id" :options="oaMemberOption" optionFilterProp="last_name" placeholder="请选择" allowClear :field-names="{ label: 'oa_name', value: 'id' }">
          <template #option="{ oa_name }">
            <div class="select-option">
              <div>{{ oa_name }}</div>
            </div>
          </template>
        </a-select>
      </a-form-item>

      <a-form-item label="系统角色" name="role_ids">
        <a-select showArrow showSearch optionFilterProp="label" placeholder="请选择系统角色" v-model:value="formState.role_ids" :options="roleOption" />
      </a-form-item>
    </a-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="gap-12px flex">
        <a-button :loading="submitLoading" type="primary" @click="handleSave">保存</a-button>
        <a-button :loading="submitLoading" @click="closeDrawer">取消</a-button>
      </div>
    </template>
  </a-drawer>

  <select-depart v-if="showSelectDepart" ref="selectDepartRef" @change="handleDepartmentChange" :tree-data="departmentTreeData" @close="showSelectDepart = false" />

  <!-- <account-invite-modal ref="accountInviteRef" /> -->
</template>

<script setup lang="ts">
import { ref, computed, h, getCurrentInstance, nextTick, onMounted } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { AddUserAccount, GetAccountSelectOption } from '@/servers/UserManagerNew'
import { GetList as GetRoleList } from '@/servers/RoleNew'
import { GetCompanyTree } from '@/servers/CompanyArchitecture'
import { cloneDeep, createLoadingDebounce } from '@/utils'
import Sortable from 'sortablejs'
import SelectDepart from './SelectDepart.vue'
// import AccountInviteModal from '@/views/user/accountLists/components/AccountInviteModal.vue'

const { proxy }: any = getCurrentInstance()

const emit = defineEmits(['query', 'close'])
const openDrawer = ref(false)

const selectDepartRef = ref()
const submitLoading = ref(false)

// const accountInviteEl = useTemplateRef<typeof AccountInviteModal>('accountInviteRef')

const formRef = ref<FormInstance>()
// 表单数据
const formState = ref<any>({
  department: [],
})
// 定义部门树节点接口
interface DepartmentTreeNode {
  id: string | number // 节点ID
  department_name?: string // 部门名称
  company_name?: string // 公司名称（可选）
  type: number // 节点类型（1=公司，2=部门等）
  childs?: DepartmentTreeNode[] // 子节点（可选）
  [key: string]: any // 允许其他属性
}
// 部门树数据
const departmentTreeData = ref<DepartmentTreeNode[]>([])
// 企业下拉框
const enterpriseOption = ref<any[]>([])
// 部门下拉框
const departmentOption = ref<any[]>([])
// 岗位下拉框
const jobOption = ref([])
// 直接上级下拉框
const leaderOption = ref([])
// 关联OA成员下拉框
const oaMemberOption = ref<any[]>([])
// 系统角色下拉框
const roleOption = ref<any[]>([])

const showSelectDepart = ref(false)

// 表单验证规则（响应式）
const rules = computed(() => {
  const currentCompany = getCurrentCompany(formState.value.company_id)
  const isXiyue = currentCompany && currentCompany.company_category_type == 1
  return {
    user_name: [{ required: true, message: '请完善用户名信息' }],
    account_name: [{ required: true, message: '请完善别名信息' }],
    real_name: [
      {
        required: true,
        message: '请完善真实姓名信息',
      },
    ],
    company_id: [{ required: true, message: '请完善所属企业信息' }],
    role_ids: [{ required: true, message: '请完善系统角色信息' }],
    job_id: isXiyue ? [{ required: true, message: '请完善工号信息' }] : [],
  }
})

// 获取直接上级下拉框
const GetLeaderList = (id: any) => {
  return GetAccountSelectOption({
    company_id: id,
    scope: '内部联系人',
  }).then((res) => {
    leaderOption.value = res.data.map((item: any) => ({
      ...item,
      account_name: `${item.real_name}（${item.account_name}）`,
    }))
  })
}

// 获取角色下拉框
const GetRoleListData = () => {
  return GetRoleList({ page: 1, pageSize: 500 }).then((res) => {
    roleOption.value = res.data.list.map((item: any) => ({
      label: item.role_name,
      value: `${item.id}`,
    }))
    if (roleOption.value.length > 0) {
      const item = roleOption.value.find((i: any) => i.label.includes('普通用户'))
      if (item) {
        formState.value.role_ids = item.value
      }
    }
  })
}

// 获取岗位下拉框
const GetJobList = () => {
  // 临时使用静态数据
  // jobOption.value = [
  //   { label: '经理', value: '1' },
  //   { label: '主管', value: '2' },
  //   { label: '专员', value: '3' },
  //   { label: '助理', value: '4' },
  // ]
  return Promise.resolve()
}

const setPathName = (data: any, treeData: any) => {
  data.forEach((item: any) => {
    treeData.forEach((item1: any) => {
      if (item.id == item1.id) {
        item.company_name = item1.company_name
      }
      if (item1.childs && item1.childs.length > 0) {
        setPathName(data, item1.childs)
      }
    })
  })
}

// 获取部门下拉框
const GetDepartmentList = (companyId: any) => {
  return GetCompanyTree({}).then((res) => {
    // 查找指定企业下的所有部门数据
    const findCompanyTree = (nodes: any[], targetId: string): any[] => {
      for (const node of nodes) {
        if (node.id === targetId && node.type === 1) {
          // 找到目标企业，返回其子节点（部门数据）
          return node.childs || []
        }
        if (node.childs && node.childs.length > 0) {
          const result = findCompanyTree(node.childs, targetId)
          if (result.length > 0) {
            return result
          }
        }
      }
      return []
    }

    const companyTree = findCompanyTree(res.data || [], companyId)
    setPathName(formState.value.department, companyTree)
    departmentTreeData.value = companyTree // 保存树形数据
  })
}

// 获取企业下拉框
const GetEnterpriseList = () => {
  return GetCompanyTree({}).then((res) => {
    // 递归查找所有企业节点（type=1）
    const filterCompanies = (nodes: any[]): any[] => {
      const companies: any[] = []

      const traverse = (nodeList: any[]) => {
        nodeList.forEach((node) => {
          if (node.type === 1) {
            companies.push({
              label: node.department_name || node.company_name,
              value: node.id,
              company_category_type: node.company_category_type || 0,
            })
          }
          if (node.childs && node.childs.length > 0) {
            traverse(node.childs)
          }
        })
      }

      traverse(nodes)
      return companies
    }

    enterpriseOption.value = filterCompanies(res.data || [])
    console.log('企业选项:', enterpriseOption.value)
  })
}

// 当企业为西月时才显示oa关联
const showOaDepartment = computed(() => {
  const current = enterpriseOption.value.find((item: any) => item.value === formState.value.company_id)
  return current
})

// 处理企业下拉框变化后，直接上级下拉框变化
const handleCompanyChange = (value: any) => {
  formState.value.leader_id = null
  formState.value.department_ids = []
  formState.value.department = []
  GetLeaderList(value)
  GetDepartmentList(value)
}

// 获取关联OA成员
const getOaMemberOption = async () => {
  // 临时使用空数据
  oaMemberOption.value = []
}

// 打开抽屉
const showDrawer = async (entId?: any) => {
  openDrawer.value = true

  // 先获取企业列表
  await GetRoleListData()
  await GetJobList()
  await GetEnterpriseList()
  await getOaMemberOption()

  // 如果传入了企业ID，设置默认选中的企业
  if (entId) {
    // formState.value.company_id = entId
    await handleCompanyChange(entId)
  }

  nextTick(() => {
    onDrop()
  })
}

// 打开部门选择对话框
const openDepartmentSelect = async () => {
  showSelectDepart.value = true
  await nextTick()
  const arr = formState.value.department.map((item: any) => {
    return {
      id: item.id,
      department_name: item.name || item.department_name,
      type: item.type,
      company_name: item.company_name,
    }
  })
  selectDepartRef.value?.showModal(arr, departmentTreeData.value)
}

// 处理部门选择变化
const handleDepartmentChange = async (departments: any[]) => {
  showSelectDepart.value = false
  formState.value.department = departments
  formState.value.department_ids = formState.value.department.map((item: any) => item.id)
  // 更新选项显示
  departmentOption.value = departments.map((item, index: any) => ({
    label: item.department_name,
    value: item.id,
    key: item.id,
    index,
  }))
}

// 处理部门标签删除
const handleDepartmentDeselect = (value: string) => {
  // 更新 department 数组
  formState.value.department = formState.value.department.filter((item: any) => item.id !== value)
  // 更新选项显示
  departmentOption.value = formState.value.department.map((item: any, index: any) => ({
    label: item.department_name,
    value: item.id,
    index,
    key: item.id,
  }))
}

// 关闭抽屉
const closeDrawer = () => {
  openDrawer.value = false
  submitLoading.value = false // 关闭时重置loading
  emit('close')
}

// 账号不合规弹窗
const showAccountError = () => {
  proxy.$confirm.show({
    title: '账号不合规',
    content: h('div', null, [
      h('div', null, '账号需遵循如下规则：'),
      h('div', null, '1、所属企业非"西月集团"的，账号不以"00"、"10"、"20"、"30"、"40"、"50"、"60"、"70"、"80"、"90"、"XPW"、"WM"开头'),
      h('div', null, '2、所属企业为"西月集团"，账号不以"XPW"、"WM"开头'),
    ]),
    okButtonProps: { style: { display: 'none' } },
  })
}

// 邀请弹窗
const showInvite = (data?: any) => {
  proxy.$confirm.show({
    title: '新增成功',
    content: h('div', null, [h('div', null, '新增成员成功，新增后，需邀请成员绑定后，才可启用账号，是否前往邀请？')]),
    confirmText: '前往邀请',
    onOk: () => {
      // accountInviteEl.value?.show(row)
      console.log('邀请功能待实现')
      console.log('邀请数据:', data)
    },
  })
}

// 找到当前企业
const getCurrentCompany = (id: any) => {
  return enterpriseOption.value.find((item: any) => item.value === id)
}

// 保存用户核心逻辑
const saveUserCore = async () => {
  // 新增：账号不能以XPW或WM开头（不区分大小写）
  const accountId = formState.value.account_id || ''
  const currentCompany = getCurrentCompany(formState.value.company_id)
  const isXiyue = currentCompany && currentCompany.company_category_type == 1
  if (isXiyue) {
    // 西月集团：不能以 XPW 或 WM 开头
    if (/^(XPW|WM)/i.test(accountId)) {
      showAccountError()
      return
    }
  } else {
    // 非西月集团：不能以 00/10/20...90/XPW/WM 开头
    if (/^(0[0-9]|XPW|WM)/i.test(accountId)) {
      showAccountError()
      return
    }
  }

  formRef.value
    ?.validate()
    .then(async () => {
      submitLoading.value = true
      // 构造保存的参数
      const bindCompanyParam = formState.value.department_ids.map((item: any, index: any) => {
        return {
          company_id: item,
          is_main: index == 0 ? '主要' : '次要',
        }
      })

      const params = {
        account_type: '企业',
        user_id: formState.value.user_id || '',
        user_name: formState.value.user_name, // 用户名
        account_name: formState.value.account_name, // 别名
        real_name: formState.value.real_name, // 真实姓名
        job_id: formState.value.job_id || '', // 工号
        company_id: formState.value.company_id, // 所属企业
        bindCompanyParam,
        status: '启用',
        position_id: formState.value.position_id || '', // 所属岗位
        leader_id: formState.value.leader_id || '', // 直接上级
        role_ids: formState.value.role_ids ? [parseInt(formState.value.role_ids)] : [], // 系统角色
        scope: '内部联系人',
        phone: formState.value.phone || '',
        email: formState.value.email || '',
      }
      AddUserAccount(params)
        .then(async (res) => {
          message.success('保存成功')
          emit('query')
          await showInvite(res.data)
          closeDrawer()
          submitLoading.value = false
        })
        .catch(() => {
          submitLoading.value = false
        })
    })
    .catch((error: any) => {
      submitLoading.value = false
      // 获取第一个错误信息
      if (error.errorFields) {
        const firstError = error.errorFields[0]
        const isXiyue = currentCompany && currentCompany.company_category_type == 1
        switch (firstError.name[0]) {
          case 'user_name':
            message.error('请完善用户名信息')
            break
          case 'account_name':
            message.error('请完善别名信息')
            break
          case 'real_name':
            message.error('请完善真实姓名信息')
            break
          case 'job_id':
            if (isXiyue) {
              message.error('请完善工号信息')
            }
            break
          case 'company_id':
            message.error('请完善所属企业信息')
            break
          case 'role_ids':
            message.error('请选择系统角色')
            break
          default:
            message.error(firstError.errors[0])
        }
      }
    })
}

// 添加防抖的保存用户函数
const handleSave = createLoadingDebounce(saveUserCore, submitLoading, 1000)

// 抽屉状态改变后的回调
const afterOpenChange = (status: boolean) => {
  if (!status) {
    formRef.value?.resetFields()
    // 清空数据
    Object.assign(formState.value, {
      user_name: '',
      account_name: '',
      real_name: '',
      job_id: '',
      company_id: '',
      position_id: '',
      leader_id: '',
      role_ids: '',
      department: [],
      department_ids: [],
      phone: '',
      email: '',
    })
  }
}

const onDrop = () => {
  nextTick(() => {
    // 确保 DOM 已渲染
    const el: HTMLElement | null = document.querySelector('.ant-select-selection-overflow')

    if (!el) return
    new Sortable(el, {
      animation: 300,
      handle: '.dragTag', // 拖拽手柄
      delay: 10, // 拖拽延迟
      forceFallback: true, // 强制使用原生拖拽
      onEnd: (item) => {
        const { oldIndex, newIndex } = item

        const currRow = formState.value.department_ids.splice(oldIndex, 1)[0]
        const arr = cloneDeep(formState.value.department_ids)
        arr.splice(newIndex, 0, currRow)

        const currRow1 = formState.value.department.splice(oldIndex, 1)[0]
        const arr1 = cloneDeep(formState.value.department)
        arr1.splice(newIndex, 0, currRow1)

        nextTick(() => {
          // 确保 DOM 同步更新
          formState.value.department_ids = arr
          formState.value.department = arr1
          departmentOption.value = formState.value.department.map((item: any, index: any) => ({
            label: item.name || item.department_name,
            value: item.id,
            index,
          }))
        })
      },
    })
  })
}

onMounted(() => {})

defineExpose({
  show: showDrawer,
})
</script>

<style lang="scss" scoped>
:deep(.ant-form) {
  .ant-form-item {
    margin-bottom: 16px;
  }
}
</style>
