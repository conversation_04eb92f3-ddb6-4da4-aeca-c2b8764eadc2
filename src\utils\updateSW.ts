import { registerSW } from 'virtual:pwa-register'

const intervalMS = 2 * 60 * 1000

const updateSW = registerSW({
  immediate: true,
  onRegisteredSW(swUrl, r) {
    r &&
      setInterval(async () => {
        if (r.installing || !navigator) return
        if ('connection' in navigator && !navigator.onLine) return
        const resp = await fetch(swUrl, {
          cache: 'no-store',
          headers: {
            cache: 'no-store',
            'cache-control': 'no-cache',
          },
        })
        console.log('resp', resp)

        if (resp?.status === 200) {
          await r.update()
        }
      }, intervalMS)
  },
})

export { updateSW }
