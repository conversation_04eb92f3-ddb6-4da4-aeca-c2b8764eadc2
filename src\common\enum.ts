/* eslint-disable no-unused-vars */

/** 页面类型 */
export enum PageType {
  /** 用户管理 */
  USER_MANAGE = 20000,

  /** 通知管理 */
  NOTICE_MANAGE = 60000,

  // 店铺管理
  // SHOP_MANAGE = 71000,

  // 角色管理
  ROLE_MANAGE = 31000,

  // 通用设置
  GENERAL_SETTINGS = 42000,

  // 资源管理 - 平台大类
  PLATFORM_LARGE_CATEGORIES = 81001,

  // 商品管理 - 品牌库
  BRAND_MANAGEMENT = 71000,

  // 资源管理 - 平台小类
  PLATFORM_SUB_CATEGORIES = 82001,

  /* 国家 */
  COUNTRY = 83001,

  // 资源管理 - 紫鸟主账号
  PURPLE_BIRD_MAIN_ACCOUNT = 86100,

  // 资源管理 - 紫鸟子账号
  PURPLE_BIRD_STAFF_ACCOUNT = 86300,

  // 资源管理 - 紫鸟设备
  PURPLE_BIRD_DEVICE = 86200,

  // 资源管理 - 手机号管理
  MOBILE_NUMBER = 84001,

  // 资源管理 - 邮箱管理
  EMAIL_MANAGEMENT = 85001,

  // 资源管理 - 执照管理
  LICENSE_MANAGEMENT = 87001,

  // // 变更负责人流程
  // CHANGE_OWNER_FLOW = 91000,

  // 法人管理
  LegalPerson_GetList = 89001,

  // 财务管理
  FinanceAccount_GetList = 88001,

  // 店铺注册申请
  SHOP_REGIST_FLOW = 92000,

  // 供应商入驻审核
  SUPPLIER_SETTLEMENT_APPROVAL = 93000,

  // 供应商信息
  SUPPLIER_INFO = 94000,

  // 国家/地区
  Country_Region = 51000,

  // 货币管理
  Currency = 52000,

  // 汇率管理
  ExchangeRate = 53000,

  // 语言管理
  LanguageTB = 54000,

  // 商品属性管理
  ATTR_MANAGEMENT = 72000,

  // 类目模板管理
  CategoryTemplate = 74000,

  // 类目管理
  CATEGORY_MANAGEMENT = 73000,

  // 供应商商品库
  SUPPLIER_PRODUCT_LIBRARY = 91000,

  // 资质/证书管理
  QUALIFICATION_CERTIFICATE = 80000,
  // 商品标签管理
  ProductLabel = 75000,

  // 商品审核
  PRODUCT_AUDIT = 95000,

  // 库存管理
  PRODUCT_STOCK = 101000,
}
