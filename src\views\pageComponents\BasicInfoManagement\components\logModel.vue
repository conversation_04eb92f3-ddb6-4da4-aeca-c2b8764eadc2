<template>
  <a-drawer v-if="open" v-model:open="open" width="40%" class="custom-class" root-class-name="drawerrootclass" :bodyStyle="{ padding: '0px' }" :title="title" placement="right" @close="closeDrawer">
    <div class="w-full overflow-y-auto h-full flex justify-start items-start p-20px">
      <a-timeline>
        <a-timeline-item v-for="(item, index) in logdata" :key="index">
          <p class="font-bold">{{ (item as any).time }}</p>
          <p>
            <span class="font-bold">{{ (item as any).user }}</span>
            {{ (item as any).title }}
          </p>
          <p>{{ (item as any).subtitle }}</p>
        </a-timeline-item>
      </a-timeline>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
const open = ref(false)
const title = ref('')
const closeDrawer = () => {}
const logdata = ref([])
const init = (logdatatext, titletext) => {
  open.value = true
  logdata.value = logdatatext.reverse()
  title.value = titletext
}
defineExpose({ init })
</script>
