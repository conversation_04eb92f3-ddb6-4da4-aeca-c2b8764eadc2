<template>
  <a-cascader
    :multiple="item.multiple"
    :show-checked-strategy="Cascader.SHOW_CHILD"
    v-model:value="item.value"
    :placeholder="item.label"
    :field-names="item.fieldNames ? item.fieldNames : { label: 'label', value: 'value', children: 'children' }"
    :max-tag-count="0"
    class="w-140px"
    showArrow
    v-bind="item"
  />
</template>

<script setup lang="ts">
import { Cascader } from 'ant-design-vue'
import { FormItemType } from '../type'

defineEmits<{
  (e: 'serach'): void
}>()

const item = defineModel<FormItemType<'calendar'>>('item', { required: true })
</script>

<style scoped></style>
