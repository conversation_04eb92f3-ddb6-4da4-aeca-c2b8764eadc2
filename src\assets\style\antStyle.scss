/**
* 调整ant组件库的样式
*/

// 调整按钮间距
.ant-btn:not([class*='ant-dropdown-trigger']):not([class*='ant-btn-icon-only']):not([class*='page-button']) {
  padding: 4px 12px;
  line-height: 1;
}
.ant-dropdown-trigger {
  padding-inline: 12px;
}
.ant-pagination-item-active {
    .page-button {
        color: #1890FF !important;
    }
}
// 分页按钮非激活状态
.ant-pagination-item:not([class*='ant-pagination-item-active']) {
  border: 1px solid #dcdee0 !important;
}
// 分页按钮
.ant-pagination-item {
  margin-right: 8px !important;
  padding-inline: 8px !important;
}

// 分页切换上下页禁用
.page-prev[disabled='true'] {
  cursor: not-allowed !important;
}
.page-next[disabled='true'] {
  cursor: not-allowed !important;
}

.ant-menu-dark .ant-menu-item-selected {
  color: #1890ff !important;
  .ant-menu-item-icon {
    color: #1890ff !important;
  }
}
// 调整徽标小点
.ant-badge-dot {
    box-shadow: none !important;
}
.ant-menu-dark .ant-menu-item-selected {
  background-color: rgba(64, 158, 255, 0.15) !important;
}
.ant-select:not(.ant-select-customize-input) .ant-select-selector:not(:hover):not(:active) {
  border: 1px solid #DCDEE0 !important;
}
.ant-input-affix-wrapper:not(:hover):not(:active) {
  border: 1px solid #DCDEE0 !important;
}
.ant-picker {
  border: 1px solid #DCDEE0 !important;
}

.ant-dropdown-menu-item {
    &:hover {
      background: #F1F8FF !important;
    }
    &:active {
      .ant-dropdown-menu-title-content {
        .btn:not([disabled]) {
          color: #1890FF;
        }
      }
    }
    .ant-dropdown-menu-title-content {
      .btn:not([disabled]) {
        color: #333333;
      }
    }
  }
  .filterTabs {
    .ant-tabs-nav {
        margin-bottom: 12px !important;
        .ant-tabs-nav-wrap {
            line-height: 0 !important;
            
        }
    }
  }
.formBox {
  .ant-btn >.anticon+span {
    margin-left: 4px;
  }
}

.ant-select-arrow {
  color: #c0c0c0 !important;
}
.ant-picker-separator {
  color: #c0c0c0 !important;
}
.ant-picker-suffix {
  color: #c0c0c0 !important;
}

.search-btn-box {
  .ant-btn-default:not(:active):not(:hover) {
    .anticon-setting {
      color: #333;
    }
  }
  .ant-btn-default:not(:active):not(:hover) {
    &:active {
      border-color: #1890FF !important;
    }
    &:hover {
      color: #1890FF !important;
    }
  }

  // 非:active 下
  .ant-btn-default:not(:active):not(:hover) {
    color: #333 !important;
    border-color: #d9d9d9 !important;
  }
  .ant-btn-default {
    &:active {
      border-color: #1890FF !important;
    }
    &:hover {
      color: #1890FF !important;
    }
  }
}

.formBox {
  .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    padding: 0 8px;
  }
  .ant-input-affix-wrapper {
    padding: 3px 8px !important;
    .ant-input {
      color: #333 !important;
    }
  }
}
.ant-menu-submenu-selected {
  .iconfont  {
    color: #fff !important;
  }
}
.ant-btn {
  box-shadow: none !important;
}

.ant-tag:not([class*='quickSearch']) {
  font-size: 12px !important;
  height: 20px !important;
  border-radius: 2px !important;
  line-height: 20px !important;
  padding-inline: 4px !important;
  color: #0F1521 !important;
  background-color: #EDF2FA;
  border: none;
}

.ant-menu-dark {
  .ant-menu-item-selected {
    border-left: 3px solid #1890FF !important;
  }
}
.ant-select-item-option-selected {
  font-weight: normal !important;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), #1890FF !important;
  .ant-select-item-option-content {
    color: #1890FF !important;
  }
}

.ant-select-item {
  color: #333 !important;
}
.ant-select-single:not([class*='ant-select-disabled']) {
  .ant-select-selection-item {
    color: #333 !important;
  }
}
.ant-input-group {
  .ant-btn-icon-only:not(:active):not(:hover) {
    // color: #333 !important;
    border-color: #dcdee0 !important;
  }
}
.ant-select-item {
  transition: none !important;
}
.ant-select-dropdown .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: #F1F8FF;
}
