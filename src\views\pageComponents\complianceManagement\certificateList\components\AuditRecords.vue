<template>
  <a-drawer title="审核记录" :width="672" v-model:open="visible" @close="handleClose">
    <div class="log-container" ref="logContainerRef" @scroll="handleScroll">
      <a-timeline v-if="logList?.length > 0">
        <a-timeline-item v-for="(item, index) in logList" :key="index">
          <p class="font-bold">{{ item.op_at }}</p>
          <div class="bg-gray-100 pl-20px pt-10px pb-2px">
            <p>
              <span class="text-13px">{{ item.user_name }}</span>
              <span class="user-dept">{{ item.user_department ? `[${item.user_department}]` : '' }}</span>
            </p>
            <!-- <p class="operation-info">{{ item.op_name || item.op_type }}</p> -->

            <!-- 显示修改的字段 -->
            <div v-if="item.edits && item.edits.length > 0" class="log-details">
              <div v-for="(edit, editIndex) in item.edits" :key="editIndex" class="log-detail-item">
                <!-- <span>{{ cleanFieldName(edit.name) }}：</span> -->
                <!-- <span class="old-value" v-if="edit.old_val">{{ cleanFieldValue(edit.old_val) }}</span> -->
                <!-- <span class="arrow" v-if="edit.old_val">→</span> -->
                <span class="new-value">{{ cleanFieldValue(edit.new_val) }}</span>
              </div>
            </div>

            <!-- 显示新增的字段 -->
            <div v-if="item.adds && item.adds.length > 0" class="log-details">
              <!-- <div class="operation-type">新增：</div> -->
              <div v-for="(add, addIndex) in item.adds" :key="addIndex" class="log-detail-item">
                <!-- <span>{{ cleanFieldName(add.name) }}：</span> -->
                <span class="new-value">{{ cleanFieldValue(add.new_val) }}</span>
              </div>
            </div>

            <!-- 显示删除的字段 -->
            <div v-if="item.deletes && item.deletes.length > 0" class="log-details">
              <!-- <div class="operation-type">删除：</div> -->
              <div v-for="(del, delIndex) in item.deletes" :key="delIndex" class="log-detail-item">
                <!-- <span>{{ cleanFieldName(del.name) }}：</span> -->
                <span class="old-value">{{ cleanFieldValue(del.new_val) }}</span>
              </div>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>

      <div v-if="logList?.length === 0 && !loading" class="empty-log">暂无审核记录</div>
      <div v-if="loading" class="loading-text">加载中...</div>
      <div v-if="!hasMore && logList.length > 0" class="no-more-text">没有更多数据了</div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { GetOpLogInfos } from '@/servers/Certificate'

const visible = ref(false)
const logContainerRef = ref<HTMLElement | null>(null)
const loading = ref(false)
const hasMore = ref(true)

const logList = ref<any[]>([])
const page = ref(1)
const pageSize = ref(20)

// // 清理字段名称，去除引号
// const cleanFieldName = (name: string) => {
//   if (!name) return ''
//   return name.replace(/"/g, '')
// }

// 清理字段值，去除引号
const cleanFieldValue = (value: string) => {
  if (!value) return ''
  return value.replace(/"/g, '')
}

const handleClose = () => {
  logList.value = []
  visible.value = false
  page.value = 1
  hasMore.value = true
}

const loadData = async (id: string) => {
  if (loading.value || !hasMore.value) return
  loading.value = true
  try {
    const res: any = await GetOpLogInfos({
      page: 3,
      // page:3 page.value,
      sortField: '',
      sortType: '',
      id,
    })
    if (res.code === 0 && res.success) {
      const list = res.data.reverse() || []
      if (page.value === 1) {
        logList.value = list
      } else {
        logList.value = [...logList.value, ...list]
      }
      hasMore.value = list.length === pageSize.value
      if (hasMore.value) {
        page.value++
      }
    }
  } catch (error) {
    console.error('加载审核记录失败:', error)
  } finally {
    loading.value = false
  }
}

const handleScroll = () => {
  if (!logContainerRef.value || !currentId.value) return
  const { scrollTop, scrollHeight, clientHeight } = logContainerRef.value
  if (scrollHeight - scrollTop - clientHeight < 50) {
    loadData(currentId.value)
  }
}

const currentId = ref<string | null>(null)

const handleOpen = async (id: string | null) => {
  visible.value = true
  currentId.value = id
  if (!id) return
  page.value = 1
  hasMore.value = true
  loadData(id)
}

defineExpose({
  open: handleOpen,
})
</script>

<style scoped lang="scss">
.log-container {
  padding: 20px;
  overflow-y: auto;
  width: 100%;
  height: calc(100% - 60px);
}

.loading-text,
.no-more-text {
  text-align: center;
  padding: 10px 0;
  color: #999;
}

.font-bold {
  font-weight: bold;
  margin-bottom: 8px;
}

.user-dept {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.operation-info {
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
}

.log-details {
  margin-bottom: 12px;

  //   margin-left: 8px;
}

.operation-type {
  margin-bottom: 4px;
  font-weight: 500;
  color: #262626;
}

.log-detail-item {
  //   padding-left: 8px;
  margin-bottom: 4px;
  font-size: 13px;
}

.old-value {
  color: #999;
  text-decoration: line-through;
}

.arrow {
  margin: 0 8px;
  color: #409eff;
}

.new-value {
  font-weight: 500;
  color: #262626;
}

.empty-log {
  padding: 20px;
  color: #999;
  text-align: center;
}
</style>
