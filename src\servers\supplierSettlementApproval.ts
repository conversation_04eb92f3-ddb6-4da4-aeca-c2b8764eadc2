// 供应商入驻审核
import { request } from './request'

// 获取供应商入驻审核列表
export const GetSupplierSettlementApprovalList = (data) => {
  return request({ url: '/api/Supplier/GetSupplierList', data }, 'POST')
}
// 枚举
export const GetEnumList = () => {
  return request({ url: '/api/Common/GetDropdownItems' }, 'GET')
}
// 供应商信息
export const GetSupplierInfo = (data) => {
  return request({ url: '/api/Supplier/GetSupplierById', data }, 'GET')
}
// 主营类目下拉框
export const GetMainCategoryList = (data) => {
  return request({ url: '/api/Common/GetProductCategoryList', data }, 'POST')
}
// 供应商审核
export const SupplierAudit = (data) => {
  return request({ url: '/api/Supplier/Audit', data }, 'POST')
}
// 获取联系人真实电话号码
export const GetContactPhone = (data) => {
  return request({ url: '/api/Supplier/GetMobilephoneNumber', data }, 'GET')
}
// 获取真实银行卡号
export const GetBankCard = (data) => {
  return request({ url: '/api/Supplier/GetCollectionCardNumber', data }, 'GET')
}
// 列表红点数
export const GetRedPoint = () => {
  return request({ url: '/api/Supplier/GetLabelStatusCount' }, 'GET')
}
// 禁用/启用供应商
export const ToggleSupplierStatus = (data) => {
  return request({ url: '/api/Supplier/ToggleSupplierStatus', data }, 'GET')
}

// 编辑供应商跟进信息
export const UpdateSupplierFollow = (data) => {
  return request({ url: '/api/Supplier/UpdateSupplierFollow', data }, 'POST')
}

// 推送供应商信息到SRM
export const PushSupplierToSRM = (data) => {
  return request({ url: '/api/Supplier/PushSupplierToSRM', data }, 'POST')
}
