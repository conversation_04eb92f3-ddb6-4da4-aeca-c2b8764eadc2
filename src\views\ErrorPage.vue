<template>
  <div class="errorMainBox">
    <div class="errorBox">
      <div class="titleBox">
        <img src="../assets/image/logo-o.png" alt="" />
        <div>
          <div>访问受限</div>
          <div>SRS供应商管理后台</div>
        </div>
      </div>

      <!-- 错误状态显示 -->
      <div class="error-container">
        <!-- 图标区域 -->
        <div class="error-icon-section">
          <div class="lock-icon">
            <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
              <!-- 锁环 -->
              <path d="M25 30V23C25 15.268 31.268 9 39 9C46.732 9 53 15.268 53 23V30" stroke="#409EFF" stroke-width="3" stroke-linecap="round" />
              <!-- 锁身 -->
              <rect x="20" y="30" width="40" height="30" rx="6" fill="#409EFF" stroke="#36A3F7" stroke-width="1.5" />
              <!-- 锁孔 -->
              <circle cx="40" cy="45" r="4" fill="white" />
              <rect x="38" y="45" width="4" height="8" fill="white" />
            </svg>
          </div>
        </div>

        <!-- 错误信息区域 -->
        <div class="error-message-section">
          <h2 class="error-title">{{ errorTitle }}</h2>
          <p class="error-description">{{ errorDescription }}</p>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="error-actions">
        <a-button type="primary" @click="handleRetry" :loading="retrying">退出系统</a-button>
      </div>
    </div>

    <div class="beian">
      <span>© 广东云跨易网络科技有限公司 版权所有</span>
      <div class="line"></div>
      <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备2024317142号-1</a>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { LogoutV4, LoginRedirect } from '@/servers/UmcAuth'
import { beforLogout } from '@/utils'

interface Props {
  errorCode?: number
  errorMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  errorCode: 1000,
  errorMessage: '登录失败，请重试',
})

const retrying = ref(false)

// 根据错误代码显示不同的标题和描述
const errorTitle = computed(() => {
  switch (props.errorCode) {
    case 1000:
      return '对不起，您暂时没有权限！'
    case 3000:
      return '对不起，您暂时没有权限！'
    default:
      return '登录失败'
  }
})

const errorDescription = computed(() => {
  switch (props.errorCode) {
    case 1000:
      return '如需继续使用，请联系系统管理员'
    case 3000:
      return '如需继续使用，请联系系统管理员'
    default:
      return props.errorMessage || '请联系系统管理员'
  }
})

// 动态获取当前页面的IP和端口
const getCurrentHostInfo = () => {
  const protocol = window.location.protocol // http: 或 https:
  const hostname = window.location.hostname // IP地址或域名
  const port = window.location.port // 端口号

  // 构建完整的主机信息
  let hostInfo = `${protocol}//${hostname}`
  if (port) {
    hostInfo += `:${port}`
  }

  return hostInfo
}

// 重新登录
const handleRetry = () => {
  retrying.value = true
  tuichu()
}

const tuichu = async () => {
  try {
    // 优先从sessionStorage获取LoginToken（这是在handleLoginError中保存的）
    let LoginToken = sessionStorage.getItem('logoutLoginToken') || ''

    // 如果sessionStorage中没有，再从localStorage获取
    if (!LoginToken) {
      const userData = localStorage.getItem('userData')
      if (userData) {
        try {
          const userDataObj = JSON.parse(userData)
          LoginToken = userDataObj.login_token || ''
          if (LoginToken) {
            sessionStorage.setItem('logoutLoginToken', LoginToken)
          }
        } catch (e) {
          console.warn('解析userData失败:', e)
        }
      }
    }

    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('ErrorPage退出登录 - LoginToken:', LoginToken ? `${LoginToken.substring(0, 20)}...` : '无')
      console.log('LoginToken来源:', sessionStorage.getItem('logoutLoginToken') ? 'sessionStorage' : 'localStorage')
    }

    // 直接调用LogoutV4接口退出登录
    const logoutResult = await LogoutV4(LoginToken)

    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('=== LogoutV4接口响应 ===')
      console.log('完整响应对象:', logoutResult)
      console.log('响应成功状态:', logoutResult?.success)
    }

    // 无论接口调用成功与否，都清除本地数据
    beforLogout()

    if (import.meta.env.VITE_APP_ENV === 'development') {
      if (logoutResult && logoutResult.success) {
        console.log('=== 退出登录成功 ===')
      } else {
        console.log('=== 退出登录接口失败，但仍清除本地数据 ===')
      }
    }

    // 获取登录URL并跳转
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('=== 获取登录URL ===')
    }

    let ipParam: string | undefined
    if (import.meta.env.VITE_APP_ENV === 'development') {
      // 开发环境：传递当前页面的IP和端口
      ipParam = getCurrentHostInfo()
      console.log('开发环境，传递IP参数:', ipParam)
    }

    const loginResponse = await LoginRedirect(ipParam)
    if (loginResponse.success && loginResponse.data) {
      if (import.meta.env.VITE_APP_ENV === 'development') {
        console.log('=== 获取到登录URL，直接跳转 ===')
        console.log('登录URL:', loginResponse.data)
      }
      window.location.href = loginResponse.data
    } else {
      if (import.meta.env.VITE_APP_ENV === 'development') {
        console.log('=== 获取登录URL失败 ===')
      }
      // 获取登录URL失败时，不做任何操作，让用户手动刷新页面
    }
  } catch (error) {
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.error('退出登录过程中发生错误:', error)
    }

    // 即使出错也要清除本地数据
    beforLogout()

    // 尝试获取登录URL
    try {
      let ipParam: string | undefined
      if (import.meta.env.VITE_APP_ENV === 'development') {
        ipParam = getCurrentHostInfo()
      }

      const loginResponse = await LoginRedirect(ipParam)
      if (loginResponse.success && loginResponse.data) {
        window.location.href = loginResponse.data
      }
    } catch (loginError) {
      if (import.meta.env.VITE_APP_ENV === 'development') {
        console.error('获取登录URL也失败:', loginError)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.errorMainBox {
  position: relative;
  width: 100%;
  min-width: 1180px;
  height: 100vh;
  background-color: #fff;
  background-image: url('../assets/image/login-bg.webp');
  background-size: cover;

  .errorBox {
    position: absolute;
    top: 0;
    right: 360px;
    bottom: 0;
    width: 480px;
    height: 490px;
    padding: 60px;
    margin: auto;
    background: rgb(255 255 255 / 90%);
    border-radius: 10px;
    box-shadow: 0 0 20px 0 rgb(0 0 0 / 10%);

    .titleBox {
      display: flex;
      gap: 16px;
      align-items: center;
      margin-bottom: 40px;
      font-size: 18px;
      font-weight: 400;
      line-height: 30px;
      color: #1a1a1a;

      img {
        width: 60px;
        height: 60px;
      }
    }

    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30px;
      text-align: center;
    }
  }

  .beian {
    position: absolute;
    bottom: 30px;
    left: 50%;
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 12px;
    color: #999;
    transform: translateX(-50%);

    .line {
      width: 1px;
      height: 12px;
      background-color: #999;
    }

    a {
      color: #999;
      text-decoration: none;

      &:hover {
        color: #666;
      }
    }
  }
}

.error-icon-section {
  margin-bottom: 32px;

  .lock-icon {
    position: relative;
    display: inline-block;

    &::before {
      position: absolute;
      inset: -10px;
      z-index: -1;
      content: '';
      background: radial-gradient(circle, rgb(64 158 255 / 10%) 0%, transparent 70%);
      border-radius: 50%;
    }
  }
}

.error-message-section {
  margin-bottom: 40px;

  .error-title {
    margin: 0 0 12px;
    font-size: 20px;
    font-weight: 500;
    line-height: 1.4;
    color: #2c3e50;
  }

  .error-description {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
    color: #7f8c8d;
  }
}

.error-actions {
  display: flex;
  align-items: center;
  justify-content: center;

  :deep(.ant-btn) {
    height: 44px;
    padding: 0 24px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s;

    &.ant-btn-primary {
      color: #fff;
      background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
      border: none;
      box-shadow: 0 2px 8px rgb(64 158 255 / 30%);

      &:hover {
        background: linear-gradient(135deg, #36a3f7 0%, #409eff 100%);
        box-shadow: 0 4px 12px rgb(64 158 255 / 40%);
        transform: translateY(-1px);
      }
    }

    &:not(.ant-btn-primary) {
      color: #7f8c8d;
      border-color: #ddd;

      &:hover {
        color: #409eff;
        border-color: #409eff;
      }
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .errorMainBox {
    min-width: auto;
    padding: 20px;

    .errorBox {
      position: relative;
      right: auto;
      width: 100%;
      max-width: 400px;
      height: auto;
      margin: 50px auto;
      padding: 40px 30px;
    }

    .beian {
      position: relative;
      bottom: auto;
      left: auto;
      margin-top: 20px;
      transform: none;
    }
  }

  .error-actions {
    flex-direction: column;
    gap: 12px;

    :deep(.ant-btn) {
      width: 100%;
      margin-left: 0 !important;
    }
  }
}
</style>
