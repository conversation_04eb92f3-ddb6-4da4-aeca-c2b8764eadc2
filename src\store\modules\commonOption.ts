import { defineStore } from 'pinia'
import { GetCommonOption, GetPlmEnum } from '@/servers/Common'

interface CommonOptionState {
  commonOption: Record<number, any>
  plmEnum: Record<string, any>
}

/**
 * 公共选项存储
 */
const useCommonOptionStore = defineStore('commonOption', {
  state: (): CommonOptionState => ({
    commonOption: {},
    plmEnum: {},
  }),
  actions: {
    // 设置公共选项
    setCommonOption(key: string, option: any) {
      this.commonOption[key] = option
    },
    // 获取公共选项
    async getCommonOption(keys: number[]) {
      const failKeys = keys.filter((key) => !this.commonOption[key])
      if (failKeys.length > 0) {
        const { data } = await GetCommonOption({ types: failKeys })
        Object.entries(data.data).forEach(([key, value]) => {
          this.commonOption[Number(key)] = value
        })
      }
      return keys.map((key) => this.commonOption[key]).filter(Boolean)
    },
    // 更新公共选项
    async updateCommonOption(key: number[]) {
      const { data } = await GetCommonOption(key)
      Object.entries(data.data).forEach(([key, value]) => {
        this.commonOption[Number(key)] = value
      })
    },
    // 获取PLM枚举
    async getPlmEnum() {
      if (Object.keys(this.plmEnum).length === 0) {
        const { data } = await GetPlmEnum()
        this.plmEnum = data
      }
      return this.plmEnum
    },
  },
})

export default useCommonOptionStore
