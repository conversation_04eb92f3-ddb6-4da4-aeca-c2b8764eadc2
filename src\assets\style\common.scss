@use './mixin.scss' as *;

// 超出行数省略表示
@for $i from 1 through 3 {
  .line#{$i} {
    overflow: hidden;
    text-overflow: ellipsis;

    @if $i == 1 {
      white-space: nowrap;
    } @else {
      display: -webkit-box;
      -webkit-line-clamp: $i;
      -webkit-box-orient: vertical;
    }
  }
}

.flexCenter {
  @include flex;
}

.flexLeft {
  @include flex(flex-start);
}

.flexRight {
  @include flex(flex-end);
}

// 主轴等比间距
.flexBw {
  @include flex(space-between, center, row);
}

.flexCol {
  display: flex;
  flex-direction: column;
}

body,
html {
  overflow: hidden;
  font-family: var(--vxe-ui-font-family);
  font-size: 12px;
  line-height: initial;
  color: #fff;
}

// #app {
//   min-width: 1400PX;
//   overflow-x: scroll;
//   overflow-y: hidden;
// }

.bold {
  font-weight: bold;
}

.subTitle {
  position: relative;
  width: 480px;
  height: 30px;
  padding-left: 58px;
  margin-bottom: 20px;
  font-family: var(--vxe-ui-font-family);
  font-size: 16px;
  font-weight: bold;
  line-height: 30px;
  color: #eafbff;
  transform-style: preserve-3d;

  // text-shadow: 0px -2px 2px rgba(77, 196, 255, 0.5), 0px 1px 2px #FFFFFF;
  @include bgSize('http://reading.oss.iyougu.com/uploads/cardBgImg/8b599acb132f4e7386311258352a06ea.png', 44px 30px, left top);

  &::after {
    position: absolute;
    top: 0;
    left: 44px;
    width: 100%;
    height: 100%;
    content: '';
    background: linear-gradient(90deg, #3998ff 0%, rgb(94 194 255 / 0%) 100%);
    box-shadow: 0 3px 7px 1px rgb(7 22 77 / 50%);
    opacity: 0.2;
    transform: translateZ(-1px);
  }
}

.hWrap {
  width: 100%;
  height: 330px;
  background:
    linear-gradient(#5ee6ff, #5ee6ff) left top,
    linear-gradient(#5ee6ff, #5ee6ff) left top,
    linear-gradient(#5ee6ff, #5ee6ff) right top,
    linear-gradient(#5ee6ff, #5ee6ff) right top,
    linear-gradient(#5ee6ff, #5ee6ff) left bottom,
    linear-gradient(#5ee6ff, #5ee6ff) left bottom,
    linear-gradient(#5ee6ff, #5ee6ff) right bottom,
    linear-gradient(#5ee6ff, #5ee6ff) right bottom;
  background-color: rgb(50 85 211 / 10%);
  background-repeat: no-repeat;
  background-size:
    1px 10px,
    10px 1px;
  backdrop-filter: blur(4px);
  border: 1px solid;
  border-image: linear-gradient(270deg, rgb(255 255 255 / 0%), rgb(94 230 255 / 20%), rgb(255 255 255 / 0%)) 1 1;
  box-shadow: inset 0 0 13px 2px rgb(4 8 43 / 50%);
}

.tableConfigBtn {
  position: relative;
  right: -50px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  color: rgb(0 0 0 / 85%);
  cursor: pointer;
  background-color: white;
  border: 1px solid #dcdcdc;
  border-radius: 50%;
  transition: color 0.3s;

  &:hover {
    color: #3998ff;
  }
}

.commenceBox {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: end;
  width: 100%;
  height: 40px;
  overflow: hidden;

  .lineBox {
    display: flex;
    align-items: center;
    width: 100%;

    .lin1 {
      flex: 1;
      height: 2px;
      background: linear-gradient(-90deg, #3d7fff, #f7f8fa);
      border-top-left-radius: 50%;
      border-bottom-left-radius: 50%;
    }

    .centerBtn {
      display: flex;
      justify-content: center;
      width: 50px;
      margin: 0 20px;
      border-radius: 10px;
    }

    .lin2 {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg, #3d7fff, #f7f8fa);
      border-top-right-radius: 50%;
      border-bottom-right-radius: 50%;
    }
  }

  .btn {
    margin-left: 20px;
  }
}

.main {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    margin: 8px 0;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #000;
    }
  }
}

.btnBox {
  display: flex;
  align-items: center;
  gap: 10px;
}

.a-text-wrap {
  overflow-wrap: break-word;
  line-break: anywhere;
  word-break: break-all;
  color: #333;
}

.drawer-title {
  height: 36px;
  padding-left: 20px;
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 36px;
  color: #111;
  background-color: #eef1f7;
  position: relative;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    height: 12px;
    width: 2px;
    background-color: #1890ff;
    top: 50%;
    transform: translateY(-50%);
  }
}

.ant-timeline {
  .ant-timeline-item {
    padding-bottom: 12px;
  }

  .ant-timeline-item-head {
    position: relative;
    background-color: #409eff;
    border: 1px solid #fff;
    border-color: #fff;

    &::before {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 14px;
      height: 14px;
      content: '';
      border: 1px solid #409eff;
      border-radius: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .ant-timeline-item-tail {
    inset-block-start: 11px;
    height: calc(100% - 12px);
    border-color: #409eff;
    border-inline-start-width: 1px;
  }

  .ant-timeline-item-last {
    position: relative;

    &::before {
      position: absolute;
      top: 12px;
      left: 4px;
      width: 1px;
      height: calc(100% - 30px);
      content: '';
      background-color: #409eff;
    }

    &::after {
      position: absolute;
      top: calc(100% - 22px);
      left: 1px;
      width: 7px;
      height: 7px;
      content: '';
      background-color: #409eff;
      border-radius: 90%;
    }
  }

  .ant-timeline-item-content {
    inset-block-start: -16px;

    .timeline-title {
      margin-bottom: 8px;
      color: #666;
    }

    .timeline-content {
      padding: 16px;
      color: #333;
      background-color: #f6f8fa;
    }
  }
}

.detailAllBox {
  .loadingIcon {
    font-size: 1.875rem;
    color: #1890ff;
  }

  display: flex;

  .head {
    padding: 1.875rem;
    margin-bottom: 1.25rem;
    background-color: rgb(255 244 233 / 100%);
  }

  .strong {
    font-weight: bold;
  }

  .grey {
    color: #00000080;
  }

  .gutter-row {
    margin-bottom: 0.9375rem;
  }

  .label {
    color: #888;
  }

  .ant-form,
  .log {
    flex: 1;
    overflow: hidden;
  }

  .ant-form {
    padding: 1.875rem;
  }

  .log {
    min-height: calc(100vh - 61px);
    padding-top: 30px;
    background-color: #eee;
  }
}

.filterTabs {
  margin-top: -12px;
  .ant-tabs-nav {
    margin-bottom: 12px !important;
    .ant-tabs-nav-wrap {
      line-height: 0 !important;
    }
    .ant-tabs-tab-active {
      color: #1890ff !important;
      .ant-badge {
        color: #1890ff !important;
      }
    }
  }
}


.base-form {
  .ant-form {
    .ant-form-item {
      margin-bottom: 32px;
      .ant-form-item-label {
        padding-bottom: 2px;
        label {
          color: #999;
        }
      }
      .ant-input,
      .ant-input-number-input {
        &:not([disabled]) {
          color: #333;
        }
      }
      .ant-form-item-control-input {
        min-height: 14px;
      }
    }
  }
}

