import { defineStore } from 'pinia'
// import { GetUserPreference, SetUserPreference } from '@/servers/User'

const useAppStore = defineStore('app', {
  state: () => ({
    orgaInfo: {},
    refreshTime: new Date().getTime(), // 刷新时间，当时间改变时即触发刷新
    lockRefresh: false,
    isOpenLog: 0, // 0不展开 1展开
  }),
  actions: {
    setOrgaInfo(info) {
      this.orgaInfo = info
    },
    setRefreshTime() {
      this.refreshTime = new Date().getTime()
    },

    setLockRefresh(bol) {
      this.lockRefresh = bol
    },
    getExteriorDormanName() {
      return new Promise((resolve) => {
        let timer = null
        if (this.exteriorDormanName) {
          resolve(this.exteriorDormanName)
        } else {
          timer = setInterval(() => {
            if (this.exteriorDormanName) {
              clearInterval(timer)
              timer = null
              resolve(this.exteriorDormanName)
            }
          }, 100)
        }
      })
    },
    changeLogOpenVisible(value) {
      this.isOpenLog = value
      // 使用localStorage替代服务器端的用户偏好设置
      localStorage.setItem('userPreference_isOpenLog', value.toString())
    },
    async getLogOpenVisible() {
      try {
        // 使用localStorage替代服务器端的用户偏好设置
        const savedValue = localStorage.getItem('userPreference_isOpenLog')
        this.isOpenLog = savedValue ? parseInt(savedValue) : 0
      } catch (error) {
        console.warn('获取用户偏好设置失败，使用默认值:', error)
        this.isOpenLog = 0
      }
    },
  },
})

export default useAppStore
