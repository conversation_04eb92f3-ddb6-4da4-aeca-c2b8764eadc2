<template>
  <div class="main">
    <!-- 过滤器 -->
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.NOTICE_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" />

    <BaseTable ref="tableRef" :isCheckbox="true" :pageType="PageType.NOTICE_MANAGE" :getList="getListFn" v-model:form="formArr" :auto-search="false">
      <template #left-btn>
        <a-button id="noticeIncrease" type="primary" @click="increase" :disabled="!btnPermission[61001]">新建通知</a-button>
      </template>
      <template #right-btn>
        <a-button id="noticeBatchSend" @click="beforeBatchSend" :disabled="!btnPermission[61002]">批量发布</a-button>
      </template>
      <template #no="{ row }">
        <span>{{ row.no || row.code || '--' }}</span>
      </template>
      <template #notice_status="{ row }">
        <span>{{ getStatusDisplay(row.notice_status || row.status) }}</span>
      </template>
      <template #scope="{ row }">
        <span>{{ row.scope || '--' }}</span>
      </template>
      <template #scheduled_publish_at="{ row }">
        <span>{{ row.scheduled_publish_at || row.plan_publish_time || '--' }}</span>
      </template>
      <template #publish_at="{ row }">
        <span>{{ row.publish_at || row.publish_time || '--' }}</span>
      </template>

      <template #create_at="{ row }">
        <span>{{ row.create_at || row.created || '--' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at || row.modified || '--' }}</span>
      </template>
      <template #operate="{ row }">
        <a-button id="noticeDetail" @click="detail(row)" type="text" :disabled="!btnPermission[61005]">查看</a-button>
        <a-button id="noticeWithdraw" @click="noticeWithdraw(row)" type="text" :disabled="!canWithdraw(row.notice_status || row.status) || !btnPermission[61002]">撤回</a-button>
        <a-button id="noticeSend" @click="beforeSend(row)" type="text" :disabled="!canPublish(row.notice_status || row.status) || !btnPermission[61002]">发布</a-button>
        <a-button id="noticeEdit" @click="edit(row)" type="text" :disabled="!canEdit(row.notice_status || row.status) || !btnPermission[61003]">编辑</a-button>
        <a-button id="noticeDel" @click="del(row)" type="text" :disabled="!canDelete(row.notice_status || row.status) || !btnPermission[61004]">删除</a-button>
      </template>
    </BaseTable>

    <!-- 确认弹窗 -->
    <a-modal :zIndex="10000" v-model:open="modalData.isShow" :title="modalData.title">
      <div class="modalContent">{{ modalData.content }}</div>
      <template #footer>
        <a-button v-if="modalData.isConfirmBtn" :danger="modalData.okType === 'danger'" type="primary" style="margin-right: 10px" @click="modalData.okFn">{{ modalData.confirmBtnText }}</a-button>
        <a-button v-if="modalData.isCancelBtn" @click="modalData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <!-- 新增 编辑 -->
    <a-drawer
      v-model:open="drawerVisible"
      @afterVisibleChange="formRef?.clearValidate()"
      width="50vw"
      :title="drawerStatus === 1 ? '新建通知' : '编辑通知'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <LoadingOutlined v-show="drawerLoading" class="loadingIcon" />
      <a-form v-if="!drawerLoading" ref="formRef" :model="formData">
        <a-form-item label="通知编码" v-if="drawerStatus == 2" name="code">
          <a-input id="code" disabled v-model:value="formData.code" placeholder="通知编码"></a-input>
        </a-form-item>

        <a-form-item
          label="通知标题"
          name="title"
          :rules="[
            { required: true, message: '请输入通知标题' },
            { max: 200, message: '输入内容不可超过200字符' },
          ]"
        >
          <a-input id="title" v-model:value="formData.title" placeholder="请输入通知标题"></a-input>
        </a-form-item>
        <a-form-item
          label="通知内容"
          name="content"
          :rules="[
            { required: true, message: '请输入通知内容' },
            { max: 5000, message: '输入内容不可超过5000字符' },
          ]"
        >
          <a-textarea id="content" v-model:value="formData.content" placeholder="请输入通知内容" :rows="4" />
        </a-form-item>
        <a-form-item label="通知类型" name="notice_type">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            id="notice_type"
            v-model:value="formData.notice_type"
            disabled
            class="w240"
            :options="noticeTypeOption"
            placeholder="请选择通知类型"
          ></a-select>
        </a-form-item>
        <a-form-item label="通知范围" name="scope">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            id="scope"
            v-model:value="formData.scope"
            disabled
            class="w240"
            :options="scopeOption"
            placeholder="请选择通知范围"
          ></a-select>
        </a-form-item>
        <div style="display: flex; gap: 12px; align-items: center">
          <a-form-item label="发布计划" name="publish_type" :rules="[{ required: true, message: '请选择发布计划' }]">
            <a-radio-group id="publishType" @change="formData.scheduled_publish_at = null" v-model:value="formData.publish_type">
              <a-radio :id="`publishType${item.value}`" v-for="(item, index) in publishTypeOption" :key="index" :value="item.value">{{ item.label }}</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="计划发布时间" name="scheduled_publish_at">
            <a-date-picker
              @change="checkTime"
              :disabled-date="disabledDate"
              id="planPublishTime"
              :disabled="formData.publish_type == 1"
              :valueFormat="'YYYY-MM-DD HH:mm'"
              format="YYYY-MM-DD HH:mm"
              :show-time="{ format: 'HH:mm' }"
              v-model:value="formData.scheduled_publish_at"
              placeholder="请选择计划发布时间"
            />
          </a-form-item>
        </div>
        <a-form-item label="过期时间" name="expired_at" :rules="[{ required: true, message: '请现在过期时间' }]">
          <a-date-picker
            :disabled-date="(current) => disabledDate2(current, formData.scheduled_publish_at)"
            id="expTime"
            class="w240"
            :valueFormat="'YYYY-MM-DD HH:mm'"
            format="YYYY-MM-DD HH:mm"
            :show-time="{ format: 'HH:mm' }"
            v-model:value="formData.expired_at"
            placeholder="请选择过期时间"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button
          id="noticeFormComfirmAndSend"
          style="margin-right: 10px"
          type="primary"
          @click="
            () => {
              formData.save_type = 2
              drawerComfirm()
            }
          "
        >
          保存并发布
        </a-button>
        <a-button
          id="noticeFormComfirmAndSave"
          style="margin-right: 10px"
          @click="
            () => {
              formData.save_type = 1
              drawerComfirm()
            }
          "
        >
          保存草稿
        </a-button>
        <a-button id="noticeFormCancel" @click="drawerVisible = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
  </div>
</template>
<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import Form from '@/components/Form.vue'
import { onMounted, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { BatchPublishNotify, deleteNotification, DetailByEdit, GetnoticeList, AddNotification, UpdateNotification, revocation, GetDropsSource, SaveAndPublish, Publish } from '@/servers/Notice'
// import { GetEnum } from '@/servers/Common'

import BaseTable from '@/components/BaseTable/index.vue'
import { PageType } from '@/common/enum'
import eventBus from '@/utils/eventBus'
import { usePermission } from '@/hook/usePermission'
import DetailDrawer from './components/DetailDrawer.vue'

const tableRef = ref()
const formRef = ref()

const noticeTypeOption = ref([{ label: '系统通知', value: 1 }])
const scopeOption = ref([{ label: '全员', value: 1 }])
const publishTypeOption = ref([
  { label: '立即发布', value: 1 },
  { label: '定时发布', value: 2 },
])

const { btnPermission } = usePermission()

const getListFn = ref(GetnoticeList)

// 状态标准化函数：将字符串状态转换为数字状态
const normalizeStatus = (status) => {
  if (typeof status === 'string') {
    switch (status) {
      case '草稿':
        return 1
      case '待发布':
        return 2
      case '已发布':
        return 3
      default:
        return 0
    }
  }
  return typeof status === 'number' ? status : 0
}

// 状态显示函数：兼容新旧格式
const getStatusDisplay = (status) => {
  if (typeof status === 'string') {
    return status || '--'
  }
  if (typeof status === 'number') {
    switch (status) {
      case 1:
        return '草稿'
      case 2:
        return '待发布'
      case 3:
        return '已发布'
      default:
        return '--'
    }
  }
  return '--'
}

// 各种操作的状态判断函数
const canWithdraw = (status) => normalizeStatus(status) === 2 // 待发布可撤回
const canPublish = (status) => [1, 2].includes(normalizeStatus(status)) // 草稿和待发布可发布
const canEdit = (status) => normalizeStatus(status) === 1 // 草稿可编辑
const canDelete = (status) => normalizeStatus(status) === 1 // 草稿可删除

const formArr: any = ref([
  {
    label: '搜索编码',
    value: null,
    type: 'input',
    key: 'no', // 修改为新API的字段名
  },
  {
    label: '搜索标题',
    value: null,
    type: 'input',
    key: 'title',
  },
  {
    label: '搜索内容',
    value: null,
    type: 'input',
    key: 'content',
  },
  {
    label: '通知范围',
    value: null,
    type: 'select',
    key: 'scope',
    options: [], // 从GetDropsSource接口获取
  },
  {
    label: '发布时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'publish_at',
    formKeys: ['publish_start_at', 'publish_end_at'],
    placeholder: ['发布开始时间', '发布结束时间'],
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'create_at',
    formKeys: ['create_start_at', 'create_end_at'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'update_at',
    formKeys: ['update_start_at', 'update_end_at'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
  {
    label: '状态',
    value: null,
    key: 'notice_status_list',
    type: '',
    isShow: true,
    isQuicks: true,
    multiple: false, // 改为单选，因为新API只支持单个状态值
    quickNotFull: true,
    options: [], // 初始化选项数组
  },
])
// 确认框数据
const modalData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  okType: 'primary',
  title: '',
  content: '',
  okFn: () => {
    modalData.isShow = false
  },
})
// 查看
const detailDrawerRef = ref<any>(null)
// 新增 编辑
const drawerLoading = ref(false)
const drawerVisible = ref(false)
const drawerStatus = ref(1)
const formData = ref<any>({
  id: '',
  save_type: null,
  title: null,
  content: null,
  notice_type: null,
  scope: null,
  publish_type: null,
  scheduled_publish_at: null,
  expired_at: null,
})
const oldStatus = ref<any>(null)

const search = () => tableRef.value.search()

onMounted(() => {
  initScreening() // 先初始化筛选配置
  search()
  // getEnum()     // 暂时注释掉，因为它会覆盖 getSelect 的数据
  getSelect() // 最后获取下拉框数据
})
onActivated(() => {})
const checkTime = () => {
  const time1 = formData.value.scheduled_publish_at
  const time2 = formData.value.expired_at
  if (time1 && time2) {
    if (time1 > time2) {
      message.error('计划发布时间不可在过期时间之后')
      formData.value.expired_at = null
    }
  }
}
const disabledDate = (current) => {
  if (!current) return false
  const currentDate = new Date(current)
  const now = new Date()
  now.setHours(0, 0, 0, 1)
  return currentDate.getTime() < now.getTime()
}

const disabledDate2 = (current, A = null) => {
  if (!current) return false
  // console.log(A)
  // 如果 A 存在，则解析为 Date，否则使用当前时间
  const referenceDate = A ? new Date(A) : new Date()
  referenceDate.setHours(0, 0, 0, 1) // 重置为当天的开始时间

  const currentDate = new Date(current)
  return currentDate.getTime() < referenceDate.getTime()
}

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.NOTICE_MANAGE) {
    const arr: any[] = []
    obj.NOTICE_MANAGE.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          // 保留原有的 options 数据
          const newItem = { ...y, isShow: x.isShow }
          arr.push(newItem)
        }
      })
    })
    formArr.value = arr
    console.log('initScreening 完成后的 formArr:', formArr.value)
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

// 新增
const increase = () => {
  drawerLoading.value = false
  formData.value = {
    id: '',
    save_type: null,
    title: null,
    content: null,
    notice_type: 1,
    scope: 1,
    publish_type: 1,
    scheduled_publish_at: null,
    expired_at: null,
  }
  drawerStatus.value = 1
  drawerVisible.value = true
}
// 详情
const detail = (item) => {
  detailDrawerRef.value.open(item.id, btnPermission.value[61005])
}

// 获取下拉选项
const getSelect = () => {
  // 从GetDropsSource接口获取所有下拉框数据
  GetDropsSource()
    .then((res) => {
      console.log('GetDropsSource响应数据:', res.data)

      // 直接修改现有的 formArr 中的对象，避免被其他函数覆盖
      formArr.value.forEach((item) => {
        // 状态下拉框
        if (item.key === 'notice_status_list') {
          if (res.data && res.data.status) {
            const statusOptions = Object.entries(res.data.status).map(([key, value]) => ({
              label: value,
              value: key,
            }))
            item.options = statusOptions
            console.log('状态下拉框选项已设置:', statusOptions)
            console.log('状态字段设置后:', item)
          } else {
            console.warn('状态数据不存在:', res.data)
          }
        }

        // 通知范围下拉框
        if (item.key === 'scope') {
          if (res.data && res.data.scopes) {
            const scopeOptions = Object.entries(res.data.scopes).map(([key, value]) => ({
              label: value,
              value: key,
            }))
            item.options = scopeOptions
            console.log('范围下拉框选项已设置:', scopeOptions)
            console.log('范围字段设置后:', item)
          }
        }

        // 通知类型下拉框
        if (item.key === 'notice_type') {
          if (res.data && res.data.types) {
            const typeOptions = Object.entries(res.data.types).map(([key, value]) => ({
              label: value,
              value: key,
            }))
            item.options = typeOptions
            console.log('类型下拉框选项已设置:', typeOptions)
          }
        }

        // 发布类型下拉框
        if (item.key === 'publish_type') {
          if (res.data && res.data.publish_types) {
            const publishTypeOptions = Object.entries(res.data.publish_types).map(([key, value]) => ({
              label: value,
              value: key,
            }))
            item.options = publishTypeOptions
            console.log('发布类型下拉框选项已设置:', publishTypeOptions)
          }
        }
      })

      // 检查设置结果
      console.log('设置完成后的 formArr:', formArr.value)
      const statusField = formArr.value.find((item) => item.key === 'notice_status_list')
      const scopeField = formArr.value.find((item) => item.key === 'scope')
      console.log('最终状态字段配置:', statusField)
      console.log('最终范围字段配置:', scopeField)
    })
    .catch((error) => {
      console.warn('获取下拉框数据失败，使用默认数据:', error)
      // 失败时直接设置默认数据
      formArr.value.forEach((item) => {
        if (item.key === 'notice_status_list') {
          item.options = [
            { label: '草稿', value: '10' },
            { label: '待发布', value: '20' },
            { label: '已发布', value: '30' },
          ]
          console.log('使用默认状态选项:', item.options)
        }

        if (item.key === 'scope') {
          item.options = [{ label: '全员', value: '1' }]
          console.log('使用默认范围选项:', item.options)
        }
      })
    })
}

// 编辑
const edit = (item) => {
  getNotifyInfo(item.id)
  oldStatus.value = Number(item.status)
  console.log(oldStatus.value, 'oldStatus')

  drawerLoading.value = false
  drawerStatus.value = 2
  drawerVisible.value = true
}

const getNotifyInfo = async (id) => {
  DetailByEdit({ id })
    .then((res) => {
      console.log('编辑通知数据:', res)
      const data = res.data

      // 处理通知范围：支持字符串和数字格式
      let scopeValue = 1 // 默认为全员
      if (data.scope === '全员' || data.scope === 1) {
        scopeValue = 1
      }

      // 处理发布类型：支持字符串和数字格式
      let publishTypeValue = 1 // 默认为立即发布
      if (data.publish_type === '立即发布' || data.publish_type === 1) {
        publishTypeValue = 1
      } else if (data.publish_type === '定时发布' || data.publish_type === 2) {
        publishTypeValue = 2
      }

      // 设置表单数据，确保必填字段有默认值
      formData.value = {
        ...data,
        // 确保通知ID存在（用于判断是编辑还是新增）
        id: data.id || id,
        // 确保通知类型为系统通知（固定值）
        notice_type: 1,
        // 确保通知范围有正确的值
        scope: scopeValue,
        // 确保发布类型有正确的值
        publish_type: publishTypeValue,
      }

      // console.log('处理后的表单数据:', formData.value)
      // console.log('表单数据中的ID:', formData.value.id, '类型:', typeof formData.value.id)
    })
    .catch((error) => {
      console.error('获取通知详情失败:', error)
    })
}
// 新增 编辑提交
const drawerComfirm = async () => {
  try {
    await formRef.value?.validateFields()
    const params = JSON.parse(JSON.stringify(formData.value))

    // 根据保存类型选择不同的接口
    let postFunc: any = null
    let successMessage = ''

    if (formData.value.save_type === 2) {
      // 保存并发布 - 使用 SaveAndPublish 接口
      postFunc = SaveAndPublish
      successMessage = '保存并发布成功'
      // 如果没有id，设置为0
      if (!params.id) {
        params.id = 0
      }
    } else {
      // 保存草稿 - 使用原有的新增/编辑接口
      // console.log('保存草稿模式 - formData.value.id:', formData.value.id, '类型:', typeof formData.value.id)
      if (formData.value.id) {
        postFunc = UpdateNotification
        successMessage = '编辑成功'
      } else {
        postFunc = AddNotification
        successMessage = '新增成功'

        // 新增时删除id字段
        if (!params.id) {
          delete params.id
        }
      }
    }

    postFunc(params)
      .then(() => {
        if (formData.value.save_type === 1) {
          eventBus.emit('getNotice')
        }
        message.success(successMessage)
        drawerVisible.value = false
        search()
      })
      .catch((err) => {
        console.log('提交失败:', err)
        // message.error('操作失败，请重试')
      })
  } catch (errorInfo) {
    console.log('表单验证失败:', errorInfo)
  }
}
// 删除
const del = (item) => {
  modalData.title = '删除通知'
  modalData.content = `此操作不可恢复，确定要删除该通知吗？`
  modalData.confirmBtnText = '确定'
  modalData.isCancelBtn = true
  modalData.okType = 'danger'
  modalData.okFn = () => {
    deleteNotification({ id: item.id })
      .then(() => {
        modalData.isShow = false
        message.success('删除成功')
        search()
      })
      .catch(() => {
        modalData.isShow = false
      })
  }
  modalData.isShow = true
}
// 批量发布
const batchSend = (obj, type) => {
  BatchPublishNotify(obj)
    .then(() => {
      eventBus.emit('getNotice')
      message.success(`${type === 1 ? '' : '批量'}发布成功`)
      search()
      modalData.isShow = false
    })
    .catch((err) => {
      console.log('发布失败:', err)
      // message.error('发布失败，请重试')
    })
}

// 单个发布 - 使用新的 Publish 接口
const publishSingle = (item) => {
  Publish({ id: item.id })
    .then(() => {
      eventBus.emit('getNotice')
      message.success('发布成功')
      search()
    })
    .catch((err) => {
      console.log('发布失败:', err)
      // message.error('发布失败，请重试')
    })
}

// 批量发布
const beforeBatchSend = () => {
  if (tableRef.value.checkItemsArr.length === 0) {
    message.info('请勾选需要的通知')
  } else {
    const arr = tableRef.value.checkItemsArr.map((obj) => obj.id)
    batchSend({ notice_ids: arr }, 2)
  }
}

// 单个发布
const beforeSend = (item) => {
  publishSingle(item)
}

// 撤回通知 - 使用新的 Revocation 接口
const noticeWithdraw = (item) => {
  revocation({ id: item.id })
    .then(() => {
      message.success('撤回成功')
      search()
    })
    .catch((err) => {
      console.log('撤回失败:', err)
      // message.error('撤回失败，请重试')
    })
}

// const enumData = ref<any>([])
// 获取所有枚举选项
// const getEnum = () => {
//   GetEnum()
//     .then((res) => {
//       enumData.value = res.data
//       formArr.value.forEach((item) => {
//         if (item.key == 'notice_status_list') {
//           item.options = enumData.value?.notice?.notice_status || []
//         }
//         if (item.key == 'scope') {
//           item.options = enumData.value?.notice?.scope || []
//         }
//       })
//     })
//     .catch((error) => {
//       console.warn('获取枚举选项失败:', error)
//       // 设置默认值
//       console.log('// 获取所有枚举选项的设置默认值5');

//       formArr.value.forEach((item) => {
//         if (item.key == 'notice_status_list') {
//           item.options = []
//         }
//         if (item.key == 'scope') {
//           item.options = []
//         }
//       })
//     })
// }
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    margin: 8px 0;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: 15px;

    .batchBtn {
      margin-left: 10px;
    }
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 32px;

      .tag {
        padding: 0 10px;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 10px;
      }
    }
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 120px;
    min-width: 120px;
    margin-right: 30px;

    label {
      font-size: 12px;

      &::after {
        display: none !important;
      }
    }
  }
}

.w240 {
  width: 240px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.loadingIcon {
  font-size: 30px;
  color: #1890ff;
}

.TagFilter {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;

  .item {
    margin-right: 20px;
  }
}
</style>
