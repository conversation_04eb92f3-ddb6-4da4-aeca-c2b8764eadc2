<template>
  <a-drawer
    :footer="logVisble ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看用户"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <template #extra>
      <a-button @click="changeLogVisible" v-if="btnPermission[21204]">日志</a-button>
    </template>
    <div class="detailAllBox">
      <a-spin v-show="detailloading" />
      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>
          <a-collapse-panel key="1" :header="`账号：${target.account_id}`" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">账号ID</p>
                <p class="value">{{ target.account_id || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">用户名</p>
                <p class="value">{{ target.user_name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">别名</p>
                <p class="value">{{ target.account_name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">真实姓名</p>
                <p class="value">{{ target.real_name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">工号</p>
                <p class="value">{{ target.job_id || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">用户编号</p>
                <p class="value">{{ target.user_id || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">{{ getCompanyLabelForTarget() }}</p>
                <p class="value">{{ getCompanyValueForTarget() }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8" v-if="shouldShowBothCompanyAndUnit()">
                <p class="label">所属企业</p>
                <p class="value">{{ target.company_name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">所在部门</p>
                <p class="value">{{ target.department_name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">岗位</p>
                <p class="value">{{ target.position_name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">直接上级</p>
                <p class="value">{{ target.leader_name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">角色</p>
                <p class="value">{{ target.role_names && target.role_names.length > 0 ? target.role_names.join(', ') : '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">状态</p>
                <p class="value">{{ target.status == 0 ? '停用' : '启用' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">账号来源</p>
                <p class="value">{{ target.account_source_format || '--' }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>

          <!-- 部门信息面板 -->
          <a-collapse-panel key="2" header="部门信息" :style="customStyle" v-if="target.department && target.department.length > 0">
            <div v-for="(dept, index) in target.department" :key="dept.id" class="department-item">
              <h4 v-if="target.department.length > 1">部门 {{ index + 1 }}</h4>
              <a-row :gutter="16">
                <a-col class="gutter-row" :span="8">
                  <p class="label">部门名称</p>
                  <p class="value">{{ dept.name || '--' }}</p>
                </a-col>
                <a-col class="gutter-row" :span="16">
                  <p class="label">完整路径</p>
                  <p class="value">{{ dept.full_name || '--' }}</p>
                </a-col>
                <a-col class="gutter-row" :span="8">
                  <p class="label">是否主要部门</p>
                  <p class="value">{{ dept.is_main_format || '--' }}</p>
                </a-col>
              </a-row>
              <a-divider v-if="index < target.department.length - 1" />
            </div>
          </a-collapse-panel>

          <!-- 联系方式面板 -->
          <a-collapse-panel key="3" header="联系方式" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="12">
                <p class="label">手机号码</p>
                <p class="value">
                  <span v-if="target.phones && target.phones.length > 0">
                    {{ target.phones.join(', ') }}
                  </span>
                  <span v-else>--</span>
                </p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">邮箱地址</p>
                <p class="value">
                  <span v-if="target.emails && target.emails.length > 0">
                    {{ target.emails.join(', ') }}
                  </span>
                  <span v-else>--</span>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>

          <a-collapse-panel key="4" header="其他" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改时间</p>
                <p class="value">{{ target.update_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target.update_user_real_name }}</div>
                      <div v-show="target.update_user_scope == 1">所属公司：{{ target.update_user_company_name ? target.update_user_company_name : '--' }}</div>
                      <div v-show="target.update_user_scope != 1">所属客户：{{ target.update_user_customer_name ? target.update_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ target.update_user_department ? target.update_user_department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ target.update_user_jobtitlename ? target.update_user_jobtitlename : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target.update_user_real_name ? target.update_user_real_name : '--' }}</span>
                      <span v-if="target.update_user_department || target.update_user_jobtitlename" class="detailValueDescription">
                        （
                        <span v-if="target.update_user_jobtitlename">{{ target.update_user_jobtitlename }}&nbsp;|&nbsp;</span>
                        <span v-if="target.update_user_department">
                          {{ target.update_user_department.length > 10 ? target.update_user_department.slice(0, 10) + '...' : target.update_user_department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">来源</p>
                <p class="value">{{ target.source_type ? formatOptionLabel(target.source_type, sourceOption) : '--' }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
      <LogDrawer ref="LogDrawerRef" class="log" v-if="!detailloading && appStore.isOpenLog" />
    </div>
  </a-drawer>
  <!-- 日志 -->
</template>

<script lang="ts" setup>
import { sourceOption } from '@/common/options'
import { formatOptionLabel } from '@/utils'
import { GetUserInfo } from '@/servers/UserManagerNew'
import { DoubleRightOutlined } from '@ant-design/icons-vue'
import useAppStore from '@/store/modules/app'
import LogDrawer from './LogDrawer.vue'

const activeKey = ref(['1', '2', '3'])
const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'

const appStore = useAppStore()

const { btnPermission } = usePermission()
const LogDrawerRef = ref()
const detailloading = ref(false)
const detailVisible = ref(false)
const logVisble = ref(false)
const target = ref<any>({})
const options = ref({})
const open = (row, selectMap) => {
  console.log('open', row, selectMap)
  target.value = null
  detailVisible.value = true
  options.value = selectMap
  detailloading.value = true

  // 使用GetUserInfo接口获取用户详情
  // 尝试使用不同的ID字段，优先使用account_id，然后是id
  const userId = row.account_id || row.id
  console.log('查看用户详情，使用ID:', userId, '原始row数据:', row)

  GetUserInfo({
    umcAccountId: userId,
  })
    .then((res) => {
      if (res.data) {
        target.value = res.data
        detailloading.value = false
        nextTick(() => {
          LogDrawerRef.value?.open(target.value)
        })
      } else {
        detailloading.value = false
      }
    })
    .catch(() => {
      detailloading.value = false
    })
}

// // 查看日志
// const looklog = () => {
//   LogDrawerRef.value.open(target.value.id)
// }

const changeLogVisible = () => {
  appStore.changeLogOpenVisible(appStore.isOpenLog ? 0 : 1)
  if (appStore.isOpenLog) {
    nextTick(() => {
      LogDrawerRef.value?.open(target.value)
    })
  }
}

// 获取公司/单位标签（优先显示单位）
const getCompanyLabelForTarget = () => {
  if (!target.value) return '所属企业'
  // 如果有单位信息，显示"所属单位"
  if (target.value.unit_name) {
    return '所属单位'
  }
  // 否则显示"所属企业"
  return '所属企业'
}

// 获取公司/单位值（优先显示单位）
const getCompanyValueForTarget = () => {
  if (!target.value) return '--'
  // 优先显示单位信息
  if (target.value.unit_name) {
    return target.value.unit_name || '--'
  }
  // 没有单位信息时显示企业信息
  return target.value.company_name || '--'
}

// 判断是否需要同时显示企业和单位信息
const shouldShowBothCompanyAndUnit = () => {
  if (!target.value) return false
  // 只有当有单位信息且企业信息与单位信息不同时，才显示企业信息
  return target.value.unit_name && target.value.company_name && target.value.unit_name !== target.value.company_name
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.department-item {
  margin-bottom: 16px;

  h4 {
    margin-bottom: 12px;
    font-weight: 500;
    color: #1890ff;
  }
}
</style>
