interface FormItem {
  label: string
  value?: any
  type: string
  key: string
  width?: number
  isShow?: boolean
  multiple?: boolean
  options?: any[]
  fieldNames?: { label: string; value: string; children: string }
  formKeys?: string[]
  placeholder?: string[]
  valueFormat?: string
  mode?: string
  onChange?: (value: any) => void
}

export const formArrAll: FormItem[] = [
  {
    label: '供应商商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'supplier_product_number',
    isShow: true,
  },
  {
    label: '平台商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'product_number',
    isShow: true,
  },
  {
    label: '商品名称(中)',
    value: null,
    type: 'input',
    key: 'product_name',
    isShow: true,
  },
  {
    label: '商品名称(英)',
    value: null,
    type: 'input',
    key: 'product_name_en',
    isShow: false,
  },
  {
    label: '是否白牌',
    value: null,
    type: 'select',
    key: 'is_white_brand',
    multiple: false,
    options: [
      {
        value: true,
        label: '是',
      },
      {
        value: false,
        label: '否',
      },
    ],
    isShow: true,
    onChange: undefined, // 将在运行时设置联动逻辑
  },
  {
    label: '商品品牌',
    value: [],
    type: 'select',
    key: 'brand_id_list',
    multiple: true,
    isShow: true,
    mode: 'multiple',
    options: [],
  },
  {
    label: '商品类目',
    value: [],
    type: 'cascader',
    key: 'category_id_list',
    multiple: true,
    options: [],
    fieldNames: { label: 'label', value: 'value', children: 'children' },
    isShow: true,
  },
  {
    label: '申报采购单价',
    type: 'range-input',
    key: 'declared_purchase_tax_price',
    formKeys: ['min_declared_purchase_tax_price', 'max_declared_purchase_tax_price'],
    value: [],
    placeholder: ['申报采购单价', '申报采购单价'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '议定采购单价',
    type: 'range-input',
    key: 'agreed_purchase_price',
    formKeys: ['min_agreed_purchase_tax_price', 'max_agreed_purchase_tax_price'],
    value: [],
    placeholder: ['议定采购单价', '议定采购单价'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '供货价',
    type: 'range-input',
    key: 'store_supply_price',
    formKeys: ['min_store_supply_price', 'max_store_supply_price'],
    value: [],
    placeholder: ['供货价', '供货价'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '首批新品采购量',
    type: 'range-input',
    key: 'first_batch_quantity',
    formKeys: ['min_first_batch_quantity', 'max_first_batch_quantity'],
    value: [],
    placeholder: ['首批新品采购量', '首批新品采购量'],
    valueFormat: '0.00',
    width: 110,
    isShow: false,
  },
  // {
  //   label: '运费',
  //   type: 'range-input',
  //   key: 'shipping_fee',
  //   formKeys: ['min_shipping_fee', 'max_shipping_fee'],
  //   value: [],
  //   placeholder: ['运费', '运费'],
  //   valueFormat: '0.00',
  //   isShow: false,
  // },
  {
    label: '发货时间',
    type: 'range-picker',
    key: 'shipment_time',
    formKeys: ['min_shipment_time', 'max_shipment_time'],
    placeholder: ['发货时间', '发货时间'],
    isShow: false,
  },
  {
    label: '预计交期',
    type: 'range-picker',
    key: 'expected_delivery_date',
    formKeys: ['min_expected_delivery_date', 'max_expected_delivery_date'],
    placeholder: ['预计交期', '预计交期'],
    isShow: false,
  },
  {
    label: '提审人',
    value: null,
    type: 'select',
    key: 'submit_audit_id',
    isShow: false,
    options: [],
  },
  {
    label: '提审时间',
    type: 'range-picker',
    key: 'submit_audit_at',
    formKeys: ['min_submit_audit_at', 'max_submit_audit_at'],
    placeholder: ['提审时间', '提审时间'],
    isShow: true,
  },
  {
    label: '审核状态',
    value: null,
    type: 'select',
    key: 'status',
    options: [],
    isShow: true,
  },
  {
    label: '审核时间',
    type: 'range-picker',
    key: 'audit_time',
    formKeys: ['min_audit_time', 'max_audit_time'],
    placeholder: ['审核时间', '审核时间'],
    isShow: false,
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'create_at',
    formKeys: ['create_at_start', 'create_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
    valueFormat: 'YYYY-MM-DD',
    isShow: false,
  },
  {
    label: '供应商编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'supplier_number',
    isShow: true,
  },
  {
    label: '供应商名称',
    value: null,
    type: 'input',
    key: 'supplier_name',
    isShow: true,
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'modified_at',
    formKeys: ['modified_at_start', 'modified_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
    valueFormat: 'YYYY-MM-DD',
    isShow: false,
  },
]

export const formArrAllWaitAudit: FormItem[] = [
  {
    label: '供应商商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'supplier_product_number',
    isShow: true,
  },
  {
    label: '平台商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'product_number',
    isShow: true,
  },
  {
    label: '商品名称(中)',
    value: null,
    type: 'input',
    key: 'product_name',
    isShow: true,
  },
  {
    label: '商品名称(英)',
    value: null,
    type: 'input',
    key: 'product_name_en',
    isShow: false,
  },
  {
    label: '是否白牌',
    value: null,
    type: 'select',
    key: 'is_white_brand',
    multiple: false,
    options: [
      {
        value: true,
        label: '是',
      },
      {
        value: false,
        label: '否',
      },
    ],
    isShow: true,
    onChange: undefined, // 将在运行时设置联动逻辑
  },
  {
    label: '商品品牌',
    value: [],
    type: 'select',
    key: 'brand_id_list',
    multiple: true,
    isShow: true,
    mode: 'multiple',
    options: [],
  },
  {
    label: '商品类目',
    value: [],
    type: 'cascader',
    key: 'category_id_list',
    multiple: true,
    options: [],
    fieldNames: { label: 'label', value: 'value', children: 'children' },
    isShow: true,
  },
  {
    label: '申报采购单价',
    type: 'range-input',
    key: 'declared_purchase_tax_price',
    formKeys: ['min_declared_purchase_tax_price', 'max_declared_purchase_tax_price'],
    value: [],
    placeholder: ['申报采购单价', '申报采购单价'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '议定采购单价',
    type: 'range-input',
    key: 'agreed_purchase_price',
    formKeys: ['min_agreed_purchase_tax_price', 'max_agreed_purchase_tax_price'],
    value: [],
    placeholder: ['议定采购单价', '议定采购单价'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '供货价',
    type: 'range-input',
    key: 'store_supply_price',
    formKeys: ['min_store_supply_price', 'max_store_supply_price'],
    value: [],
    placeholder: ['供货价', '供货价'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '首批新品采购量',
    type: 'range-input',
    key: 'first_batch_quantity',
    formKeys: ['min_first_batch_quantity', 'max_first_batch_quantity'],
    value: [],
    placeholder: ['首批新品采购量', '首批新品采购量'],
    valueFormat: '0.00',
    width: 110,
    isShow: false,
  },
  {
    label: '运费',
    type: 'range-input',
    key: 'shipping_fee',
    formKeys: ['min_shipping_fee', 'max_shipping_fee'],
    value: [],
    placeholder: ['运费', '运费'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '发货时间',
    type: 'range-picker',
    key: 'shipment_time',
    formKeys: ['min_shipment_time', 'max_shipment_time'],
    placeholder: ['发货时间', '发货时间'],
    isShow: false,
  },
  {
    label: '预计交期',
    type: 'range-picker',
    key: 'expected_delivery_date',
    formKeys: ['min_expected_delivery_date', 'max_expected_delivery_date'],
    placeholder: ['预计交期', '预计交期'],
    isShow: false,
  },
  {
    label: '提审人',
    value: null,
    type: 'select',
    key: 'submit_audit_id',
    isShow: false,
    options: [],
  },
  {
    label: '提审时间',
    type: 'range-picker',
    key: 'submit_audit_at',
    formKeys: ['min_submit_audit_at', 'max_submit_audit_at'],
    placeholder: ['提审时间', '提审时间'],
    isShow: true,
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'create_at',
    formKeys: ['create_at_start', 'create_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
    valueFormat: 'YYYY-MM-DD',
    isShow: false,
  },
  {
    label: '供应商编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'supplier_number',
    isShow: true,
  },
  {
    label: '供应商名称',
    value: null,
    type: 'input',
    key: 'supplier_name',
    isShow: true,
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'modified_at',
    formKeys: ['modified_at_start', 'modified_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
    valueFormat: 'YYYY-MM-DD',
    isShow: false,
  },
]

export const formArrAllAudited: FormItem[] = [
  {
    label: '供应商商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'supplier_product_number',
    isShow: true,
  },
  {
    label: '平台商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'product_number',
    isShow: true,
  },
  {
    label: '商品名称(中)',
    value: null,
    type: 'input',
    key: 'product_name',
    isShow: true,
  },
  {
    label: '商品名称(英)',
    value: null,
    type: 'input',
    key: 'product_name_en',
    isShow: false,
  },
  {
    label: '是否白牌',
    value: null,
    type: 'select',
    key: 'is_white_brand',
    multiple: false,
    options: [
      {
        value: true,
        label: '是',
      },
      {
        value: false,
        label: '否',
      },
    ],
    isShow: true,
    onChange: null, // 将在运行时设置联动逻辑
  },
  {
    label: '商品品牌',
    value: [],
    type: 'select',
    key: 'brand_id_list',
    multiple: true,
    isShow: true,
    mode: 'multiple',
    options: [],
  },
  {
    label: '商品类目',
    value: [],
    type: 'cascader',
    key: 'category_id_list',
    multiple: true,
    options: [],
    fieldNames: { label: 'label', value: 'value', children: 'children' },
    isShow: true,
  },
  {
    label: '申报采购单价',
    type: 'range-input',
    key: 'declared_purchase_tax_price',
    formKeys: ['min_declared_purchase_tax_price', 'max_declared_purchase_tax_price'],
    value: [],
    placeholder: ['申报采购单价', '申报采购单价'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '议定采购单价',
    type: 'range-input',
    key: 'agreed_purchase_price',
    formKeys: ['min_agreed_purchase_tax_price', 'max_agreed_purchase_tax_price'],
    value: [],
    placeholder: ['议定采购单价', '议定采购单价'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '供货价',
    type: 'range-input',
    key: 'store_supply_price',
    formKeys: ['min_store_supply_price', 'max_store_supply_price'],
    value: [],
    placeholder: ['供货价', '供货价'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '首批新品采购量',
    type: 'range-input',
    key: 'first_batch_quantity',
    formKeys: ['min_first_batch_quantity', 'max_first_batch_quantity'],
    value: [],
    placeholder: ['首批新品采购量', '首批新品采购量'],
    valueFormat: '0.00',
    width: 110,
    isShow: false,
  },
  {
    label: '运费',
    type: 'range-input',
    key: 'shipping_fee',
    formKeys: ['min_shipping_fee', 'max_shipping_fee'],
    value: [],
    placeholder: ['运费', '运费'],
    valueFormat: '0.00',
    isShow: false,
  },
  {
    label: '发货时间',
    type: 'range-picker',
    key: 'shipment_time',
    formKeys: ['min_shipment_time', 'max_shipment_time'],
    placeholder: ['发货时间', '发货时间'],
    isShow: false,
  },
  {
    label: '预计交期',
    type: 'range-picker',
    key: 'expected_delivery_date',
    formKeys: ['min_expected_delivery_date', 'max_expected_delivery_date'],
    placeholder: ['预计交期', '预计交期'],
    isShow: false,
  },
  {
    label: '提审人',
    value: null,
    type: 'select',
    key: 'submit_audit_id',
    isShow: false,
    options: [],
  },
  {
    label: '提审时间',
    type: 'range-picker',
    key: 'submit_audit_at',
    formKeys: ['min_submit_audit_at', 'max_submit_audit_at'],
    placeholder: ['提审时间', '提审时间'],
    isShow: true,
  },
  {
    label: '审核时间',
    type: 'range-picker',
    key: 'audit_time',
    formKeys: ['min_audit_time', 'max_audit_time'],
    placeholder: ['审核时间', '审核时间'],
    isShow: false,
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'create_at',
    formKeys: ['create_at_start', 'create_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
    valueFormat: 'YYYY-MM-DD',
    isShow: false,
  },
  {
    label: '供应商编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'supplier_number',
    isShow: true,
  },
  {
    label: '供应商名称',
    value: null,
    type: 'input',
    key: 'supplier_name',
    isShow: true,
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'modified_at',
    formKeys: ['modified_at_start', 'modified_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
    valueFormat: 'YYYY-MM-DD',
    isShow: false,
  },
]
