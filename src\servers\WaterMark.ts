// 水印接口 - 使用新的 /XY/Watermark/ 路径
import { request, requestXY } from './request'

// 获取水印配置信息
export const GetWatermarkInfo = (data) => {
  return requestXY({ url: '/Watermark/GetWatermarkInfo', data }, 'POST')
}

// 保存ui水印配置
export const SaveUIConfig = (data) => {
  return requestXY({ url: '/Watermark/SaveUIConfig', data }, 'POST')
}

// 获取系统界面水印
export const GetSystemWatermark = () => {
  return requestXY({ url: '/Watermark/GetSystemWatermark' }, 'GET')
}

// 保存预览水印配置
export const SaveReviewConfig = (data) => {
  return requestXY({ url: '/Watermark/SaveReviewConfig', data }, 'POST')
}

// 保存下载水印配置
export const SaveDownloadConfig = (data) => {
  return requestXY({ url: '/Watermark/SaveDownloadConfig', data }, 'POST')
}

// 获取日志 - 使用通用日志接口
export const GetOpLogInfos = (data) => {
  return requestXY({ url: '/Common/GetOpLogInfos', data: { ...data, page: 34 } }, 'POST')
}

// 预览水印
export const PreviewWatermark = (data) => {
  return requestXY({ url: '/Watermark/PreviewWatermark', data }, 'POST')
}

// === 以下接口暂时保留旧路径，因为新API中可能没有对应接口 ===

// 获取角色下拉框 - 保留旧路径，新API中未找到对应接口
export const GetRoleSelectOption = (data) => {
  return request({ url: '/api/Watermark/GetRoleSelectOption', data })
}
