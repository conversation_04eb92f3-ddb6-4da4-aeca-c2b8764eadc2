<template>
  <a-drawer
    :footer="logVisible ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看类目"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <div class="detail-form-container">
      <LoadingOutlined v-show="detailLoading" class="loadingIcon" />

      <a-form
        v-if="!detailLoading && target"
        layout="horizontal"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="drawer-title">基本信息</div>

          <!-- 类目编码 -->
          <div class="info-row">
            <div class="info-label">类目编码</div>
            <div class="info-content">{{ target.code || '--' }}</div>
          </div>

          <!-- 类目名称 -->
          <div class="info-row">
            <div class="info-label">类目名称</div>
            <div class="info-content">{{ target.detailLanguages[0].name || '--' }}</div>
          </div>

          <!-- 类目类型 -->
          <!-- <div class="info-row">
            <div class="info-label">类目类型</div>
            <div class="info-content">{{ formatCategoryType(target.type) }}</div>
          </div> -->

          <!-- 类目编号 -->
          <div class="info-row">
            <div class="info-label">类目编号</div>
            <div class="info-content">{{ target.number || '--' }}</div>
          </div>

          <!-- 描述 -->
          <div class="info-row">
            <div class="info-label">描述</div>
            <div class="info-content">{{ target.description || '--' }}</div>
          </div>

          <!-- 父级类目 -->
          <div class="info-row">
            <div class="info-label">父级类目</div>
            <div class="info-content">{{ target.parent_name || '--' }}</div>
          </div>

          <!-- 是否继承父级类目模板 -->
          <div class="info-row">
            <div class="info-label">是否继承父级类目模板</div>
            <div class="info-content">{{ target.is_inherit ? '是' : '否' }}</div>
          </div>

          <!-- 类目模板 -->
          <!-- <div class="info-row">
            <div class="info-label">类目模板</div>
            <div class="info-content">{{ target.category_template_name || '--' }}</div>
          </div> -->

          <!-- 状态 -->
          <div class="info-row">
            <div class="info-label">状态</div>
            <div class="info-content">{{ target.status === 1 ? '启用' : '停用' }}</div>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="drawer-title">其他信息</div>

          <div class="info-row-group">
            <div class="info-row">
              <div class="info-label">创建时间</div>
              <div class="info-content">{{ formatDateTime(target.create_at) || '--' }}</div>
            </div>
            <!-- <div class="info-row">
              <div class="info-label">创建人</div>
              <div class="info-content">
                {{ target.create_user_real_name || '--' }}
                <span v-if="target.create_user_department || target.create_user_jobtitlename" class="user-extra">
                  （{{ [target.create_user_department, target.create_user_jobtitlename].filter(Boolean).join('/') }}）
                </span>
              </div>
            </div> -->
          </div>

          <div class="info-row-group">
            <div class="info-row">
              <div class="info-label">最后修改时间</div>
              <div class="info-content">{{ formatDateTime(target.update_at) || '--' }}</div>
            </div>
            <!-- <div class="info-row">
              <div class="info-label">最后修改人</div>
              <div class="info-content">
                {{ target.update_user_real_name || '--' }}
                <span v-if="target.update_user_department || target.update_user_jobtitlename" class="user-extra">
                  （{{ [target.update_user_department, target.update_user_jobtitlename].filter(Boolean).join('/') }}）
                </span>
              </div>
            </div> -->
          </div>
        </div>
      </a-form>

      <log-drawer ref="logDrawerRef" class="log" v-if="!detailLoading && appStore.isOpenLog" />
    </div>
  </a-drawer>
</template>

    

<script lang="ts" setup>
import { ref, nextTick } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { GetCategoryDetail } from '@/servers/category'
import useAppStore from '@/store/modules/app'
// import LogDrawer from './LogDrawer.vue'

const appStore = useAppStore()

const logDrawerRef = ref()
const detailVisible = ref(false)
const detailLoading = ref(false)
const logVisible = ref(false)

const target = ref<any>(null)

// 打开详情抽屉
const open = async (id: string, hasLogPermission: boolean) => {
  target.value = null
  detailLoading.value = true
  detailVisible.value = true
  logVisible.value = hasLogPermission

  try {
    const res = await GetCategoryDetail({ id })
    target.value = res.data
    detailLoading.value = false
    nextTick(() => {
      if (hasLogPermission) {
        logDrawerRef.value?.open(target.value)
      }
    })
  } catch (error) {
    console.error('加载属性详情失败:', error)
    detailLoading.value = false
  }
}



// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '--'
  return dateStr // 保留完整的日期时间
}

// 格式化类目类型
const formatCategoryType = (type: number) => {
  switch (type) {
    case 1:
      return '成品';
    case 2:
      return '半成品';
    case 3:
      return '原材料';
    default:
      return '--';
  }
};
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.detail-form-container {
  position: relative;
  padding: 24px;
  background-color: #fff;
  min-height: 100%;
}

.loadingIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #1890ff;
  z-index: 10;
}

.form-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.drawer-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  min-height: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-row-group {
  display: flex;
  gap: 32px;
  margin-bottom: 16px;

  .info-row {
    flex: 1;
    margin-bottom: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  flex: 0 0 160px;
  font-weight: 500;
  color: #595959;
  line-height: 32px;
  text-align: right;
  padding-right: 16px;
}

.info-content {
  flex: 1;
  color: #262626;
  line-height: 32px;
  word-break: break-all;

  .user-extra {
    color: #8c8c8c;
    font-size: 12px;
  }
}

.log {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  border-left: 1px solid #f0f0f0;
}

// 响应式设计
@media (max-width: 1200px) {
  .info-row-group {
    flex-direction: column;
    gap: 16px;
  }

  .info-label {
    flex: 0 0 100px;
  }
}
</style>


<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #00000080;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #00000080;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}
</style>
