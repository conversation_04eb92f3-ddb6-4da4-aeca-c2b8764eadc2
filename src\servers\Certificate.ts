import { request, requestXY } from './request'

// 获取资质证书列表
export const GetList = (data) => request({ url: '/api/Certificate/GetList', data }, 'POST')
// 资质证书详情
export const GetCertificateDetail = (data) => request({ url: '/api/Certificate/GetDetail', data }, 'GET')
// 审核资质证书
export const AuditCertificate = (data) => request({ url: '/api/Certificate/Approval', data }, 'POST')
// 获取日志 - 使用通用日志接口
export const GetOpLogInfos = (data) => {
  return requestXY({ url: '/Common/GetOpLogInfos', data: { ...data, page: 53 } }, 'POST')
}
// 导出资质证书
export const ExportCertificate = (data) => request({ url: '/api/Certificate/Export', data, responseType: 'blob' })

// 获取文件列表
export const GetFileList = (data) => request({ url: '/api/Files/GetFileList', data }, 'POST')
