<template>
  <a-modal v-model:open="openModal" title="供应商审核" @ok="showModal" centered @cancel="handleCancel">
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSave" :loading="saveLoading" :disabled="localExamineInfo.audit_status == 30">{{ saveBtnText }}</a-button>
    </template>
    <a-form ref="formRef" :model="localExamineInfo" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }" :rules="rules">
      <a-row :gutter="16">
        <a-col :span="16">
          <a-form-item label="供应商名称">
            <span>{{ localExamineInfo.supplier_name }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="16">
          <a-form-item label="公司类型">
            <span>{{ localExamineInfo.company_type_string }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="16">
          <a-form-item label="主营类目">
            <div v-if="localExamineInfo.audit_main_categories_strings.length > 0 && localExamineInfo.audit_main_categories_strings">
              <span v-for="(item, index) in localExamineInfo.audit_main_categories_strings" :key="item">
                {{ item }}{{ index !== localExamineInfo.audit_main_categories_strings.length - 1 ? '，' : '' }}
              </span>
            </div>
            <div v-else>
              <span v-for="(item, index) in localExamineInfo.main_categories_strings" :key="item">{{ item }}{{ index !== localExamineInfo.main_categories_strings.length - 1 ? '，' : '' }}</span>
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="16">
          <a-form-item label="审核结果" name="approve_result">
            <a-select :options="approveResultList" placeholder="请选择审核结果" allow-clear v-model:value="localExamineInfo.approve_result" :disabled="localExamineInfo.audit_status == 30"></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="16">
          <a-form-item label="审核备注" name="approve_remark">
            <a-textarea :maxlength="200" show-count :rows="5" v-model:value="localExamineInfo.approve_remark" :disabled="localExamineInfo.audit_status == 30"></a-textarea>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue'
import { ref, onMounted, defineExpose, defineEmits } from 'vue'
import { SupplierAudit } from '@/servers/supplierSettlementApproval'

const emit = defineEmits(['handleClose', 'RedPoint'])
// 父组件传入的审核信息
const props = defineProps<{ ExamineInfo: any }>()
// 本地审核信息
const localExamineInfo = ref({ ...props.ExamineInfo })
// 是否显示弹窗
const openModal = ref(false)
// 表单
const formRef = ref()
// 保存loading
const saveLoading = ref(false)
// 后端给的审批状态枚举
// {
//   10: '草稿',
//   20: '待审核',
//   30: '待UMC审核',
//   90: '审核通过',
//   95: '审核不通过'
// }
const btnTextByEnum = {
  20: '提交平台认证',
  30: '平台审核中',
}
// 保存按钮的文字
const saveBtnText = computed(() => {
  // audit_type 入驻审核=1, 资料修改审核=2
  console.log((props.ExamineInfo?.audit_type === 1 && btnTextByEnum[localExamineInfo.value.audit_status] && localExamineInfo.value.approve_result) || '保存')
  return (props.ExamineInfo?.audit_type === 1 && localExamineInfo.value.approve_result && btnTextByEnum[localExamineInfo.value.audit_status]) || '保存'
})
// 审核结果列表
const approveResultList = ref([
  {
    label: '审核通过',
    value: true,
  },
  {
    label: '审核不通过',
    value: false,
  },
])
// 表单验证规则
const rules = computed(() => {
  return {
    approve_result: [{ required: localExamineInfo.value.audit_status != 30, message: '请选择审核结果', trigger: 'blur' }],
    approve_remark: localExamineInfo.value.approve_result === false ? [{ required: true, message: '请输入审核备注', trigger: 'blur' }] : [],
  }
})
// 显示弹窗
const showModal = () => {
  openModal.value = true
  if (localExamineInfo.value.audit_status == 30) {
    localExamineInfo.value.approve_result = true
  }
}
// 取消
const handleCancel = () => {
  console.log(876)
  formRef.value.resetFields()
  localExamineInfo.value.approve_result = null
  localExamineInfo.value.approve_remark = ''
  openModal.value = false
}
// 保存
const handleSave = () => {
  // 检验表单
  formRef.value
    .validate()
    .then(() => {
      saveLoading.value = true
      const params = {
        is_pass: localExamineInfo.value.approve_result,
        audit_opinion: localExamineInfo.value.approve_remark,
        supplier_id: localExamineInfo.value.id,
      }
      SupplierAudit(params)
        .then(() => {
          message.success('审核成功')
          handleCancel()
          emit('handleClose')
          emit('RedPoint')
        })
        .finally(() => {
          saveLoading.value = false
        })
    })
    .catch((error) => {
      if (error.errorFields) {
        const firstError = error.errorFields[0]
        switch (firstError.name[0]) {
          case 'approve_result':
            message.error('请选择审核结果')
            break
          case 'approve_remark':
            message.error('请输入审核备注')
            break
          default:
            message.error(firstError.errors[0])
        }
      }
    })
}
defineExpose({
  showModal,
})

onMounted(() => {})
// 监听父组件传入的审核信息，避免数据丢失
watch(
  () => props.ExamineInfo,
  (newVal) => {
    localExamineInfo.value = { ...newVal }
  },
  { immediate: true, deep: true },
)
</script>
<style scoped lang="scss">
:deep(.ant-input-textarea) {
  width: 360px;
}

:deep(.ant-input-textarea textarea) {
  resize: none;
}
</style>
